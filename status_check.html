<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Algorithm Status Check</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .status-section {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #3e3e42;
        }
        
        .status-item:last-child {
            border-bottom: none;
        }
        
        .status-label {
            font-weight: bold;
        }
        
        .status-value {
            font-family: monospace;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Layered DAG Algorithm Status Check</h1>
        
        <div class="status-section">
            <h3>System Status</h3>
            <div class="status-item">
                <span class="status-label">Browser:</span>
                <span class="status-value" id="browserInfo">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">JavaScript Support:</span>
                <span class="status-value" id="jsSupport">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">ES6 Features:</span>
                <span class="status-value" id="es6Support">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">Game.js Loading:</span>
                <span class="status-value" id="gameJsStatus">Checking...</span>
            </div>
        </div>
        
        <div class="status-section">
            <h3>Algorithm Functions</h3>
            <div class="status-item">
                <span class="status-label">generateDeterministicSolvableScenario:</span>
                <span class="status-value" id="mainAlgorithm">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">calculateLayeredScenarioSpec:</span>
                <span class="status-value" id="scenarioSpec">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">generateLayeredPortPlan:</span>
                <span class="status-value" id="portPlan">Checking...</span>
            </div>
            <div class="status-item">
                <span class="status-label">validateLayeredScenarioCompletely:</span>
                <span class="status-value" id="validation">Checking...</span>
            </div>
        </div>
        
        <div class="status-section">
            <h3>Quick Test</h3>
            <button onclick="runQuickTest()">Run Quick Test</button>
            <button onclick="testAllLevels()">Test All Levels</button>
            <button onclick="clearTestOutput()">Clear Output</button>
            
            <div class="test-output" id="testOutput">
                <div class="info">Ready to run tests...</div>
            </div>
        </div>
    </div>

    <script>
        // Check system status
        function checkSystemStatus() {
            // Browser info
            const browserInfo = navigator.userAgent.split(' ').slice(-2).join(' ');
            document.getElementById('browserInfo').textContent = browserInfo;
            document.getElementById('browserInfo').className = 'status-value success';
            
            // JavaScript support
            try {
                eval('const test = () => ({ es6: true });');
                document.getElementById('jsSupport').textContent = 'ES6 Supported';
                document.getElementById('jsSupport').className = 'status-value success';
            } catch (e) {
                document.getElementById('jsSupport').textContent = 'Limited Support';
                document.getElementById('jsSupport').className = 'status-value warning';
            }
            
            // ES6 features
            try {
                const testMap = new Map();
                const testSet = new Set();
                const testArrow = () => true;
                const testClass = class TestClass {};
                
                document.getElementById('es6Support').textContent = 'Full Support';
                document.getElementById('es6Support').className = 'status-value success';
            } catch (e) {
                document.getElementById('es6Support').textContent = 'Partial Support';
                document.getElementById('es6Support').className = 'status-value warning';
            }
        }
        
        // Load game.js and check functions
        function loadGameJS() {
            const script = document.createElement('script');
            script.src = 'game.js?v=layered-dag-algorithm-2025';
            script.onload = function() {
                document.getElementById('gameJsStatus').textContent = 'Loaded Successfully';
                document.getElementById('gameJsStatus').className = 'status-value success';
                
                checkAlgorithmFunctions();
            };
            script.onerror = function() {
                document.getElementById('gameJsStatus').textContent = 'Failed to Load';
                document.getElementById('gameJsStatus').className = 'status-value error';
            };
            
            document.head.appendChild(script);
        }
        
        // Check algorithm functions
        function checkAlgorithmFunctions() {
            const functions = [
                { name: 'generateDeterministicSolvableScenario', id: 'mainAlgorithm' },
                { name: 'calculateLayeredScenarioSpec', id: 'scenarioSpec' },
                { name: 'generateLayeredPortPlan', id: 'portPlan' },
                { name: 'validateLayeredScenarioCompletely', id: 'validation' }
            ];
            
            functions.forEach(func => {
                const element = document.getElementById(func.id);
                if (typeof window[func.name] === 'function') {
                    element.textContent = 'Available';
                    element.className = 'status-value success';
                } else {
                    element.textContent = 'Not Found';
                    element.className = 'status-value error';
                }
            });
        }
        
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const div = document.createElement('div');
            div.className = type;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearTestOutput() {
            document.getElementById('testOutput').innerHTML = '';
        }
        
        function runQuickTest() {
            clearTestOutput();
            log('Running quick algorithm test...', 'info');
            
            try {
                if (typeof generateDeterministicSolvableScenario !== 'function') {
                    log('ERROR: Main algorithm function not available', 'error');
                    return;
                }
                
                const testDifficulty = {
                    level: 2,
                    availableTypes: ['square', 'circle'],
                    availableColors: ['#ff5252', '#2196F3']
                };
                
                log('Generating scenario for level 2...', 'info');
                const scenario = generateDeterministicSolvableScenario(testDifficulty);
                
                if (scenario) {
                    log('SUCCESS: Scenario generated', 'success');
                    log('Start node: ' + (scenario.startNode ? 'YES' : 'NO'), 'info');
                    log('End node: ' + (scenario.endNode ? 'YES' : 'NO'), 'info');
                    log('Intermediate nodes: ' + (scenario.nodes ? scenario.nodes.length : 0), 'info');
                    log('Connections: ' + (scenario.guaranteedConnections ? scenario.guaranteedConnections.length : 0), 'info');
                } else {
                    log('ERROR: Scenario generation returned null', 'error');
                }
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        function testAllLevels() {
            clearTestOutput();
            log('Testing all levels...', 'info');
            
            try {
                if (typeof generateDeterministicSolvableScenario !== 'function') {
                    log('ERROR: Main algorithm function not available', 'error');
                    return;
                }
                
                for (let level = 1; level <= 5; level++) {
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4))
                    };
                    
                    try {
                        const scenario = generateDeterministicSolvableScenario(difficulty);
                        
                        if (scenario && scenario.startNode && scenario.endNode) {
                            log('Level ' + level + ': SUCCESS - ' + (scenario.nodes ? scenario.nodes.length : 0) + ' nodes', 'success');
                        } else {
                            log('Level ' + level + ': FAILED - Invalid scenario', 'error');
                        }
                    } catch (levelError) {
                        log('Level ' + level + ': ERROR - ' + levelError.message, 'error');
                    }
                }
                
                log('All levels test completed', 'info');
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        // Initialize on page load
        window.addEventListener('load', () => {
            checkSystemStatus();
            loadGameJS();
        });
        
        // Catch global errors
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
        });
    </script>
</body>
</html>
