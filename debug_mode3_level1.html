<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Mode 3 Level 1</title>
    <style>
        body { 
            font-family: monospace; 
            padding: 20px; 
            background: #1a1a1a; 
            color: #00ff00; 
        }
        .debug-section { 
            border: 1px solid #333; 
            margin: 15px 0; 
            padding: 20px; 
            background: #2a2a2a; 
        }
        .success { color: #44ff44; }
        .error { color: #ff4444; }
        .warning { color: #ffaa44; }
        .info { color: #4444ff; }
        .header { color: #ffffff; font-weight: bold; }
        .step { margin-left: 20px; color: #88cc88; }
        .analysis { background: #2a2a4a; padding: 10px; margin: 5px 0; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover { background: #444; }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #555;
            padding: 8px;
            text-align: left;
        }
        th { background: #333; }
    </style>
</head>
<body>
    <h1>🐛 Debug Mode 3 Level 1 Solvability</h1>
    
    <div class="debug-section">
        <button onclick="testCurrentImplementation()">测试当前实现</button>
        <button onclick="debugStepByStep()">逐步调试</button>
        <button onclick="testPortBalance()">检查端口平衡</button>
        <button onclick="fixAndTest()">修复并测试</button>
        <button onclick="clearResults()">清空结果</button>
    </div>
    
    <div id="results"></div>

    <script>
        class DebugNode {
            constructor(type) {
                this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
                this.type = type;
                this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
                this.inputPorts = [];
                this.outputPorts = [];
                this.x = 0;
                this.y = 0;
            }

            addInputPort(type, color) {
                const port = { 
                    id: `port_${Math.random().toString(36).substr(2, 9)}`, 
                    type, color, side: 'input' 
                };
                this.inputPorts.push(port);
                return port;
            }

            addOutputPort(type, color) {
                const port = { 
                    id: `port_${Math.random().toString(36).substr(2, 9)}`, 
                    type, color, side: 'output' 
                };
                this.outputPorts.push(port);
                return port;
            }

            getAllPorts() {
                return [...this.inputPorts, ...this.outputPorts];
            }

            moveTo(x, y) {
                this.x = x;
                this.y = y;
            }
        }

        // 模拟游戏状态
        const debugGameState = {
            nodes: [],
            temporaryNodes: [],
            connections: [],
            portTypes: ['square', 'diamond', 'triangle', 'circle'],
            portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
            infiniteMode: {
                isActive: true,
                currentWave: 1,
                adaptiveDifficulty: 1
            }
        };

        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `debug-section ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}</strong> - ${message}`;
            results.appendChild(div);
            console.log(message);
        }

        // 分析系统状态
        function analyzeSystemState() {
            const allNodes = [...debugGameState.nodes, ...debugGameState.temporaryNodes];
            const analysis = {
                totalNodes: allNodes.length,
                startNodes: allNodes.filter(n => n.type === 'start'),
                endNodes: allNodes.filter(n => n.type === 'end'),
                intermediateNodes: allNodes.filter(n => n.type === 'normal'),
                temporaryNodes: debugGameState.temporaryNodes,
                placedNodes: debugGameState.nodes
            };

            // 端口统计
            const portStats = new Map();
            allNodes.forEach(node => {
                node.getAllPorts().forEach(port => {
                    const key = `${port.type}-${port.color}`;
                    if (!portStats.has(key)) {
                        portStats.set(key, { input: 0, output: 0 });
                    }
                    if (port.side === 'input' || node.inputPorts.includes(port)) {
                        portStats.get(key).input++;
                    } else {
                        portStats.get(key).output++;
                    }
                });
            });

            analysis.portStats = portStats;
            return analysis;
        }

        // 检查端口平衡
        function checkPortBalance(analysis) {
            const imbalances = [];
            for (const [type, counts] of analysis.portStats.entries()) {
                if (counts.input !== counts.output) {
                    imbalances.push({
                        type,
                        input: counts.input,
                        output: counts.output,
                        difference: counts.output - counts.input
                    });
                }
            }
            return {
                isBalanced: imbalances.length === 0,
                imbalances
            };
        }

        // 创建基础Mode 3场景
        function createMode3BasicScenario() {
            debugGameState.nodes = [];
            debugGameState.temporaryNodes = [];

            // 起点节点
            const startNode = new DebugNode('start');
            startNode.id = 'mode3_start';
            startNode.addOutputPort('square', '#ff5252');
            startNode.moveTo(50, 200);

            // 终点节点
            const endNode = new DebugNode('end');
            endNode.id = 'mode3_end';
            endNode.addInputPort('square', '#ff5252');
            endNode.moveTo(550, 200);

            debugGameState.nodes = [startNode, endNode];
            
            log('🎬 创建基础Mode 3场景', 'info');
            log(`基础节点: 1个起点, 1个终点`, 'step');
            
            return { startNode, endNode };
        }

        // 当前实现的临时节点生成（从game.js复制）
        function generateTemporaryNodesCurrentImplementation() {
            log('🔧 使用当前实现生成临时节点...', 'info');
            
            const wave = 1;
            debugGameState.temporaryNodes = [];

            try {
                // 简单生成1个中间节点
                const tempNode = new DebugNode('normal');
                tempNode.id = `temp_wave_${wave}`;
                tempNode.label = `Temp-W${wave}`;
                
                // 添加端口 - 这里可能是问题所在
                tempNode.addInputPort('square', '#ff5252');
                tempNode.addOutputPort('diamond', '#2196F3'); // 注意：输出类型不同！
                
                tempNode.moveTo(300, 200);
                debugGameState.temporaryNodes.push(tempNode);

                log(`✅ 生成 ${debugGameState.temporaryNodes.length} 个临时节点`, 'success');
                return debugGameState.temporaryNodes;

            } catch (error) {
                log(`❌ 临时节点生成失败: ${error.message}`, 'error');
                return [];
            }
        }

        // 修复的临时节点生成
        function generateTemporaryNodesFixed() {
            log('🔧 使用修复版本生成临时节点...', 'info');
            
            const wave = 1;
            debugGameState.temporaryNodes = [];

            try {
                // 分析现有系统需求
                const analysis = analyzeSystemState();
                const balance = checkPortBalance(analysis);

                log('📊 分析现有系统需求:', 'step');
                log(`起点: ${analysis.startNodes.length}, 终点: ${analysis.endNodes.length}`, 'step');

                // 生成平衡的临时节点
                const tempNode = new DebugNode('normal');
                tempNode.id = `temp_wave_${wave}_fixed`;
                tempNode.label = `Bridge-W${wave}`;
                
                // 确保端口类型匹配
                tempNode.addInputPort('square', '#ff5252');  // 匹配起点输出
                tempNode.addOutputPort('square', '#ff5252'); // 匹配终点输入
                
                tempNode.moveTo(300, 200);
                debugGameState.temporaryNodes.push(tempNode);

                log(`✅ 生成 ${debugGameState.temporaryNodes.length} 个临时节点 (端口匹配)`, 'success');
                return debugGameState.temporaryNodes;

            } catch (error) {
                log(`❌ 修复版本生成失败: ${error.message}`, 'error');
                return [];
            }
        }

        // 测试当前实现
        function testCurrentImplementation() {
            log('🧪 测试当前实现', 'header');
            
            createMode3BasicScenario();
            generateTemporaryNodesCurrentImplementation();
            
            const analysis = analyzeSystemState();
            const balance = checkPortBalance(analysis);
            
            log('📊 系统分析结果:', 'info');
            log(`<div class="analysis">
                总节点数: ${analysis.totalNodes}<br>
                起点: ${analysis.startNodes.length}, 终点: ${analysis.endNodes.length}<br>
                临时节点: ${analysis.temporaryNodes.length}<br>
                端口平衡: ${balance.isBalanced ? '✅ 平衡' : '❌ 不平衡'}
            </div>`, 'info');

            if (!balance.isBalanced) {
                log('⚠️ 发现端口不平衡问题:', 'warning');
                balance.imbalances.forEach(imbalance => {
                    log(`${imbalance.type}: ${imbalance.output}输出 vs ${imbalance.input}输入 (差值: ${imbalance.difference})`, 'error');
                });
            }

            return balance.isBalanced;
        }

        // 逐步调试
        function debugStepByStep() {
            log('🔍 逐步调试分析', 'header');
            
            // 步骤1: 创建基础场景
            log('步骤1: 创建基础场景', 'step');
            createMode3BasicScenario();
            
            let analysis = analyzeSystemState();
            log(`基础场景: ${analysis.totalNodes} 节点`, 'step');
            
            // 显示端口详情
            log('📋 基础场景端口分析:', 'info');
            for (const [type, counts] of analysis.portStats.entries()) {
                log(`${type}: ${counts.output}输出, ${counts.input}输入`, 'step');
            }
            
            // 步骤2: 当前实现
            log('步骤2: 当前实现生成临时节点', 'step');
            generateTemporaryNodesCurrentImplementation();
            
            analysis = analyzeSystemState();
            const balance1 = checkPortBalance(analysis);
            
            log('📋 添加临时节点后端口分析:', 'info');
            for (const [type, counts] of analysis.portStats.entries()) {
                log(`${type}: ${counts.output}输出, ${counts.input}输入`, 'step');
            }
            
            if (!balance1.isBalanced) {
                log('❌ 问题确认: 端口类型不匹配导致不可解', 'error');
            }
            
            // 步骤3: 修复版本
            log('步骤3: 修复版本生成临时节点', 'step');
            createMode3BasicScenario(); // 重置
            generateTemporaryNodesFixed();
            
            analysis = analyzeSystemState();
            const balance2 = checkPortBalance(analysis);
            
            log('📋 修复版本端口分析:', 'info');
            for (const [type, counts] of analysis.portStats.entries()) {
                log(`${type}: ${counts.output}输出, ${counts.input}输入`, 'step');
            }
            
            if (balance2.isBalanced) {
                log('✅ 修复成功: 端口完全平衡', 'success');
            } else {
                log('❌ 修复失败: 仍然存在不平衡', 'error');
            }
        }

        // 测试端口平衡
        function testPortBalance() {
            log('⚖️ 端口平衡详细测试', 'header');
            
            createMode3BasicScenario();
            generateTemporaryNodesCurrentImplementation();
            
            const analysis = analyzeSystemState();
            
            // 创建端口分析表格
            let tableHTML = `
                <table>
                    <tr><th>端口类型</th><th>输出端口</th><th>输入端口</th><th>差值</th><th>状态</th></tr>
            `;
            
            for (const [type, counts] of analysis.portStats.entries()) {
                const diff = counts.output - counts.input;
                const status = diff === 0 ? '✅ 平衡' : '❌ 不平衡';
                const rowClass = diff === 0 ? 'success' : 'error';
                
                tableHTML += `
                    <tr class="${rowClass}">
                        <td>${type}</td>
                        <td>${counts.output}</td>
                        <td>${counts.input}</td>
                        <td>${diff > 0 ? '+' : ''}${diff}</td>
                        <td>${status}</td>
                    </tr>
                `;
            }
            
            tableHTML += '</table>';
            
            log(`📊 端口平衡分析表:${tableHTML}`, 'info');
            
            // 节点详情
            log('📋 节点详情分析:', 'info');
            [...debugGameState.nodes, ...debugGameState.temporaryNodes].forEach(node => {
                const inputPorts = node.inputPorts.map(p => `${p.type}-${p.color}`).join(', ');
                const outputPorts = node.outputPorts.map(p => `${p.type}-${p.color}`).join(', ');
                log(`${node.type} ${node.id}: 输入[${inputPorts}] 输出[${outputPorts}]`, 'step');
            });
        }

        // 修复并测试
        function fixAndTest() {
            log('🔧 应用修复并测试', 'header');
            
            // 修复1: 正确的端口匹配算法
            function generateCorrectTemporaryNodes() {
                debugGameState.temporaryNodes = [];
                
                // 分析现有端口需求
                const analysis = analyzeSystemState();
                const requiredConnections = new Map();
                
                // 统计起点输出
                analysis.startNodes.forEach(node => {
                    node.outputPorts.forEach(port => {
                        const key = `${port.type}-${port.color}`;
                        requiredConnections.set(key, (requiredConnections.get(key) || 0) + 1);
                    });
                });
                
                log('🎯 需要处理的端口类型:', 'step');
                for (const [type, count] of requiredConnections.entries()) {
                    log(`${type}: ${count} 个输出需要处理`, 'step');
                }
                
                // 生成桥接节点
                let nodeIndex = 0;
                for (const [portType, count] of requiredConnections.entries()) {
                    const [type, color] = portType.split('-');
                    
                    const bridgeNode = new DebugNode('normal');
                    bridgeNode.id = `bridge_${nodeIndex}`;
                    bridgeNode.label = `Bridge-${nodeIndex + 1}`;
                    
                    // 完全匹配的端口
                    bridgeNode.addInputPort(type, color);
                    bridgeNode.addOutputPort(type, color);
                    
                    bridgeNode.moveTo(250 + nodeIndex * 100, 200);
                    debugGameState.temporaryNodes.push(bridgeNode);
                    
                    nodeIndex++;
                }
                
                log(`✅ 生成 ${debugGameState.temporaryNodes.length} 个桥接节点`, 'success');
            }
            
            // 应用修复
            createMode3BasicScenario();
            generateCorrectTemporaryNodes();
            
            // 验证结果
            const analysis = analyzeSystemState();
            const balance = checkPortBalance(analysis);
            
            log('📊 修复后系统状态:', 'info');
            log(`<div class="analysis">
                总节点: ${analysis.totalNodes}<br>
                临时节点: ${analysis.temporaryNodes.length}<br>
                端口平衡: ${balance.isBalanced ? '✅ 完全平衡' : '❌ 仍有问题'}<br>
                可解性: ${balance.isBalanced ? '✅ 可解' : '❌ 不可解'}
            </div>`, balance.isBalanced ? 'success' : 'error');
            
            if (balance.isBalanced) {
                log('🎉 Level 1 Mode 3 修复成功！', 'success');
                
                // 输出修复后的代码建议
                log('📝 建议的代码修复:', 'info');
                log(`<pre>
// 修复generateProgressiveWaveRequirements中的端口匹配逻辑:
function generateConstraintSatisfyingTemporaryNodes(constraints) {
    const temporaryNodes = [];
    const requiredConnections = new Map();
    
    // 分析起点输出端口
    const startNodes = gameState.nodes.filter(n => n.type === 'start');
    startNodes.forEach(node => {
        node.outputPorts.forEach(port => {
            const key = \`\${port.type}-\${port.color}\`;
            requiredConnections.set(key, (requiredConnections.get(key) || 0) + 1);
        });
    });
    
    // 为每种端口类型创建桥接节点
    let nodeIndex = 0;
    for (const [portType, count] of requiredConnections.entries()) {
        const [type, color] = portType.split('-');
        
        const bridgeNode = new Node('normal');
        bridgeNode.id = \`bridge_\${Date.now()}_\${nodeIndex}\`;
        bridgeNode.label = \`Bridge-\${nodeIndex + 1}\`;
        
        // 确保端口类型完全匹配
        bridgeNode.addInputPort(type, color);
        bridgeNode.addOutputPort(type, color);
        
        temporaryNodes.push(bridgeNode);
        nodeIndex++;
    }
    
    return temporaryNodes;
}
                </pre>`, 'info');
            }
            
            return balance.isBalanced;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            log('📋 调试结果已清空', 'info');
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            log('🐛 Mode 3 Level 1 调试器就绪', 'info');
            log('请点击"测试当前实现"开始调试', 'step');
        });
    </script>
</body>
</html>