<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minimal Algorithm Test</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Minimal Algorithm Test</h1>
        
        <button onclick="testMinimalAlgorithm()">Test Minimal Algorithm</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div class="test-output" id="testOutput">
            <div class="info">Ready to test minimal algorithm implementation...</div>
        </div>
    </div>

    <script>
        // Minimal implementation of the core algorithm components
        
        class Port {
            constructor(type, color, side, nodeId) {
                this.id = 'port_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                this.type = type;
                this.color = color;
                this.side = side;
                this.nodeId = nodeId;
                this.connectedTo = null;
                this.x = 0;
                this.y = 0;
            }
        }

        class Node {
            constructor(type = 'normal') {
                this.id = 'node_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                this.type = type;
                this.x = 0;
                this.y = 0;
                this.width = 120;
                this.height = 60;
                this.inputPorts = [];
                this.outputPorts = [];
                this.depth = -1;
                this.label = this.generateLabel();
            }
            
            generateLabel() {
                switch(this.type) {
                    case 'start': return 'Start';
                    case 'end': return 'End';
                    default: return 'Node ' + Math.floor(Math.random() * 1000);
                }
            }
            
            addInputPort(type, color) {
                if (this.type === 'start') return;
                const port = new Port(type, color, 'input', this.id);
                this.inputPorts.push(port);
                return port;
            }
            
            addOutputPort(type, color) {
                if (this.type === 'end') return;
                const port = new Port(type, color, 'output', this.id);
                this.outputPorts.push(port);
                return port;
            }
        }

        // Minimal algorithm implementation
        function calculateMaxDepth(level) {
            switch(level) {
                case 1: return 1;
                case 2: return 1;
                case 3: return 2;
                case 4: return 2;
                case 5: return 3;
                default: return Math.min(level - 2, 4);
            }
        }

        function calculatePortPairs(level) {
            switch(level) {
                case 1: return 1;
                case 2: return 1;
                case 3: return 2;
                case 4: return 2;
                case 5: return 3;
                default: return Math.min(level - 2, 4);
            }
        }

        function generateMinimalScenario(difficulty) {
            const startNode = new Node('start');
            startNode.label = 'Start';
            startNode.id = 'start_node';
            startNode.depth = 0;
            
            const endNode = new Node('end');
            endNode.label = 'End';
            endNode.id = 'end_node';
            endNode.depth = calculateMaxDepth(difficulty.level);
            
            const portPairs = calculatePortPairs(difficulty.level);
            const allNodes = [startNode, endNode];
            
            // Add simple ports
            for (let i = 0; i < portPairs; i++) {
                const typeIndex = i % difficulty.availableTypes.length;
                const colorIndex = i % difficulty.availableColors.length;
                
                const portType = difficulty.availableTypes[typeIndex];
                const portColor = difficulty.availableColors[colorIndex];
                
                startNode.addOutputPort(portType, portColor);
                endNode.addInputPort(portType, portColor);
            }
            
            // Add intermediate nodes if needed
            const intermediateNodes = [];
            if (endNode.depth > 1) {
                for (let depth = 1; depth < endNode.depth; depth++) {
                    const intermediateNode = new Node('normal');
                    intermediateNode.id = 'intermediate_' + depth;
                    intermediateNode.depth = depth;
                    intermediateNode.label = 'Layer ' + depth;
                    
                    // Add matching ports
                    const portType = difficulty.availableTypes[0];
                    const portColor = difficulty.availableColors[0];
                    
                    intermediateNode.addInputPort(portType, portColor);
                    intermediateNode.addOutputPort(portType, portColor);
                    
                    intermediateNodes.push(intermediateNode);
                    allNodes.push(intermediateNode);
                }
            }
            
            // Create connections
            const guaranteedConnections = [];
            for (let i = 0; i < portPairs; i++) {
                if (intermediateNodes.length === 0) {
                    // Direct connection
                    guaranteedConnections.push({
                        from: { node: startNode.id, type: startNode.outputPorts[i].type, color: startNode.outputPorts[i].color },
                        to: { node: endNode.id, type: endNode.inputPorts[i].type, color: endNode.inputPorts[i].color },
                        sourceDepth: 0,
                        targetDepth: endNode.depth
                    });
                } else {
                    // Through intermediate nodes
                    guaranteedConnections.push({
                        from: { node: startNode.id, type: startNode.outputPorts[i].type, color: startNode.outputPorts[i].color },
                        to: { node: intermediateNodes[0].id, type: intermediateNodes[0].inputPorts[0].type, color: intermediateNodes[0].inputPorts[0].color },
                        sourceDepth: 0,
                        targetDepth: 1
                    });
                    
                    for (let j = 0; j < intermediateNodes.length - 1; j++) {
                        guaranteedConnections.push({
                            from: { node: intermediateNodes[j].id, type: intermediateNodes[j].outputPorts[0].type, color: intermediateNodes[j].outputPorts[0].color },
                            to: { node: intermediateNodes[j + 1].id, type: intermediateNodes[j + 1].inputPorts[0].type, color: intermediateNodes[j + 1].inputPorts[0].color },
                            sourceDepth: j + 1,
                            targetDepth: j + 2
                        });
                    }
                    
                    guaranteedConnections.push({
                        from: { node: intermediateNodes[intermediateNodes.length - 1].id, type: intermediateNodes[intermediateNodes.length - 1].outputPorts[0].type, color: intermediateNodes[intermediateNodes.length - 1].outputPorts[0].color },
                        to: { node: endNode.id, type: endNode.inputPorts[i].type, color: endNode.inputPorts[i].color },
                        sourceDepth: intermediateNodes.length,
                        targetDepth: endNode.depth
                    });
                }
            }
            
            return {
                startNode: startNode,
                endNode: endNode,
                nodes: intermediateNodes,
                allNodes: allNodes,
                level: difficulty.level,
                guaranteedConnections: guaranteedConnections,
                nodesByLayer: new Map()
            };
        }

        function validateMinimalScenario(scenario) {
            const errors = [];
            
            // Basic structure check
            if (!scenario.startNode) errors.push('Missing start node');
            if (!scenario.endNode) errors.push('Missing end node');
            
            // Depth check
            if (scenario.startNode && scenario.startNode.depth !== 0) {
                errors.push('Start node depth should be 0, got ' + scenario.startNode.depth);
            }
            
            if (scenario.endNode && scenario.endNode.depth <= 0) {
                errors.push('End node depth should be > 0, got ' + scenario.endNode.depth);
            }
            
            // Port balance check
            const typeBalance = new Map();
            
            scenario.allNodes.forEach(node => {
                node.inputPorts.forEach(port => {
                    const key = port.type + ':' + port.color;
                    if (!typeBalance.has(key)) {
                        typeBalance.set(key, { input: 0, output: 0 });
                    }
                    typeBalance.get(key).input++;
                });
                
                node.outputPorts.forEach(port => {
                    const key = port.type + ':' + port.color;
                    if (!typeBalance.has(key)) {
                        typeBalance.set(key, { input: 0, output: 0 });
                    }
                    typeBalance.get(key).output++;
                });
            });
            
            for (const [typeKey, balance] of typeBalance) {
                if (balance.input !== balance.output) {
                    errors.push('Port type ' + typeKey + ' unbalanced: input=' + balance.input + ', output=' + balance.output);
                }
            }
            
            // Connection depth check
            scenario.guaranteedConnections.forEach((conn, index) => {
                if (conn.sourceDepth >= conn.targetDepth) {
                    errors.push('Connection ' + (index + 1) + ' violates depth constraint: ' + conn.sourceDepth + ' >= ' + conn.targetDepth);
                }
            });
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type;
            
            const div = document.createElement('div');
            div.className = colorClass;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
        }
        
        function testMinimalAlgorithm() {
            clearOutput();
            log('Testing minimal layered DAG algorithm...', 'info');
            
            try {
                // Test different levels
                for (let level = 1; level <= 5; level++) {
                    log('Testing level ' + level + '...', 'info');
                    
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'],
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107']
                    };
                    
                    const scenario = generateMinimalScenario(difficulty);
                    const validation = validateMinimalScenario(scenario);
                    
                    if (validation.isValid) {
                        log('SUCCESS: Level ' + level + ' - ' + scenario.nodes.length + ' intermediate nodes, ' + scenario.guaranteedConnections.length + ' connections', 'success');
                    } else {
                        log('ERROR: Level ' + level + ' - ' + validation.errors.join('; '), 'error');
                    }
                }
                
                log('Minimal algorithm test completed!', 'success');
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, ready to test', 'info');
            }, 500);
        });
    </script>
</body>
</html>
