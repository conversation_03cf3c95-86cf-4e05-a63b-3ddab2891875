<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Port Level DAG Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .node-diagram {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .level-info {
            font-family: monospace;
            font-size: 12px;
            background: #e9ecef;
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Port Level DAG Validation Test</h1>
        <p>Testing the enhanced port-level DAG validation that considers layer distribution rather than just node distribution.</p>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="test-section">
            <h2>Test Case 1: Your Example - Inevitable Cycle</h2>
            <p>The exact scenario you described that would require same-level connections.</p>
            <div class="node-diagram">
nodeA (Level 1): 左1 右1
nodeB (Level 2): 左1 右2  
nodeC (Level 3): 左1
            </div>
            <p><strong>Expected:</strong> Should be detected as invalid because nodeB's 2 outputs cannot all go to higher levels.</p>
            <button onclick="testInevitableCycle()">Run Test 1</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 2: Valid Layered Distribution</h2>
            <p>A properly distributed scenario that can form a valid DAG.</p>
            <div class="node-diagram">
nodeA (Level 0): 右2
nodeB (Level 1): 左1 右1
nodeC (Level 2): 左2
            </div>
            <p><strong>Expected:</strong> Should be valid - clear flow from level 0 → 1 → 2.</p>
            <button onclick="testValidLayered()">Run Test 2</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 3: Cumulative Constraint Violation</h2>
            <p>More inputs than outputs at early levels.</p>
            <div class="node-diagram">
nodeA (Level 0): 左2
nodeB (Level 1): 右1
nodeC (Level 2): 左1
            </div>
            <p><strong>Expected:</strong> Should be invalid - level 0 has inputs but no prior outputs.</p>
            <button onclick="testCumulativeViolation()">Run Test 3</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 4: Complex Valid Scenario</h2>
            <p>Multiple levels with proper flow distribution.</p>
            <div class="node-diagram">
nodeA (Level 0): 右3
nodeB (Level 1): 左2 右2
nodeC (Level 2): 左1
nodeD (Level 3): 左2
            </div>
            <p><strong>Expected:</strong> Should be valid - proper flow cascade.</p>
            <button onclick="testComplexValid()">Run Test 4</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 5: Same Level Bottleneck</h2>
            <p>Too many outputs at one level for future levels to consume.</p>
            <div class="node-diagram">
nodeA (Level 1): 右5
nodeB (Level 2): 左1 右1
nodeC (Level 3): 左2
            </div>
            <p><strong>Expected:</strong> Should be invalid - level 1 has 5 outputs but only 3 future inputs.</p>
            <button onclick="testSameLevelBottleneck()">Run Test 5</button>
            <div id="test5-result"></div>
        </div>

        <div class="test-section">
            <h2>Overall Test Results</h2>
            <div id="overall-results"></div>
        </div>
    </div>

    <script src="compensation_algorithm.js"></script>
    <script>
        // Initialize the compensation algorithm
        const portTypes = [
            { shape: 'square', color: 'red' }
        ];

        const compensationAlgorithm = new HierarchicalCompensationAlgorithm(portTypes, 5);

        function logResult(testId, message, type = 'info') {
            const resultDiv = document.getElementById(testId + '-result');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'warning';
            resultDiv.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        function clearResults() {
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`test${i}-result`).innerHTML = '';
            }
            document.getElementById('overall-results').innerHTML = '';
        }

        function createNode(id, level, inputPorts, outputPorts) {
            const node = {
                id: id,
                type: level === 0 ? 'start' : level === 5 ? 'end' : 'intermediate',
                level: level,
                depth: level * 100,
                inputPorts: [],
                outputPorts: [],
                x: 50, y: 50,
                area: 'placed'
            };

            // Add input ports
            for (let i = 0; i < inputPorts; i++) {
                node.inputPorts.push({
                    id: `${id}_in_${i}`,
                    type: 'square',
                    color: 'red',
                    side: 'input',
                    nodeId: id,
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                });
            }

            // Add output ports
            for (let i = 0; i < outputPorts; i++) {
                node.outputPorts.push({
                    id: `${id}_out_${i}`,
                    type: 'square',
                    color: 'red',
                    side: 'output',
                    nodeId: id,
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                });
            }

            return node;
        }

        // Test Case 1: Your Example - Inevitable Cycle
        function testInevitableCycle() {
            logResult('test1', '🧪 Testing inevitable cycle scenario...', 'info');
            
            const nodes = [
                createNode('nodeA', 1, 1, 1), // Level 1: 左1 右1
                createNode('nodeB', 2, 1, 2), // Level 2: 左1 右2
                createNode('nodeC', 3, 1, 0)  // Level 3: 左1
            ];

            // Show level distribution
            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let distributionStr = '';
            for (const [level, data] of distribution.entries()) {
                if (data.inputs > 0 || data.outputs > 0) {
                    distributionStr += `<div class="level-info">Level ${level}: ${data.inputs} inputs, ${data.outputs} outputs</div>`;
                }
            }
            logResult('test1', `Port distribution:${distributionStr}`, 'info');

            const isValid = compensationAlgorithm.validatePortLevelDAG('square-red', distribution);
            
            if (!isValid) {
                logResult('test1', '✅ Correctly detected inevitable cycle scenario', 'success');
                return true;
            } else {
                logResult('test1', '❌ Failed to detect inevitable cycle - this should be invalid', 'error');
                return false;
            }
        }

        // Test Case 2: Valid Layered Distribution
        function testValidLayered() {
            logResult('test2', '🧪 Testing valid layered distribution...', 'info');
            
            const nodes = [
                createNode('nodeA', 0, 0, 2), // Level 0: 右2
                createNode('nodeB', 1, 1, 1), // Level 1: 左1 右1
                createNode('nodeC', 2, 2, 0)  // Level 2: 左2
            ];

            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let distributionStr = '';
            for (const [level, data] of distribution.entries()) {
                if (data.inputs > 0 || data.outputs > 0) {
                    distributionStr += `<div class="level-info">Level ${level}: ${data.inputs} inputs, ${data.outputs} outputs</div>`;
                }
            }
            logResult('test2', `Port distribution:${distributionStr}`, 'info');

            const isValid = compensationAlgorithm.validatePortLevelDAG('square-red', distribution);
            
            if (isValid) {
                logResult('test2', '✅ Valid layered distribution correctly accepted', 'success');
                return true;
            } else {
                logResult('test2', '❌ Valid scenario incorrectly rejected', 'error');
                return false;
            }
        }

        // Test Case 3: Cumulative Constraint Violation
        function testCumulativeViolation() {
            logResult('test3', '🧪 Testing cumulative constraint violation...', 'info');
            
            const nodes = [
                createNode('nodeA', 0, 2, 0), // Level 0: 左2 (invalid - no prior outputs)
                createNode('nodeB', 1, 0, 1), // Level 1: 右1
                createNode('nodeC', 2, 1, 0)  // Level 2: 左1
            ];

            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let distributionStr = '';
            for (const [level, data] of distribution.entries()) {
                if (data.inputs > 0 || data.outputs > 0) {
                    distributionStr += `<div class="level-info">Level ${level}: ${data.inputs} inputs, ${data.outputs} outputs</div>`;
                }
            }
            logResult('test3', `Port distribution:${distributionStr}`, 'info');

            const isValid = compensationAlgorithm.validatePortLevelDAG('square-red', distribution);
            
            if (!isValid) {
                logResult('test3', '✅ Correctly detected cumulative constraint violation', 'success');
                return true;
            } else {
                logResult('test3', '❌ Failed to detect cumulative violation', 'error');
                return false;
            }
        }

        // Test Case 4: Complex Valid Scenario
        function testComplexValid() {
            logResult('test4', '🧪 Testing complex valid scenario...', 'info');

            const nodes = [
                createNode('nodeA', 0, 0, 3), // Level 0: 右3
                createNode('nodeB', 1, 2, 2), // Level 1: 左2 右2
                createNode('nodeC', 2, 1, 0), // Level 2: 左1 (修复：移除输出端口)
                createNode('nodeD', 3, 2, 0)  // Level 3: 左2
            ];

            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let distributionStr = '';
            for (const [level, data] of distribution.entries()) {
                if (data.inputs > 0 || data.outputs > 0) {
                    distributionStr += `<div class="level-info">Level ${level}: ${data.inputs} inputs, ${data.outputs} outputs</div>`;
                }
            }
            logResult('test4', `Port distribution:${distributionStr}`, 'info');

            const isValid = compensationAlgorithm.validatePortLevelDAG('square-red', distribution);
            
            if (isValid) {
                logResult('test4', '✅ Complex valid scenario correctly accepted', 'success');
                return true;
            } else {
                logResult('test4', '❌ Complex valid scenario incorrectly rejected', 'error');
                return false;
            }
        }

        // Test Case 5: Same Level Bottleneck
        function testSameLevelBottleneck() {
            logResult('test5', '🧪 Testing same level bottleneck...', 'info');
            
            const nodes = [
                createNode('nodeA', 1, 0, 5), // Level 1: 右5
                createNode('nodeB', 2, 1, 1), // Level 2: 左1 右1
                createNode('nodeC', 3, 2, 0)  // Level 3: 左2
            ];

            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let distributionStr = '';
            for (const [level, data] of distribution.entries()) {
                if (data.inputs > 0 || data.outputs > 0) {
                    distributionStr += `<div class="level-info">Level ${level}: ${data.inputs} inputs, ${data.outputs} outputs</div>`;
                }
            }
            logResult('test5', `Port distribution:${distributionStr}`, 'info');

            const isValid = compensationAlgorithm.validatePortLevelDAG('square-red', distribution);
            
            if (!isValid) {
                logResult('test5', '✅ Correctly detected same level bottleneck', 'success');
                return true;
            } else {
                logResult('test5', '❌ Failed to detect same level bottleneck', 'error');
                return false;
            }
        }

        function runAllTests() {
            clearResults();
            
            const results = [];
            results.push(testInevitableCycle());
            results.push(testValidLayered());
            results.push(testCumulativeViolation());
            results.push(testComplexValid());
            results.push(testSameLevelBottleneck());
            
            const passedTests = results.filter(r => r).length;
            const totalTests = results.length;
            
            const overallDiv = document.getElementById('overall-results');
            if (passedTests === totalTests) {
                overallDiv.innerHTML = `<div class="result success">🎉 All ${totalTests} tests passed! Port-level DAG validation is working correctly.</div>`;
            } else {
                overallDiv.innerHTML = `<div class="result error">❌ ${passedTests}/${totalTests} tests passed. Some issues need to be addressed.</div>`;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
