// Final comprehensive verification of Mode 3 fix
console.log('🔬 Final Mode 3 Comprehensive Verification');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// Test scenarios
const testScenarios = [
    {
        name: "基础Mode 3 Level 1",
        setup: () => {
            const start = new TestNode('start');
            start.addOutputPort('square', '#ff5252');
            const end = new TestNode('end');
            end.addInputPort('square', '#ff5252');
            return { placedNodes: [start, end], nodes: [] };
        }
    },
    {
        name: "多端口起点终点",
        setup: () => {
            const start = new TestNode('start');
            start.addOutputPort('square', '#ff5252');
            start.addOutputPort('diamond', '#2196F3');
            const end = new TestNode('end');
            end.addInputPort('square', '#ff5252');
            end.addInputPort('diamond', '#2196F3');
            return { placedNodes: [start, end], nodes: [] };
        }
    },
    {
        name: "复杂多类型场景",
        setup: () => {
            const start = new TestNode('start');
            start.addOutputPort('square', '#ff5252');
            start.addOutputPort('triangle', '#4CAF50');
            const end = new TestNode('end');
            end.addInputPort('square', '#ff5252');
            end.addInputPort('triangle', '#4CAF50');
            return { placedNodes: [start, end], nodes: [] };
        }
    },
    {
        name: "gameState.nodes中的节点",
        setup: () => {
            const start = new TestNode('start');
            start.addOutputPort('square', '#ff5252');
            const end = new TestNode('end');
            end.addInputPort('square', '#ff5252');
            return { placedNodes: [], nodes: [start, end] }; // 在nodes中而不是placedNodes
        }
    },
    {
        name: "混合nodes和placedNodes",
        setup: () => {
            const start = new TestNode('start');
            start.addOutputPort('square', '#ff5252');
            const end = new TestNode('end');
            end.addInputPort('square', '#ff5252');
            const existing = new TestNode('normal');
            existing.addInputPort('diamond', '#2196F3');
            existing.addOutputPort('diamond', '#2196F3');
            return { placedNodes: [existing], nodes: [start, end] };
        }
    }
];

// Fixed implementation functions
function analyzeGlobalSystemState(mockState) {
    const allPlacedNodes = [
        ...(mockState.placedNodes || []),
        ...(mockState.nodes || [])
    ].filter((node, index, array) => 
        array.findIndex(n => n.id === node.id) === index
    );
    
    return {
        placedNodes: allPlacedNodes,
        startNodes: allPlacedNodes.filter(n => n.type === 'start'),
        endNodes: allPlacedNodes.filter(n => n.type === 'end'),
        intermediateNodes: allPlacedNodes.filter(n => n.type === 'normal'),
        globalPortBalance: new Map()
    };
}

function calculateGlobalConstraintRequirements(globalAnalysis, wave) {
    const requirements = {
        portBalanceDeficits: new Map(),
        requiredLayerNodes: new Map()
    };

    globalAnalysis.placedNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!globalAnalysis.globalPortBalance.has(typeKey)) {
                globalAnalysis.globalPortBalance.set(typeKey, { input: 0, output: 0 });
            }
            const balance = globalAnalysis.globalPortBalance.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                balance.input++;
            } else {
                balance.output++;
            }
        });
    });

    globalAnalysis.globalPortBalance.forEach((balance, typeKey) => {
        const deficit = balance.output - balance.input;
        if (deficit !== 0) {
            requirements.portBalanceDeficits.set(typeKey, deficit);
        }
    });

    return requirements;
}

function generateConstraintSatisfyingTemporaryNodes(globalConstraints, currentAnalysis) {
    const temporaryNodes = [];
    let nodeIndex = 0;

    if (globalConstraints.portBalanceDeficits.size === 0 || 
        Array.from(globalConstraints.portBalanceDeficits.values()).every(deficit => deficit === 0)) {
        
        const requiredTypes = new Set();
        currentAnalysis.startNodes.forEach(node => {
            if (node.outputPorts) {
                node.outputPorts.forEach(port => {
                    requiredTypes.add(`${port.type}-${port.color}`);
                });
            }
        });
        
        for (const portTypeKey of requiredTypes) {
            const [type, color] = portTypeKey.split('-');
            
            const bridgeNode = new TestNode('normal');
            bridgeNode.id = `bridge_${Date.now()}_${nodeIndex}`;
            bridgeNode.label = `Bridge-${nodeIndex + 1}`;
            
            bridgeNode.addInputPort(type, color);
            bridgeNode.addOutputPort(type, color);
            
            temporaryNodes.push(bridgeNode);
            nodeIndex++;
        }
    }

    if (temporaryNodes.length === 0) {
        const fallbackNode = new TestNode('normal');
        fallbackNode.id = `temp_fallback_${nodeIndex}`;
        fallbackNode.label = 'Bridge';
        fallbackNode.addInputPort('square', '#ff5252');
        fallbackNode.addOutputPort('square', '#ff5252');
        temporaryNodes.push(fallbackNode);
    }

    return temporaryNodes;
}

function verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes) {
    const allNodes = [...globalAnalysis.placedNodes, ...temporaryNodes];
    const typeCounts = new Map();

    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!typeCounts.has(typeKey)) {
                typeCounts.set(typeKey, { input: 0, output: 0 });
            }
            const count = typeCounts.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                count.input++;
            } else {
                count.output++;
            }
        });
    });

    const imbalances = [];
    for (const [typeKey, count] of typeCounts.entries()) {
        if (count.input !== count.output) {
            imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
        }
    }

    return {
        isValid: imbalances.length === 0,
        imbalances,
        allNodes: allNodes.length,
        typeCounts
    };
}

// Test runner
function runScenarioTest(scenario) {
    console.log(`\n--- ${scenario.name} ---`);
    
    const mockState = scenario.setup();
    const globalAnalysis = analyzeGlobalSystemState(mockState);
    
    console.log(`设置: ${globalAnalysis.startNodes.length}起点, ${globalAnalysis.endNodes.length}终点, ${globalAnalysis.intermediateNodes.length}中间节点`);
    
    const globalConstraints = calculateGlobalConstraintRequirements(globalAnalysis, 1);
    const temporaryNodes = generateConstraintSatisfyingTemporaryNodes(globalConstraints, globalAnalysis);
    const verification = verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes);
    
    console.log(`结果: ${verification.isValid ? '✅ 可解' : '❌ 不可解'}`);
    console.log(`生成: ${temporaryNodes.length} 个临时节点`);
    console.log(`总计: ${verification.allNodes} 个节点`);
    
    if (!verification.isValid) {
        console.log(`❌ 不平衡: ${verification.imbalances.join(', ')}`);
    }
    
    // 详细端口分析
    console.log('端口分析:');
    for (const [typeKey, count] of verification.typeCounts.entries()) {
        const status = count.input === count.output ? '✅' : '❌';
        console.log(`  ${typeKey}: ${count.output}输出, ${count.input}输入 ${status}`);
    }
    
    return verification.isValid;
}

// Run all tests
console.log('🚀 开始综合验证测试...\n');

let passedTests = 0;
let totalTests = testScenarios.length;

testScenarios.forEach(scenario => {
    if (runScenarioTest(scenario)) {
        passedTests++;
    }
});

// Final summary
console.log('\n' + '='.repeat(50));
console.log('📊 最终测试结果');
console.log('='.repeat(50));
console.log(`通过测试: ${passedTests}/${totalTests}`);
console.log(`成功率: ${(passedTests/totalTests * 100).toFixed(1)}%`);

if (passedTests === totalTests) {
    console.log('\n🎉 所有测试通过！Mode 3 Level 1 修复成功！');
    console.log('✅ 端口类型匹配算法正确');
    console.log('✅ 全局约束满足算法稳定');
    console.log('✅ 支持多种节点配置场景');
    console.log('✅ 正确处理gameState.nodes和placedNodes');
} else {
    console.log('\n⚠️ 部分测试失败，需要进一步调试');
}

console.log('\n✅ 综合验证完成');