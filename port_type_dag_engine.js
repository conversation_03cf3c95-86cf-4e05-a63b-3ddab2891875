// ========== 端口类型优先的DAG生成引擎 ==========

class PortTypeDAGEngine {
    constructor() {
        this.portTypes = ['square', 'circle', 'triangle', 'diamond'];
        this.portColors = ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'];
        
        // 为每种端口类型维护独立的DAG
        this.portTypeDAGs = new Map();
        this.initializePortTypeDAGs();
    }

    initializePortTypeDAGs() {
        this.portTypes.forEach(type => {
            this.portColors.forEach(color => {
                const key = `${type}-${color}`;
                this.portTypeDAGs.set(key, {
                    nodes: new Set(),
                    edges: new Map(), // nodeId -> Set of connected nodeIds
                    sources: new Set(), // 只有输出端口的节点
                    sinks: new Set(),   // 只有输入端口的节点
                    intermediates: new Set(), // 既有输入又有输出的节点
                    topologicalOrder: []
                });
            });
        });
    }

    // ========== 核心算法：端口类型优先生成 ==========
    
    generatePortTypeBasedTopology(existingNodes, existingConnections, config) {
        console.log('[PORT-DAG] Generating port-type-based topology');
        
        // 步骤1: 分析现有端口类型使用情况
        const currentUsage = this.analyzePortTypeUsage(existingNodes, existingConnections);
        
        // 步骤2: 为每种端口类型生成独立的DAG
        const portTypeDAGs = this.generateIndependentDAGs(currentUsage, config);
        
        // 步骤3: 智能节点分配策略
        const nodeAllocation = this.allocatePortsToNodes(portTypeDAGs, currentUsage);
        
        // 步骤4: 验证无自环配置
        const validatedNodes = this.validateNoSelfLoops(nodeAllocation);
        
        // 步骤5: 平衡端口类型
        const balancedNodes = this.balancePortTypes(validatedNodes, currentUsage);
        
        console.log('[PORT-DAG] Generated', balancedNodes.length, 'nodes with port-type DAG constraints');
        return balancedNodes;
    }

    // ========== 端口类型使用分析 ==========
    
    analyzePortTypeUsage(nodes, connections) {
        const usage = {
            portTypeCounts: new Map(),
            nodePortAssignments: new Map(),
            imbalances: new Map(),
            riskNodes: new Set() // 有自环风险的节点
        };
        
        // 分析每种端口类型的使用情况
        this.portTypes.forEach(type => {
            this.portColors.forEach(color => {
                const key = `${type}-${color}`;
                usage.portTypeCounts.set(key, { inputs: 0, outputs: 0, balance: 0 });
            });
        });
        
        // 统计现有端口
        nodes.forEach(node => {
            const nodePortTypes = new Set();
            
            // 统计输入端口
            (node.inputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                const count = usage.portTypeCounts.get(key);
                count.inputs++;
                nodePortTypes.add(key);
            });
            
            // 统计输出端口
            (node.outputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                const count = usage.portTypeCounts.get(key);
                count.outputs++;
                
                // 检查自环风险
                if (nodePortTypes.has(key)) {
                    usage.riskNodes.add(node.id);
                    console.warn('[PORT-DAG] Self-loop risk detected in node', node.id, 'for port type', key);
                }
            });
            
            usage.nodePortAssignments.set(node.id, Array.from(nodePortTypes));
        });
        
        // 计算不平衡
        usage.portTypeCounts.forEach((count, key) => {
            count.balance = count.outputs - count.inputs;
            if (count.balance !== 0) {
                usage.imbalances.set(key, count.balance);
            }
        });
        
        return usage;
    }

    // ========== 为每种端口类型生成独立DAG ==========
    
    generateIndependentDAGs(currentUsage, config) {
        const portTypeDAGs = new Map();
        
        // 为每种端口类型生成DAG
        currentUsage.portTypeCounts.forEach((count, portTypeKey) => {
            const dag = this.generateSinglePortTypeDAG(portTypeKey, count, config);
            portTypeDAGs.set(portTypeKey, dag);
        });
        
        return portTypeDAGs;
    }

    generateSinglePortTypeDAG(portTypeKey, currentCount, config) {
        console.log('[PORT-DAG] Generating DAG for port type', portTypeKey);
        
        const dag = {
            portType: portTypeKey,
            nodes: new Map(), // nodeId -> {role: 'source'|'sink'|'intermediate', depth: number}
            edges: new Set(), // 'fromNode->toNode'
            requiredPorts: { inputs: 0, outputs: 0 }
        };
        
        // 计算需要生成的端口数量
        const targetBalance = this.calculateTargetBalance(currentCount, config);
        
        // 生成DAG结构
        if (targetBalance.needsMore > 0) {
            this.generateExpansionDAG(dag, targetBalance, config);
        } else if (targetBalance.needsLess > 0) {
            this.generateContractionDAG(dag, targetBalance, config);
        } else {
            this.generateNeutralDAG(dag, config);
        }
        
        return dag;
    }

    calculateTargetBalance(currentCount, config) {
        const imbalance = currentCount.balance;
        const targetNodes = config.targetNodesPerPortType || 2;
        
        return {
            needsMore: Math.max(0, -imbalance), // 需要更多输入端口
            needsLess: Math.max(0, imbalance),  // 需要更多输出端口
            targetNodes: targetNodes,
            currentImbalance: imbalance
        };
    }

    generateExpansionDAG(dag, balance, config) {
        // 需要更多输入端口 - 生成更多汇节点
        const nodeCount = Math.max(2, balance.targetNodes);
        
        // 创建源节点
        dag.nodes.set('source_0', { role: 'source', depth: 0 });
        dag.requiredPorts.outputs++;
        
        // 创建中间节点
        for (let i = 1; i < nodeCount - 1; i++) {
            dag.nodes.set(`intermediate_${i}`, { role: 'intermediate', depth: i });
            dag.requiredPorts.inputs++;
            dag.requiredPorts.outputs++;
            
            // 连接到前一个节点
            dag.edges.add(`${i === 1 ? 'source_0' : `intermediate_${i-1}`}->intermediate_${i}`);
        }
        
        // 创建汇节点
        for (let i = 0; i < balance.needsMore; i++) {
            const sinkId = `sink_${i}`;
            dag.nodes.set(sinkId, { role: 'sink', depth: nodeCount - 1 });
            dag.requiredPorts.inputs++;
            
            // 连接到最后一个中间节点或源节点
            const sourceNode = nodeCount > 2 ? `intermediate_${nodeCount-2}` : 'source_0';
            dag.edges.add(`${sourceNode}->${sinkId}`);
        }
    }

    generateContractionDAG(dag, balance, config) {
        // 需要更多输出端口 - 生成更多源节点
        const nodeCount = Math.max(2, balance.targetNodes);
        
        // 创建多个源节点
        for (let i = 0; i < balance.needsLess; i++) {
            const sourceId = `source_${i}`;
            dag.nodes.set(sourceId, { role: 'source', depth: 0 });
            dag.requiredPorts.outputs++;
        }
        
        // 创建中间节点
        for (let i = 1; i < nodeCount - 1; i++) {
            dag.nodes.set(`intermediate_${i}`, { role: 'intermediate', depth: i });
            dag.requiredPorts.inputs++;
            dag.requiredPorts.outputs++;
        }
        
        // 创建汇节点
        dag.nodes.set('sink_0', { role: 'sink', depth: nodeCount - 1 });
        dag.requiredPorts.inputs++;
        
        // 连接源节点到第一个中间节点
        for (let i = 0; i < balance.needsLess; i++) {
            const targetNode = nodeCount > 2 ? 'intermediate_1' : 'sink_0';
            dag.edges.add(`source_${i}->${targetNode}`);
        }
        
        // 连接中间节点
        for (let i = 1; i < nodeCount - 2; i++) {
            dag.edges.add(`intermediate_${i}->intermediate_${i+1}`);
        }
        
        // 连接最后一个中间节点到汇节点
        if (nodeCount > 2) {
            dag.edges.add(`intermediate_${nodeCount-2}->sink_0`);
        }
    }

    generateNeutralDAG(dag, config) {
        // 平衡状态 - 生成简单的链式DAG
        const nodeCount = config.targetNodesPerPortType || 2;
        
        dag.nodes.set('source_0', { role: 'source', depth: 0 });
        dag.requiredPorts.outputs++;
        
        for (let i = 1; i < nodeCount - 1; i++) {
            dag.nodes.set(`intermediate_${i}`, { role: 'intermediate', depth: i });
            dag.requiredPorts.inputs++;
            dag.requiredPorts.outputs++;
            dag.edges.add(`${i === 1 ? 'source_0' : `intermediate_${i-1}`}->intermediate_${i}`);
        }
        
        dag.nodes.set('sink_0', { role: 'sink', depth: nodeCount - 1 });
        dag.requiredPorts.inputs++;
        
        const sourceNode = nodeCount > 2 ? `intermediate_${nodeCount-2}` : 'source_0';
        dag.edges.add(`${sourceNode}->sink_0`);
    }

    // ========== 智能节点分配策略 ==========
    
    allocatePortsToNodes(portTypeDAGs, currentUsage) {
        console.log('[PORT-DAG] Allocating ports to nodes using intelligent strategy');
        
        const nodeAllocation = new Map(); // nodeId -> {inputPorts: [], outputPorts: []}
        const globalNodeCounter = { count: 0 };
        
        // 策略1: 避免自环配置
        const allocationStrategy = this.createAntiSelfLoopStrategy(currentUsage);
        
        // 为每种端口类型分配节点
        portTypeDAGs.forEach((dag, portTypeKey) => {
            this.allocatePortTypeToNodes(dag, portTypeKey, nodeAllocation, allocationStrategy, globalNodeCounter);
        });
        
        // 转换为节点对象
        const nodes = this.convertAllocationToNodes(nodeAllocation);
        
        return nodes;
    }

    createAntiSelfLoopStrategy(currentUsage) {
        return {
            // 避免在同一节点上放置相同类型的输入输出端口
            avoidSelfLoop: true,
            
            // 优先使用现有的不平衡节点
            preferExistingImbalanced: true,
            
            // 中间节点的输入输出尽量使用相同的端口类型集合
            intermediateConsistency: true,
            
            // 风险节点列表
            riskNodes: currentUsage.riskNodes
        };
    }

    allocatePortTypeToNodes(dag, portTypeKey, nodeAllocation, strategy, globalNodeCounter) {
        const [portType, portColor] = portTypeKey.split('-');
        
        dag.nodes.forEach((nodeInfo, dagNodeId) => {
            // 为每个DAG节点创建实际的游戏节点
            const actualNodeId = `${portTypeKey}_${dagNodeId}_${globalNodeCounter.count++}`;
            
            if (!nodeAllocation.has(actualNodeId)) {
                nodeAllocation.set(actualNodeId, {
                    id: actualNodeId,
                    type: this.determineNodeType(nodeInfo.role),
                    depth: nodeInfo.depth,
                    inputPorts: [],
                    outputPorts: []
                });
            }
            
            const node = nodeAllocation.get(actualNodeId);
            
            // 根据角色分配端口
            if (nodeInfo.role === 'source' || nodeInfo.role === 'intermediate') {
                node.outputPorts.push({
                    id: `${actualNodeId}_out_${portTypeKey}`,
                    type: portType,
                    color: portColor,
                    side: 'output'
                });
            }
            
            if (nodeInfo.role === 'sink' || nodeInfo.role === 'intermediate') {
                node.inputPorts.push({
                    id: `${actualNodeId}_in_${portTypeKey}`,
                    type: portType,
                    color: portColor,
                    side: 'input'
                });
            }
        });
    }

    determineNodeType(role) {
        switch (role) {
            case 'source': return 'start';
            case 'sink': return 'end';
            case 'intermediate': return 'intermediate';
            default: return 'intermediate';
        }
    }

    convertAllocationToNodes(nodeAllocation) {
        const nodes = [];
        
        nodeAllocation.forEach((nodeData, nodeId) => {
            nodes.push({
                id: nodeId,
                type: nodeData.type,
                depth: nodeData.depth,
                inputPorts: nodeData.inputPorts,
                outputPorts: nodeData.outputPorts,
                x: 50, // 临时位置
                y: 50 + nodes.length * 80,
                area: 'temporary'
            });
        });
        
        return nodes;
    }

    // ========== 验证无自环配置 ==========
    
    validateNoSelfLoops(nodes) {
        console.log('[PORT-DAG] Validating no self-loop configurations');
        
        const validatedNodes = [];
        
        nodes.forEach(node => {
            const inputPortTypes = new Set();
            const outputPortTypes = new Set();
            
            // 收集端口类型
            (node.inputPorts || []).forEach(port => {
                inputPortTypes.add(`${port.type}-${port.color}`);
            });
            
            (node.outputPorts || []).forEach(port => {
                outputPortTypes.add(`${port.type}-${port.color}`);
            });
            
            // 检查重叠
            const overlap = new Set([...inputPortTypes].filter(x => outputPortTypes.has(x)));
            
            if (overlap.size > 0) {
                console.warn('[PORT-DAG] Self-loop risk detected in node', node.id, 'for types:', Array.from(overlap));
                
                // 修复策略：移除输出端口中的重叠类型
                node.outputPorts = node.outputPorts.filter(port => {
                    const portTypeKey = `${port.type}-${port.color}`;
                    return !overlap.has(portTypeKey);
                });
                
                console.log('[PORT-DAG] Fixed self-loop risk by removing output ports');
            }
            
            // 只保留有端口的节点
            if ((node.inputPorts && node.inputPorts.length > 0) || 
                (node.outputPorts && node.outputPorts.length > 0)) {
                validatedNodes.push(node);
            }
        });
        
        return validatedNodes;
    }

    // ========== 平衡端口类型 ==========
    
    balancePortTypes(nodes, currentUsage) {
        console.log('[PORT-DAG] Balancing port types');
        
        // 重新计算端口类型统计
        const newCounts = new Map();
        this.portTypes.forEach(type => {
            this.portColors.forEach(color => {
                const key = `${type}-${color}`;
                newCounts.set(key, { inputs: 0, outputs: 0 });
            });
        });
        
        // 统计新节点的端口
        nodes.forEach(node => {
            (node.inputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                newCounts.get(key).inputs++;
            });
            
            (node.outputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                newCounts.get(key).outputs++;
            });
        });
        
        // 添加当前使用情况
        currentUsage.portTypeCounts.forEach((currentCount, key) => {
            const newCount = newCounts.get(key);
            newCount.inputs += currentCount.inputs;
            newCount.outputs += currentCount.outputs;
        });
        
        // 平衡调整
        const balancedNodes = this.adjustForBalance(nodes, newCounts);
        
        return balancedNodes;
    }

    adjustForBalance(nodes, counts) {
        const adjustedNodes = [...nodes];
        
        counts.forEach((count, portTypeKey) => {
            const imbalance = count.outputs - count.inputs;
            
            if (imbalance > 0) {
                // 需要更多输入端口
                this.addInputPorts(adjustedNodes, portTypeKey, imbalance);
            } else if (imbalance < 0) {
                // 需要更多输出端口
                this.addOutputPorts(adjustedNodes, portTypeKey, -imbalance);
            }
        });
        
        return adjustedNodes;
    }

    addInputPorts(nodes, portTypeKey, count) {
        const [portType, portColor] = portTypeKey.split('-');
        const eligibleNodes = nodes.filter(n => n.type === 'intermediate' || n.type === 'end');
        
        for (let i = 0; i < count && i < eligibleNodes.length; i++) {
            const node = eligibleNodes[i];
            
            // 检查是否会造成自环
            const hasOutputOfSameType = node.outputPorts.some(p => 
                `${p.type}-${p.color}` === portTypeKey
            );
            
            if (!hasOutputOfSameType) {
                node.inputPorts.push({
                    id: `${node.id}_in_balance_${portTypeKey}_${Date.now()}`,
                    type: portType,
                    color: portColor,
                    side: 'input'
                });
            }
        }
    }

    addOutputPorts(nodes, portTypeKey, count) {
        const [portType, portColor] = portTypeKey.split('-');
        const eligibleNodes = nodes.filter(n => n.type === 'start' || n.type === 'intermediate');
        
        for (let i = 0; i < count && i < eligibleNodes.length; i++) {
            const node = eligibleNodes[i];
            
            // 检查是否会造成自环
            const hasInputOfSameType = node.inputPorts.some(p => 
                `${p.type}-${p.color}` === portTypeKey
            );
            
            if (!hasInputOfSameType) {
                node.outputPorts.push({
                    id: `${node.id}_out_balance_${portTypeKey}_${Date.now()}`,
                    type: portType,
                    color: portColor,
                    side: 'output'
                });
            }
        }
    }

    // ========== 验证端口类型DAG ==========
    
    validatePortTypeDAGs(nodes, connections) {
        console.log('[PORT-DAG] Validating port type DAGs');
        
        const validation = {
            allValid: true,
            portTypeResults: new Map(),
            selfLoopNodes: new Set(),
            issues: []
        };
        
        // 为每种端口类型验证DAG
        this.portTypes.forEach(type => {
            this.portColors.forEach(color => {
                const key = `${type}-${color}`;
                const result = this.validateSinglePortTypeDAG(key, nodes, connections);
                validation.portTypeResults.set(key, result);
                
                if (!result.isDAG) {
                    validation.allValid = false;
                    validation.issues.push(`Port type ${key} forms cycles`);
                }
                
                result.selfLoopNodes.forEach(nodeId => {
                    validation.selfLoopNodes.add(nodeId);
                });
            });
        });
        
        return validation;
    }

    validateSinglePortTypeDAG(portTypeKey, nodes, connections) {
        const result = {
            portType: portTypeKey,
            isDAG: true,
            cycles: [],
            selfLoopNodes: new Set(),
            nodeCount: 0,
            edgeCount: 0
        };
        
        // 构建该端口类型的子图
        const subgraph = this.buildPortTypeSubgraph(portTypeKey, nodes, connections);
        result.nodeCount = subgraph.nodes.size;
        result.edgeCount = subgraph.edges.size;
        
        // 检查自环
        subgraph.nodes.forEach(nodeId => {
            const node = nodes.find(n => n.id === nodeId);
            if (node) {
                const hasInput = node.inputPorts.some(p => `${p.type}-${p.color}` === portTypeKey);
                const hasOutput = node.outputPorts.some(p => `${p.type}-${p.color}` === portTypeKey);
                
                if (hasInput && hasOutput) {
                    result.selfLoopNodes.add(nodeId);
                    result.isDAG = false;
                }
            }
        });
        
        // 检查环路（简化实现）
        if (result.isDAG) {
            result.isDAG = this.detectCyclesInSubgraph(subgraph);
        }
        
        return result;
    }

    buildPortTypeSubgraph(portTypeKey, nodes, connections) {
        const subgraph = {
            nodes: new Set(),
            edges: new Set()
        };
        
        // 找到使用该端口类型的节点
        nodes.forEach(node => {
            const hasPortType = [...(node.inputPorts || []), ...(node.outputPorts || [])].some(port => 
                `${port.type}-${port.color}` === portTypeKey
            );
            
            if (hasPortType) {
                subgraph.nodes.add(node.id);
            }
        });
        
        // 找到该端口类型的连接
        connections.forEach(conn => {
            // 这里需要根据连接的端口类型来判断
            // 简化实现：假设连接信息包含端口类型
            if (conn.portType === portTypeKey) {
                subgraph.edges.add(`${conn.fromNode}->${conn.toNode}`);
            }
        });
        
        return subgraph;
    }

    detectCyclesInSubgraph(subgraph) {
        // 使用DFS检测环路
        const visited = new Set();
        const recursionStack = new Set();

        // 构建邻接表
        const adjacencyList = new Map();
        subgraph.nodes.forEach(node => {
            adjacencyList.set(node, []);
        });

        subgraph.edges.forEach(edge => {
            const [from, to] = edge.split('->');
            if (adjacencyList.has(from)) {
                adjacencyList.get(from).push(to);
            }
        });

        // 对每个未访问的节点进行DFS
        for (const node of subgraph.nodes) {
            if (!visited.has(node)) {
                if (this.dfsHasCycle(node, adjacencyList, visited, recursionStack)) {
                    return false; // 发现环路
                }
            }
        }

        return true; // 无环
    }

    dfsHasCycle(node, adjacencyList, visited, recursionStack) {
        visited.add(node);
        recursionStack.add(node);

        const neighbors = adjacencyList.get(node) || [];
        for (const neighbor of neighbors) {
            if (!visited.has(neighbor)) {
                if (this.dfsHasCycle(neighbor, adjacencyList, visited, recursionStack)) {
                    return true;
                }
            } else if (recursionStack.has(neighbor)) {
                return true; // 发现后向边，存在环路
            }
        }

        recursionStack.delete(node);
        return false;
    }
}

// ========== 导出 ==========
window.PortTypeDAGEngine = PortTypeDAGEngine;
