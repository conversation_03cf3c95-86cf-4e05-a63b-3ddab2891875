# 蓝图连接游戏 - 优化设计文档

## 一、核心概念定义

### 1.1 基础元素

#### Port（端口）
```typescript
interface Port {
  id: string;
  type: PortType;         // 形状 + 颜色的组合
  direction: 'input' | 'output';
  nodeId: string;         // 所属节点
  position: number;       // 在节点上的位置索引
  connectedTo?: string;   // 连接的对端端口ID
}

interface PortType {
  shape: Shape;    // 方形、菱形、三角形等
  color: Color;    // 红、绿、蓝等
}
```

#### Node（节点）
```typescript
interface Node {
  id: string;
  type: 'start' | 'end' | 'middle';
  inputPorts: Port[];     // 左侧端口
  outputPorts: Port[];    // 右侧端口
  position: Vector2;      // 在画布上的位置
}
```

### 1.2 连接规则

1. **类型匹配**：只有相同PortType的端口才能连接
2. **方向匹配**：output端口只能连接到input端口
3. **唯一性**：每个端口最多只能有一个连接
4. **无环约束**：连接不能形成环路
5. **深度约束**：连接只能从低深度节点指向高深度节点



### 2.2 强可解性
节点池满足以下条件时为**强可解**：
1. 存在至少一种连接方案，使得：
   - 所有端口都被连接
   - 每一种端口的连接都是DAG
   - 每个节点都在从起点到终点的路径上
   - 形成的node图是DAG

### 2.3 端口平衡约束
对于每种PortType τ：
```
可用输出端口数(τ) ≥ 必需输入端口数(τ)
```
其中：
- 可用输出端口数 = 起点和中间节点的该类型输出端口总数
- 必需输入端口数 = 终点的该类型输入端口数 + 中间节点的最小连接需求

## 三、节点生成算法

### 3.1 基础生成算法
```typescript
interface GenerationConfig {
  startNodes: number;      // 起点数量
  endNodes: number;        // 终点数量
  middleNodes: number;     // 中间节点数量
  portTypes: PortType[];   // 可用端口类型
  maxPortsPerSide: number; // 单侧最大端口数
  difficulty: number;      // 难度系数 [0, 1]
}



## 四、游戏模式实现


### 4.3 模式3：无限构筑模式（重点优化）
```typescript
class InfiniteMode {
  private currentGraph: Node[] = [];
  private wave: number = 1;
  
  async startWave() {
    if (this.wave === 1) {
      // 第一波：生成初始图
      this.currentGraph = generateSolvableNodePool({
        startNodes: 1,
        endNodes: 1,
        middleNodes: 3,
        portTypes: getBasicPortTypes(),
        maxPortsPerSide: 2,
        difficulty: 0.1
      });
    } else {
      // 后续波次：基于现有图进行扩展
      const modification = this.generateModification();
      this.currentGraph = await this.applyModification(this.currentGraph, modification);
    }
    
    // 等待玩家完成新的连接
    const success = await this.waitForPlayerSolution();
    if (success) {
      this.wave++;
      this.startWave();
    }
  }
  
  private generateModification(): GraphModification {
    const modifications = [];
    
    // 1. 可能添加新的起点/终点（概率随波次增加）
    if (Math.random() < 0.3 + this.wave * 0.05) {
      modifications.push({
        type: 'addNode',
        nodeType: Math.random() < 0.5 ? 'start' : 'end',
        portCount: 1 + Math.floor(Math.random() * 3)
      });
    }
    
    // 2. 添加新的中间节点
    const newNodeCount = Math.floor(1 + Math.random() * (1 + this.wave / 5));
    for (let i = 0; i < newNodeCount; i++) {
      modifications.push({
        type: 'addNode',
        nodeType: 'middle',
        inputPorts: 1 + Math.floor(Math.random() * 3),
        outputPorts: 1 + Math.floor(Math.random() * 3)
      });
    }
    
    // 3. 修改现有节点（添加端口，不删除）
    const nodesToModify = Math.floor(this.currentGraph.length * 0.2);
    for (let i = 0; i < nodesToModify; i++) {
      const node = this.selectNodeForModification();
      if (node && this.canAddPortsToNode(node)) {
        modifications.push({
          type: 'modifyNode',
          nodeId: node.id,
          addInputPorts: Math.floor(Math.random() * 2),
          addOutputPorts: Math.floor(Math.random() * 2)
        });
      }
    }
    
    return modifications;
  }
  
  private async applyModification(
    currentGraph: Node[], 
    modifications: GraphModification[]
  ): Promise<Node[]> {
    // 1. 保存当前连接状态
    const currentConnections = this.saveConnections(currentGraph);
    
    // 2. 应用修改
    let modifiedGraph = [...currentGraph];
    for (const mod of modifications) {
      modifiedGraph = this.applySimpleModification(modifiedGraph, mod);
    }
    
    // 3. 确保修改后的图仍然可解
    const newNodes = this.ensureSolvability(modifiedGraph, currentConnections);
    
    // 4. 标记被影响的连接
    this.markInvalidConnections(newNodes, currentConnections);
    
    return newNodes;
  }
  
  private ensureSolvability(
    modifiedGraph: Node[], 
    existingConnections: Connection[]
  ): Node[] {
    // 1. 重新计算深度层级
    this.recalculateDepths(modifiedGraph);
    
    // 2. 分析端口类型需求
    const portDeficit = this.analyzePortDeficit(modifiedGraph, existingConnections);
    
    // 3. 添加平衡节点以确保可解性
    if (portDeficit.length > 0) {
      const balancingNodes = this.generateBalancingNodes(portDeficit);
      modifiedGraph.push(...balancingNodes);
    }
    
    // 4. 最终验证
    if (!this.verifySolvabilityWithPartialConnections(modifiedGraph, existingConnections)) {
      throw new Error("Failed to maintain solvability");
    }
    
    return modifiedGraph;
  }
}
```

## 五、关键算法优化

### 5.1 端口类型生成策略
```typescript
function getPortTypesForLevel(level: number): PortType[] {
  const shapes = ['square', 'diamond', 'triangle'];
  const colors = ['red', 'green', 'blue'];
  
  // 根据关卡逐步引入新的端口类型
  const maxTypes = Math.min(2 + Math.floor(level / 3), shapes.length * colors.length);
  const types: PortType[] = [];
  
  for (let i = 0; i < maxTypes; i++) {
    types.push({
      shape: shapes[i % shapes.length],
      color: colors[Math.floor(i / shapes.length)]
    });
  }
  
  return types;
}
```

### 5.2 深度分配优化
```typescript
function assignNodeDepths(nodes: Node[]): void {
  // 使用拓扑排序确定最优深度
  const graph = buildAdjacencyList(nodes);
  const depths = topologicalSort(graph);
  
  // 确保起点在深度0，终点在最大深度
  normalizeDepths(nodes, depths);
}
```

### 5.3 验证算法优化
```typescript
function fastSolvabilityCheck(nodes: Node[]): boolean {
  // 1. 快速检查：端口类型计数
  const portCounts = countPortsByType(nodes);
  for (const type of Object.keys(portCounts)) {
    if (portCounts[type].output < portCounts[type].requiredInput) {
      return false;
    }
  }
  
  // 2. 快速检查：层级连通性
  if (!checkLayerConnectivity(nodes)) {
    return false;
  }
  
  // 3. 详细检查：尝试构造解
  return attemptGreedySolution(nodes) !== null;
}
```

## 六、实现建议

### 6.1 数据结构选择
- 使用邻接表表示图结构，便于快速遍历
- 使用Map存储端口ID到端口对象的映射，加速查找
- 使用优先队列实现贪心算法中的端口匹配

### 6.2 性能优化
- 缓存端口类型统计结果
- 使用增量式验证算法（只验证变化部分）
- 预生成节点池，避免游戏过程中的计算延迟

### 6.3 用户体验优化
- 提供连接提示（高亮可连接的端口）
- 实时验证连接合法性（防止形成环）
- 流动动画预览，帮助玩家理解数据流向

## 七、测试策略

### 7.1 单元测试
- 测试各种配置下的节点生成算法
- 测试可解性验证算法的正确性
- 测试图修改操作的一致性

### 7.2 集成测试
- 测试完整的游戏流程
- 测试极端情况（大量节点、复杂连接）
- 测试错误恢复机制

### 7.3 性能测试
- 测试生成算法的时间复杂度
- 测试验证算法的响应时间
- 测试内存使用情况

## 八、扩展性考虑

### 8.1 新增端口类型
设计支持动态添加新端口类型的接口，包括：
- 自定义形状和颜色
- 特殊连接规则（如一对多连接）
- 条件连接（需要满足特定条件）

### 8.2 新增节点类型
- 分支节点（条件判断）
- 循环节点（有限次循环，不违反DAG）
- 转换节点（改变端口类型）

### 8.3 难度调节
- 动态难度调整（根据玩家表现）
- 自定义难度参数
- 挑战模式（限制条件下的解谜）