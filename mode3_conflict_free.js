// ========== Mode 3 无冲突实现 ==========

// 游戏状态
const mode3ConflictFreeState = {
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    
    // Canvas引用
    temporaryCanvas: null,
    temporaryCtx: null,
    gameCanvas: null,
    gameCtx: null,
    
    // 交互状态
    draggingNode: null,
    selectedPort: null,
    
    // Mode 3 冲突解决状态
    currentWave: 1,
    conflictResolver: null,
    lastConflictAnalysis: null,
    
    // 统计信息
    conflictResolutions: [],
    portTypeValidations: new Map(),
    nodeAllocationHistory: []
};

// ========== 初始化 ==========

function initializeMode3ConflictFree() {
    console.log('[MODE3-CONFLICT-FREE] Initializing conflict-free mode');
    
    // 获取Canvas元素
    mode3ConflictFreeState.temporaryCanvas = document.getElementById('temporaryCanvas');
    mode3ConflictFreeState.gameCanvas = document.getElementById('gameCanvas');
    
    if (!mode3ConflictFreeState.temporaryCanvas || !mode3ConflictFreeState.gameCanvas) {
        console.error('[MODE3-CONFLICT-FREE] Canvas elements not found');
        return false;
    }
    
    mode3ConflictFreeState.temporaryCtx = mode3ConflictFreeState.temporaryCanvas.getContext('2d');
    mode3ConflictFreeState.gameCtx = mode3ConflictFreeState.gameCanvas.getContext('2d');
    
    // 初始化冲突解决器
    mode3ConflictFreeState.conflictResolver = new PortDAGConflictResolver();
    
    // 设置事件监听器
    setupMode3ConflictFreeEventListeners();
    
    // 生成初始状态
    generateInitialConflictFreeState();
    
    // 启动连续验证
    startConflictFreeValidation();
    
    updateMode3ConflictFreeDisplay();
    
    console.log('[MODE3-CONFLICT-FREE] Conflict-free mode initialized');
    return true;
}

function setupMode3ConflictFreeEventListeners() {
    [mode3ConflictFreeState.temporaryCanvas, mode3ConflictFreeState.gameCanvas].forEach(canvas => {
        canvas.addEventListener('mousedown', handleMode3ConflictFreeMouseDown);
        canvas.addEventListener('mousemove', handleMode3ConflictFreeMouseMove);
        canvas.addEventListener('mouseup', handleMode3ConflictFreeMouseUp);
    });
    
    document.addEventListener('keydown', handleMode3ConflictFreeKeyDown);
}

// ========== 初始状态生成 ==========

function generateInitialConflictFreeState() {
    console.log('[MODE3-CONFLICT-FREE] Generating initial conflict-free state');
    
    // 创建基础配置
    const config = {
        portTypeCount: 4,
        maxNodesPerPortType: 3,
        targetComplexity: 0.3,
        conflictTolerance: 0.0 // 零容忍冲突
    };
    
    // 使用冲突解决器生成节点
    try {
        const generatedNodes = mode3ConflictFreeState.conflictResolver.generateConflictFreeTopology(config);
        
        // 分配到临时区域
        positionConflictFreeNodesInTemporaryArea(generatedNodes);
        mode3ConflictFreeState.temporaryNodes = generatedNodes;
        
        // 记录分配历史
        recordNodeAllocation(generatedNodes, 'initial_generation');
        
        console.log('[MODE3-CONFLICT-FREE] Generated', generatedNodes.length, 'conflict-free nodes');
        
    } catch (error) {
        console.error('[MODE3-CONFLICT-FREE] Initial generation failed:', error);
        generateFallbackConflictFreeNodes();
    }
}

function positionConflictFreeNodesInTemporaryArea(nodes) {
    const margin = 20;
    const nodeSize = 60;
    const spacing = 80;
    
    nodes.forEach((node, index) => {
        node.x = margin + nodeSize/2;
        node.y = margin + nodeSize/2 + index * spacing;
        node.area = 'temporary';
    });
}

function generateFallbackConflictFreeNodes() {
    console.log('[MODE3-CONFLICT-FREE] Generating fallback conflict-free nodes');
    
    // 生成绝对无冲突的简单节点
    const fallbackNodes = [
        createConflictFreeNode('start_red_square', 'start', 0, [], [
            { type: 'square', color: 'red' }
        ]),
        createConflictFreeNode('mid_red_to_blue', 'intermediate', 1, [
            { type: 'square', color: 'red' }
        ], [
            { type: 'circle', color: 'blue' }
        ]),
        createConflictFreeNode('end_blue_circle', 'end', 2, [
            { type: 'circle', color: 'blue' }
        ], [])
    ];
    
    fallbackNodes.forEach((node, index) => {
        node.x = 50;
        node.y = 100 + index * 80;
        node.area = 'temporary';
    });
    
    mode3ConflictFreeState.temporaryNodes = fallbackNodes;
}

function createConflictFreeNode(id, type, depth, inputPortSpecs, outputPortSpecs) {
    const node = {
        id: id || `cf_node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: type,
        depth: depth,
        inputPorts: [],
        outputPorts: [],
        createdAt: Date.now(),
        conflictFree: true,
        portTypeSet: new Set()
    };
    
    // 创建输入端口
    inputPortSpecs.forEach((spec, index) => {
        const portTypeKey = `${spec.type}-${spec.color}`;
        node.portTypeSet.add(portTypeKey);
        
        node.inputPorts.push({
            id: `${node.id}_in_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'input',
            nodeId: node.id,
            portTypeKey: portTypeKey,
            x: 0,
            y: 0
        });
    });
    
    // 创建输出端口 - 确保与输入端口类型不重叠
    outputPortSpecs.forEach((spec, index) => {
        const portTypeKey = `${spec.type}-${spec.color}`;
        
        // 冲突检查
        if (node.portTypeSet.has(portTypeKey)) {
            console.warn('[MODE3-CONFLICT-FREE] Potential conflict detected, skipping output port', portTypeKey);
            return;
        }
        
        node.portTypeSet.add(portTypeKey);
        
        node.outputPorts.push({
            id: `${node.id}_out_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'output',
            nodeId: node.id,
            portTypeKey: portTypeKey,
            x: 0,
            y: 0
        });
    });
    
    return node;
}

// ========== 冲突解决扩展 ==========

function expandWithConflictResolution() {
    console.log('[MODE3-CONFLICT-FREE] Expanding with conflict resolution');
    
    try {
        // 收集当前状态
        const existingNodes = [...mode3ConflictFreeState.temporaryNodes, ...mode3ConflictFreeState.placedNodes];
        
        // 分析当前端口类型使用情况
        const currentUsage = analyzeCurrentPortTypeUsage(existingNodes);
        
        // 配置扩展参数
        const expansionConfig = {
            existingUsage: currentUsage,
            targetNewNodes: 2 + mode3ConflictFreeState.currentWave,
            conflictAvoidance: true,
            balancePriority: 0.9
        };
        
        // 使用冲突解决器生成新节点
        const newNodes = mode3ConflictFreeState.conflictResolver.generateConflictFreeTopology(expansionConfig);
        
        // 验证新节点不会与现有节点产生冲突
        const validatedNodes = validateNoConflictsWithExisting(newNodes, existingNodes);
        
        // 定位新节点到临时区域
        positionConflictFreeNodesInTemporaryArea(validatedNodes);
        
        // 添加到临时节点池
        mode3ConflictFreeState.temporaryNodes.push(...validatedNodes);
        
        // 记录分配历史
        recordNodeAllocation(validatedNodes, `wave_${mode3ConflictFreeState.currentWave}_expansion`);
        
        console.log('[MODE3-CONFLICT-FREE] Added', validatedNodes.length, 'conflict-free nodes');
        
    } catch (error) {
        console.error('[MODE3-CONFLICT-FREE] Expansion failed:', error);
        generateFallbackConflictFreeNodes();
    }
}

function analyzeCurrentPortTypeUsage(nodes) {
    const usage = {
        portTypeCounts: new Map(),
        nodePortAssignments: new Map(),
        conflictRisks: new Set()
    };
    
    // 统计每种端口类型的使用情况
    nodes.forEach(node => {
        const nodePortTypes = new Set();
        
        [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
            const portTypeKey = port.portTypeKey || `${port.type}-${port.color}`;
            nodePortTypes.add(portTypeKey);
            
            const count = usage.portTypeCounts.get(portTypeKey) || { inputs: 0, outputs: 0 };
            if (port.side === 'input') {
                count.inputs++;
            } else {
                count.outputs++;
            }
            usage.portTypeCounts.set(portTypeKey, count);
        });
        
        usage.nodePortAssignments.set(node.id, Array.from(nodePortTypes));
        
        // 检查节点内部冲突
        const inputTypes = new Set();
        const outputTypes = new Set();
        
        (node.inputPorts || []).forEach(port => {
            inputTypes.add(port.portTypeKey || `${port.type}-${port.color}`);
        });
        
        (node.outputPorts || []).forEach(port => {
            outputTypes.add(port.portTypeKey || `${port.type}-${port.color}`);
        });
        
        // 检查重叠
        inputTypes.forEach(type => {
            if (outputTypes.has(type)) {
                usage.conflictRisks.add(`${node.id}:${type}`);
            }
        });
    });
    
    return usage;
}

function validateNoConflictsWithExisting(newNodes, existingNodes) {
    const validatedNodes = [];
    
    newNodes.forEach(newNode => {
        let hasConflict = false;
        
        // 检查新节点内部是否有冲突
        const newNodeInputTypes = new Set();
        const newNodeOutputTypes = new Set();
        
        (newNode.inputPorts || []).forEach(port => {
            newNodeInputTypes.add(port.portTypeKey || `${port.type}-${port.color}`);
        });
        
        (newNode.outputPorts || []).forEach(port => {
            newNodeOutputTypes.add(port.portTypeKey || `${port.type}-${port.color}`);
        });
        
        // 检查内部重叠
        newNodeInputTypes.forEach(type => {
            if (newNodeOutputTypes.has(type)) {
                hasConflict = true;
                console.warn('[MODE3-CONFLICT-FREE] Internal conflict detected in new node', newNode.id, 'for type', type);
            }
        });
        
        if (!hasConflict) {
            validatedNodes.push(newNode);
        } else {
            console.log('[MODE3-CONFLICT-FREE] Rejecting conflicted node', newNode.id);
        }
    });
    
    return validatedNodes;
}

function recordNodeAllocation(nodes, operation) {
    const record = {
        operation: operation,
        timestamp: Date.now(),
        nodeCount: nodes.length,
        nodes: nodes.map(node => ({
            id: node.id,
            type: node.type,
            portTypes: Array.from(node.portTypeSet || []),
            conflictFree: node.conflictFree
        }))
    };
    
    mode3ConflictFreeState.nodeAllocationHistory.push(record);
}

// ========== 冲突验证 ==========

function startConflictFreeValidation() {
    setInterval(() => {
        validateConflictFreeState();
    }, 2000);
}

function validateConflictFreeState() {
    const allNodes = [...mode3ConflictFreeState.temporaryNodes, ...mode3ConflictFreeState.placedNodes];
    
    // 验证每个节点的冲突状态
    const validationResults = new Map();
    
    allNodes.forEach(node => {
        const result = validateNodeConflictFree(node);
        validationResults.set(node.id, result);
    });
    
    // 验证端口类型DAG
    const portTypeValidations = validatePortTypeDAGs(allNodes, mode3ConflictFreeState.connections);
    
    mode3ConflictFreeState.portTypeValidations = portTypeValidations;
    
    // 更新UI状态
    updateConflictFreeValidationStatus(validationResults, portTypeValidations);
}

function validateNodeConflictFree(node) {
    const result = {
        nodeId: node.id,
        isConflictFree: true,
        conflicts: [],
        portTypes: new Set()
    };
    
    const inputTypes = new Set();
    const outputTypes = new Set();
    
    // 收集端口类型
    (node.inputPorts || []).forEach(port => {
        const portTypeKey = port.portTypeKey || `${port.type}-${port.color}`;
        inputTypes.add(portTypeKey);
        result.portTypes.add(portTypeKey);
    });
    
    (node.outputPorts || []).forEach(port => {
        const portTypeKey = port.portTypeKey || `${port.type}-${port.color}`;
        outputTypes.add(portTypeKey);
        result.portTypes.add(portTypeKey);
    });
    
    // 检查重叠
    inputTypes.forEach(type => {
        if (outputTypes.has(type)) {
            result.isConflictFree = false;
            result.conflicts.push({
                type: 'self_connection_risk',
                portType: type,
                description: `Node has both input and output of type ${type}`
            });
        }
    });
    
    return result;
}

function validatePortTypeDAGs(nodes, connections) {
    const validations = new Map();
    
    // 为每种端口类型验证DAG
    const portTypes = new Set();
    nodes.forEach(node => {
        [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
            portTypes.add(port.portTypeKey || `${port.type}-${port.color}`);
        });
    });
    
    portTypes.forEach(portType => {
        const validation = validateSinglePortTypeDAG(portType, nodes, connections);
        validations.set(portType, validation);
    });
    
    return validations;
}

function validateSinglePortTypeDAG(portType, nodes, connections) {
    const result = {
        portType: portType,
        isDAG: true,
        nodeCount: 0,
        edgeCount: 0,
        selfLoopNodes: new Set(),
        cycles: []
    };
    
    // 构建该端口类型的子图
    const subgraphNodes = new Set();
    const subgraphEdges = new Set();
    
    // 找到使用该端口类型的节点
    nodes.forEach(node => {
        const hasPortType = [...(node.inputPorts || []), ...(node.outputPorts || [])].some(port => 
            (port.portTypeKey || `${port.type}-${port.color}`) === portType
        );
        
        if (hasPortType) {
            subgraphNodes.add(node.id);
            
            // 检查自环
            const hasInput = (node.inputPorts || []).some(port => 
                (port.portTypeKey || `${port.type}-${port.color}`) === portType
            );
            const hasOutput = (node.outputPorts || []).some(port => 
                (port.portTypeKey || `${port.type}-${port.color}`) === portType
            );
            
            if (hasInput && hasOutput) {
                result.selfLoopNodes.add(node.id);
                result.isDAG = false;
            }
        }
    });
    
    result.nodeCount = subgraphNodes.size;
    result.edgeCount = subgraphEdges.size;
    
    return result;
}

function updateConflictFreeValidationStatus(nodeValidations, portTypeValidations) {
    // 统计冲突节点
    let conflictNodes = 0;
    nodeValidations.forEach(result => {
        if (!result.isConflictFree) {
            conflictNodes++;
        }
    });
    
    // 统计无效的端口类型DAG
    let invalidPortTypes = 0;
    portTypeValidations.forEach(result => {
        if (!result.isDAG) {
            invalidPortTypes++;
        }
    });
    
    // 更新UI
    const statusElement = document.getElementById('conflict-free-status');
    if (statusElement) {
        const isAllValid = conflictNodes === 0 && invalidPortTypes === 0;
        statusElement.textContent = isAllValid ? 'All Conflict-Free' : `${conflictNodes} conflicts, ${invalidPortTypes} invalid DAGs`;
        statusElement.className = isAllValid ? 'status-good' : 'status-error';
    }
}

// ========== 渲染系统 ==========

function updateMode3ConflictFreeDisplay() {
    clearMode3ConflictFreeCanvases();
    drawMode3ConflictFreeNodes();
    drawMode3ConflictFreeConnections();
    drawMode3ConflictFreeUI();
}

function clearMode3ConflictFreeCanvases() {
    if (mode3ConflictFreeState.temporaryCtx) {
        mode3ConflictFreeState.temporaryCtx.clearRect(0, 0, mode3ConflictFreeState.temporaryCanvas.width, mode3ConflictFreeState.temporaryCanvas.height);
    }
    if (mode3ConflictFreeState.gameCtx) {
        mode3ConflictFreeState.gameCtx.clearRect(0, 0, mode3ConflictFreeState.gameCanvas.width, mode3ConflictFreeState.gameCanvas.height);
    }
}

function drawMode3ConflictFreeNodes() {
    // 绘制临时节点
    mode3ConflictFreeState.temporaryNodes.forEach(node => {
        drawMode3ConflictFreeNode(node, mode3ConflictFreeState.temporaryCtx);
    });
    
    // 绘制放置节点
    mode3ConflictFreeState.placedNodes.forEach(node => {
        drawMode3ConflictFreeNode(node, mode3ConflictFreeState.gameCtx);
    });
}

function drawMode3ConflictFreeNode(node, ctx) {
    const nodeSize = 60;
    const portSize = 12;
    
    // 检查冲突状态
    const isConflictFree = node.conflictFree !== false;
    
    // 绘制节点主体
    ctx.fillStyle = isConflictFree ? getMode3ConflictFreeNodeColor(node.type) : '#ff6b6b';
    ctx.fillRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制节点边框
    ctx.strokeStyle = isConflictFree ? '#fff' : '#ff0000';
    ctx.lineWidth = isConflictFree ? 2 : 3;
    ctx.strokeRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制端口
    drawMode3ConflictFreeNodePorts(node, ctx, nodeSize, portSize);
    
    // 绘制节点标签
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.id.substring(0, 8), node.x, node.y + 3);
    
    // 绘制冲突状态指示器
    if (isConflictFree) {
        ctx.fillStyle = '#00ff00';
        ctx.font = '12px Arial';
        ctx.fillText('✓', node.x + 20, node.y - 20);
    } else {
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.fillText('⚠', node.x + 20, node.y - 20);
    }
}

function drawMode3ConflictFreeNodePorts(node, ctx, nodeSize, portSize) {
    // 输入端口（左侧）
    if (node.inputPorts && node.inputPorts.length > 0) {
        const spacing = nodeSize / (node.inputPorts.length + 1);
        node.inputPorts.forEach((port, index) => {
            const portX = node.x - nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3ConflictFreePort(ctx, portX, portY, port, portSize);
        });
    }
    
    // 输出端口（右侧）
    if (node.outputPorts && node.outputPorts.length > 0) {
        const spacing = nodeSize / (node.outputPorts.length + 1);
        node.outputPorts.forEach((port, index) => {
            const portX = node.x + nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3ConflictFreePort(ctx, portX, portY, port, portSize);
        });
    }
}

function drawMode3ConflictFreePort(ctx, x, y, port, size) {
    ctx.fillStyle = port.color;
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 1;
    
    switch (port.type) {
        case 'square':
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.strokeRect(x - size/2, y - size/2, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x - size/2, y + size/2);
            ctx.lineTo(x + size/2, y + size/2);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x + size/2, y);
            ctx.lineTo(x, y + size/2);
            ctx.lineTo(x - size/2, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
}

function getMode3ConflictFreeNodeColor(type) {
    switch (type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        case 'intermediate': return '#2196F3';
        default: return '#666';
    }
}

function drawMode3ConflictFreeConnections() {
    mode3ConflictFreeState.connections.forEach(connection => {
        const fromPort = findConflictFreePortById(connection.fromPort);
        const toPort = findConflictFreePortById(connection.toPort);
        
        if (fromPort && toPort) {
            drawMode3ConflictFreeConnectionLine(mode3ConflictFreeState.gameCtx, fromPort, toPort);
        }
    });
}

function drawMode3ConflictFreeConnectionLine(ctx, fromPort, toPort) {
    ctx.strokeStyle = fromPort.color;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(fromPort.x, fromPort.y);
    ctx.lineTo(toPort.x, toPort.y);
    ctx.stroke();
}

function drawMode3ConflictFreeUI() {
    const ctx = mode3ConflictFreeState.gameCtx;
    
    // 绘制状态信息
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Wave: ${mode3ConflictFreeState.currentWave}`, 10, 25);
    ctx.fillText(`Conflict Resolutions: ${mode3ConflictFreeState.conflictResolutions.length}`, 10, 45);
    ctx.fillText(`Conflict-Free Mode: Active`, 10, 65);
}

// ========== 事件处理 ==========

function handleMode3ConflictFreeMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const isTemporaryArea = event.target === mode3ConflictFreeState.temporaryCanvas;
    
    // 检查端口点击
    const clickedPort = findConflictFreePortAtPosition(x, y, isTemporaryArea);
    if (clickedPort) {
        handleMode3ConflictFreePortClick(clickedPort);
        return;
    }
    
    // 检查节点点击
    const clickedNode = findConflictFreeNodeAtPosition(x, y, isTemporaryArea);
    if (clickedNode) {
        startMode3ConflictFreeNodeDrag(clickedNode, x, y);
    }
}

function handleMode3ConflictFreeMouseMove(event) {
    if (mode3ConflictFreeState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        mode3ConflictFreeState.draggingNode.node.x = x;
        mode3ConflictFreeState.draggingNode.node.y = y;
        updateMode3ConflictFreeDisplay();
    }
}

function handleMode3ConflictFreeMouseUp(event) {
    if (mode3ConflictFreeState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        finishMode3ConflictFreeNodeDrag(x, y, event.target === mode3ConflictFreeState.gameCanvas);
        mode3ConflictFreeState.draggingNode = null;
        updateMode3ConflictFreeDisplay();
    }
}

function handleMode3ConflictFreeKeyDown(event) {
    switch (event.key) {
        case 'n':
        case 'N':
            nextConflictFreeWave();
            break;
        case 'v':
        case 'V':
            validateConflictFreeState();
            break;
        case 'r':
        case 'R':
            regenerateConflictFreeNodes();
            break;
        case 'a':
        case 'A':
            analyzeConflictResolution();
            break;
    }
}

// ========== 辅助函数 ==========

function findConflictFreeNodeAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3ConflictFreeState.temporaryNodes : mode3ConflictFreeState.placedNodes;
    const nodeSize = 60;
    
    for (const node of nodes) {
        if (x >= node.x - nodeSize/2 && x <= node.x + nodeSize/2 &&
            y >= node.y - nodeSize/2 && y <= node.y + nodeSize/2) {
            return node;
        }
    }
    return null;
}

function findConflictFreePortAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3ConflictFreeState.temporaryNodes : mode3ConflictFreeState.placedNodes;
    const portSize = 12;
    
    for (const node of nodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.x !== undefined && port.y !== undefined) {
                if (x >= port.x - portSize/2 && x <= port.x + portSize/2 &&
                    y >= port.y - portSize/2 && y <= port.y + portSize/2) {
                    return port;
                }
            }
        }
    }
    return null;
}

function findConflictFreePortById(portId) {
    const allNodes = [...mode3ConflictFreeState.temporaryNodes, ...mode3ConflictFreeState.placedNodes];
    for (const node of allNodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        const port = allPorts.find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

function startMode3ConflictFreeNodeDrag(node, x, y) {
    mode3ConflictFreeState.draggingNode = {
        node: node,
        offsetX: x - node.x,
        offsetY: y - node.y
    };
}

function finishMode3ConflictFreeNodeDrag(x, y, isGameArea) {
    const node = mode3ConflictFreeState.draggingNode.node;
    
    if (isGameArea) {
        // 移动到游戏区域
        if (mode3ConflictFreeState.temporaryNodes.includes(node)) {
            mode3ConflictFreeState.temporaryNodes = mode3ConflictFreeState.temporaryNodes.filter(n => n !== node);
            mode3ConflictFreeState.placedNodes.push(node);
            node.area = 'placed';
        }
    } else {
        // 移动回临时区域
        if (mode3ConflictFreeState.placedNodes.includes(node)) {
            mode3ConflictFreeState.placedNodes = mode3ConflictFreeState.placedNodes.filter(n => n !== node);
            mode3ConflictFreeState.temporaryNodes.push(node);
            node.area = 'temporary';
        }
    }
}

function handleMode3ConflictFreePortClick(port) {
    if (!mode3ConflictFreeState.selectedPort) {
        mode3ConflictFreeState.selectedPort = port;
        console.log('[MODE3-CONFLICT-FREE] Selected port', port.id);
    } else {
        if (canConnectConflictFreePorts(mode3ConflictFreeState.selectedPort, port)) {
            createConflictFreeConnection(mode3ConflictFreeState.selectedPort, port);
        }
        mode3ConflictFreeState.selectedPort = null;
    }
}

function canConnectConflictFreePorts(fromPort, toPort) {
    // 检查端口类型匹配
    const fromType = fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`;
    const toType = toPort.portTypeKey || `${toPort.type}-${toPort.color}`;
    
    return fromType === toType && 
           fromPort.side !== toPort.side && 
           fromPort.nodeId !== toPort.nodeId;
}

function createConflictFreeConnection(fromPort, toPort) {
    const connection = {
        id: `conn_${Date.now()}`,
        fromNode: fromPort.nodeId,
        fromPort: fromPort.id,
        toNode: toPort.nodeId,
        toPort: toPort.id,
        portType: fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`
    };
    
    mode3ConflictFreeState.connections.push(connection);
    console.log('[MODE3-CONFLICT-FREE] Created connection', connection.id);
    updateMode3ConflictFreeDisplay();
}

function nextConflictFreeWave() {
    mode3ConflictFreeState.currentWave++;
    expandWithConflictResolution();
    updateMode3ConflictFreeDisplay();
}

function regenerateConflictFreeNodes() {
    mode3ConflictFreeState.temporaryNodes = [];
    expandWithConflictResolution();
    updateMode3ConflictFreeDisplay();
}

function analyzeConflictResolution() {
    if (mode3ConflictFreeState.conflictResolver) {
        const analysis = mode3ConflictFreeState.conflictResolver.analyzeConflictResolution(new Map());
        
        const report = [
            '=== Conflict Resolution Analysis ===',
            `Total Nodes: ${analysis.totalNodes}`,
            `Nodes by Role:`,
            ...Array.from(analysis.nodesByRole.entries()).map(([role, count]) => `  ${role}: ${count}`),
            `Port Type Distribution:`,
            ...Array.from(analysis.portTypeDistribution.entries()).map(([type, count]) => `  ${type}: ${count} nodes`),
            `Conflict Resolutions: ${analysis.conflictResolutions.length}`
        ];
        
        alert(report.join('\n'));
    }
}

// ========== 全局函数 ==========

window.initializeMode3ConflictFree = initializeMode3ConflictFree;
window.updateMode3ConflictFreeDisplay = updateMode3ConflictFreeDisplay;
window.nextConflictFreeWave = nextConflictFreeWave;
window.validateConflictFreeState = validateConflictFreeState;
window.regenerateConflictFreeNodes = regenerateConflictFreeNodes;
window.analyzeConflictResolution = analyzeConflictResolution;

// ========== 自动初始化 ==========

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MODE3-CONFLICT-FREE] DOM loaded, initializing conflict-free mode...');
    setTimeout(() => {
        if (typeof PortDAGConflictResolver !== 'undefined') {
            if (initializeMode3ConflictFree()) {
                console.log('[MODE3-CONFLICT-FREE] Conflict-free mode ready!');
            }
        } else {
            console.error('[MODE3-CONFLICT-FREE] PortDAGConflictResolver not loaded');
        }
    }, 100);
});
