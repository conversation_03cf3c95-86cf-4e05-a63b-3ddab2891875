// 模式3路径骨架生成算法
// 基于SYSTEM.md的路径覆盖约束实现

// ========== 路径骨架生成器 ==========
class PathSkeletonGenerator {
    constructor() {
        this.pathCache = new Map();
        this.skeletonHistory = [];
    }

    // 生成路径骨架（保证每个节点至少在一条从起点到终点的路径上）
    generatePathSkeleton(nodes) {
        console.log('[PATH-SKELETON] 开始生成路径骨架...');
        
        const startNodes = nodes.filter(n => n.type === 'start');
        const endNodes = nodes.filter(n => n.type === 'end');
        const intermediateNodes = nodes.filter(n => n.type === 'intermediate');
        
        if (startNodes.length === 0 || endNodes.length === 0) {
            throw new Error('必须至少有一个起点和一个终点节点');
        }
        
        const skeleton = {
            paths: [],
            connections: [],
            coveredNodes: new Set(),
            uncoveredNodes: new Set(nodes.map(n => n.id))
        };
        
        // 步骤1：生成主要路径（贪心算法优先覆盖未使用节点）
        this.generateMainPaths(startNodes, endNodes, intermediateNodes, skeleton);
        
        // 步骤2：处理未覆盖的节点
        this.handleUncoveredNodes(nodes, skeleton);
        
        // 步骤3：验证路径覆盖完整性
        this.validatePathCoverage(nodes, skeleton);
        
        console.log(`[PATH-SKELETON] 路径骨架生成完成，覆盖 ${skeleton.coveredNodes.size}/${nodes.length} 个节点`);
        return skeleton;
    }

    generateMainPaths(startNodes, endNodes, intermediateNodes, skeleton) {
        console.log('[PATH-SKELETON] 生成主要路径...');
        
        // 按深度排序中间节点
        const sortedIntermediates = [...intermediateNodes].sort((a, b) => (a.depth || 0) - (b.depth || 0));
        
        // 为每个起点生成到终点的路径
        startNodes.forEach((startNode, startIndex) => {
            endNodes.forEach((endNode, endIndex) => {
                const pathId = `path_${startIndex}_${endIndex}`;
                const path = this.generateSinglePath(startNode, endNode, sortedIntermediates, skeleton);
                
                if (path.nodes.length > 0) {
                    skeleton.paths.push({
                        id: pathId,
                        startNode: startNode.id,
                        endNode: endNode.id,
                        nodes: path.nodes,
                        connections: path.connections
                    });
                    
                    // 标记覆盖的节点
                    path.nodes.forEach(nodeId => {
                        skeleton.coveredNodes.add(nodeId);
                        skeleton.uncoveredNodes.delete(nodeId);
                    });
                    
                    // 添加连接到骨架
                    skeleton.connections.push(...path.connections);
                }
            });
        });
    }

    generateSinglePath(startNode, endNode, availableIntermediates, skeleton) {
        const path = {
            nodes: [startNode.id],
            connections: []
        };
        
        let currentNode = startNode;
        const usedNodes = new Set([startNode.id]);
        
        // 贪心选择中间节点（优先选择未覆盖的节点）
        while (currentNode.id !== endNode.id) {
            const nextNode = this.selectNextNode(currentNode, endNode, availableIntermediates, skeleton, usedNodes);
            
            if (!nextNode) {
                // 直接连接到终点
                if (this.canConnect(currentNode, endNode)) {
                    path.nodes.push(endNode.id);
                    path.connections.push({
                        id: `conn_${currentNode.id}_${endNode.id}`,
                        fromNode: currentNode.id,
                        toNode: endNode.id,
                        fromPort: this.selectOutputPort(currentNode),
                        toPort: this.selectInputPort(endNode)
                    });
                    break;
                } else {
                    console.warn(`[PATH-SKELETON] 无法从 ${currentNode.id} 连接到 ${endNode.id}`);
                    break;
                }
            } else {
                path.nodes.push(nextNode.id);
                path.connections.push({
                    id: `conn_${currentNode.id}_${nextNode.id}`,
                    fromNode: currentNode.id,
                    toNode: nextNode.id,
                    fromPort: this.selectOutputPort(currentNode),
                    toPort: this.selectInputPort(nextNode)
                });
                
                usedNodes.add(nextNode.id);
                currentNode = nextNode;
            }
        }
        
        return path;
    }

    selectNextNode(currentNode, endNode, availableIntermediates, skeleton, usedNodes) {
        // 过滤可用的中间节点
        const candidates = availableIntermediates.filter(node => {
            return !usedNodes.has(node.id) && 
                   this.canConnect(currentNode, node) &&
                   (node.depth || 0) > (currentNode.depth || 0);
        });
        
        if (candidates.length === 0) {
            return null;
        }
        
        // 优先选择未覆盖的节点
        const uncoveredCandidates = candidates.filter(node => skeleton.uncoveredNodes.has(node.id));
        if (uncoveredCandidates.length > 0) {
            // 选择深度最接近当前节点的未覆盖节点
            return uncoveredCandidates.reduce((best, current) => {
                const bestDepthDiff = Math.abs((best.depth || 0) - (currentNode.depth || 0));
                const currentDepthDiff = Math.abs((current.depth || 0) - (currentNode.depth || 0));
                return currentDepthDiff < bestDepthDiff ? current : best;
            });
        }
        
        // 如果没有未覆盖的节点，选择最近的节点
        return candidates.reduce((best, current) => {
            const bestDepthDiff = Math.abs((best.depth || 0) - (currentNode.depth || 0));
            const currentDepthDiff = Math.abs((current.depth || 0) - (currentNode.depth || 0));
            return currentDepthDiff < bestDepthDiff ? current : best;
        });
    }

    canConnect(fromNode, toNode) {
        // 检查深度约束
        if ((fromNode.depth || 0) >= (toNode.depth || 0)) {
            return false;
        }
        
        // 检查端口兼容性（简化版本）
        const fromOutputPorts = fromNode.outputPorts || [];
        const toInputPorts = toNode.inputPorts || [];
        
        if (fromOutputPorts.length === 0 || toInputPorts.length === 0) {
            return false;
        }
        
        // 检查是否有兼容的端口类型
        return fromOutputPorts.some(outPort => 
            toInputPorts.some(inPort => 
                outPort.type === inPort.type && outPort.color === inPort.color
            )
        );
    }

    selectOutputPort(node) {
        const outputPorts = node.outputPorts || [];
        return outputPorts.length > 0 ? outputPorts[0].id : null;
    }

    selectInputPort(node) {
        const inputPorts = node.inputPorts || [];
        return inputPorts.length > 0 ? inputPorts[0].id : null;
    }

    handleUncoveredNodes(allNodes, skeleton) {
        console.log(`[PATH-SKELETON] 处理 ${skeleton.uncoveredNodes.size} 个未覆盖节点...`);
        
        const uncoveredNodesList = Array.from(skeleton.uncoveredNodes)
            .map(nodeId => allNodes.find(n => n.id === nodeId))
            .filter(node => node);
        
        // 为每个未覆盖的节点创建额外路径
        uncoveredNodesList.forEach(uncoveredNode => {
            this.createPathForUncoveredNode(uncoveredNode, allNodes, skeleton);
        });
    }

    createPathForUncoveredNode(uncoveredNode, allNodes, skeleton) {
        const startNodes = allNodes.filter(n => n.type === 'start');
        const endNodes = allNodes.filter(n => n.type === 'end');
        
        // 找到最近的起点和终点
        const nearestStart = this.findNearestNode(uncoveredNode, startNodes);
        const nearestEnd = this.findNearestNode(uncoveredNode, endNodes);
        
        if (nearestStart && nearestEnd) {
            const pathId = `extra_path_${uncoveredNode.id}`;
            const extraPath = {
                id: pathId,
                startNode: nearestStart.id,
                endNode: nearestEnd.id,
                nodes: [nearestStart.id, uncoveredNode.id, nearestEnd.id],
                connections: [
                    {
                        id: `conn_${nearestStart.id}_${uncoveredNode.id}`,
                        fromNode: nearestStart.id,
                        toNode: uncoveredNode.id,
                        fromPort: this.selectOutputPort(nearestStart),
                        toPort: this.selectInputPort(uncoveredNode)
                    },
                    {
                        id: `conn_${uncoveredNode.id}_${nearestEnd.id}`,
                        fromNode: uncoveredNode.id,
                        toNode: nearestEnd.id,
                        fromPort: this.selectOutputPort(uncoveredNode),
                        toPort: this.selectInputPort(nearestEnd)
                    }
                ]
            };
            
            skeleton.paths.push(extraPath);
            skeleton.connections.push(...extraPath.connections);
            skeleton.coveredNodes.add(uncoveredNode.id);
            skeleton.uncoveredNodes.delete(uncoveredNode.id);
            
            console.log(`[PATH-SKELETON] 为节点 ${uncoveredNode.id} 创建额外路径`);
        }
    }

    findNearestNode(targetNode, candidateNodes) {
        if (candidateNodes.length === 0) return null;
        
        return candidateNodes.reduce((nearest, current) => {
            const nearestDistance = this.calculateDistance(targetNode, nearest);
            const currentDistance = this.calculateDistance(targetNode, current);
            return currentDistance < nearestDistance ? current : nearest;
        });
    }

    calculateDistance(node1, node2) {
        const dx = (node1.x || 0) - (node2.x || 0);
        const dy = (node1.y || 0) - (node2.y || 0);
        return Math.sqrt(dx * dx + dy * dy);
    }

    validatePathCoverage(allNodes, skeleton) {
        const totalNodes = allNodes.length;
        const coveredNodes = skeleton.coveredNodes.size;
        
        if (coveredNodes < totalNodes) {
            console.warn(`[PATH-SKELETON] 路径覆盖不完整: ${coveredNodes}/${totalNodes} 节点被覆盖`);
            console.warn('[PATH-SKELETON] 未覆盖节点:', Array.from(skeleton.uncoveredNodes));
        } else {
            console.log(`[PATH-SKELETON] ✅ 路径覆盖完整: ${coveredNodes}/${totalNodes} 节点被覆盖`);
        }
        
        return {
            isComplete: coveredNodes === totalNodes,
            coverage: coveredNodes / totalNodes,
            uncoveredNodes: Array.from(skeleton.uncoveredNodes)
        };
    }
}

// ========== 端口分配器 ==========
class PortAllocator {
    constructor() {
        this.allocationHistory = [];
    }

    // 基于路径连接需求计算最小端口数
    allocatePortsBasedOnSkeleton(nodes, pathSkeleton) {
        console.log('[PORT-ALLOCATOR] 基于路径骨架分配端口...');
        
        const portRequirements = this.calculatePortRequirements(pathSkeleton);
        const balancedDistribution = this.generateBalancedDistribution(portRequirements);
        
        // 应用端口分配到节点
        this.applyPortAllocation(nodes, balancedDistribution);
        
        console.log('[PORT-ALLOCATOR] 端口分配完成');
        return balancedDistribution;
    }

    calculatePortRequirements(pathSkeleton) {
        const requirements = new Map();
        
        // 分析每个连接的端口需求
        pathSkeleton.connections.forEach(conn => {
            // 输出端口需求
            if (!requirements.has(conn.fromNode)) {
                requirements.set(conn.fromNode, { inputs: new Set(), outputs: new Set() });
            }
            requirements.get(conn.fromNode).outputs.add(`${conn.fromPort || 'default'}`);
            
            // 输入端口需求
            if (!requirements.has(conn.toNode)) {
                requirements.set(conn.toNode, { inputs: new Set(), outputs: new Set() });
            }
            requirements.get(conn.toNode).inputs.add(`${conn.toPort || 'default'}`);
        });
        
        return requirements;
    }

    generateBalancedDistribution(requirements) {
        const distribution = new Map();
        const portTypes = ['square', 'circle', 'triangle', 'diamond'];
        const portColors = ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'];
        
        // 确保每种类型的端口数量平衡
        let typeIndex = 0;
        let colorIndex = 0;
        
        requirements.forEach((nodeReqs, nodeId) => {
            const nodeDistribution = {
                inputPorts: [],
                outputPorts: []
            };
            
            // 分配输入端口
            Array.from(nodeReqs.inputs).forEach((portId, index) => {
                nodeDistribution.inputPorts.push({
                    id: portId,
                    type: portTypes[typeIndex % portTypes.length],
                    color: portColors[colorIndex % portColors.length],
                    side: 'input'
                });
                typeIndex++;
                colorIndex++;
            });
            
            // 分配输出端口
            Array.from(nodeReqs.outputs).forEach((portId, index) => {
                nodeDistribution.outputPorts.push({
                    id: portId,
                    type: portTypes[typeIndex % portTypes.length],
                    color: portColors[colorIndex % portColors.length],
                    side: 'output'
                });
                typeIndex++;
                colorIndex++;
            });
            
            distribution.set(nodeId, nodeDistribution);
        });
        
        return distribution;
    }

    applyPortAllocation(nodes, distribution) {
        nodes.forEach(node => {
            const allocation = distribution.get(node.id);
            if (allocation) {
                node.inputPorts = allocation.inputPorts;
                node.outputPorts = allocation.outputPorts;
            }
        });
    }
}

// ========== 导出接口 ==========
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PathSkeletonGenerator,
        PortAllocator
    };
}
