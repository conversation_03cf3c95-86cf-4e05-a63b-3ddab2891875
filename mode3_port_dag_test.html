<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Port-Type DAG Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .port-dag-info {
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .info-item {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #00aaff;
        }
        
        .status-good { color: #4CAF50; }
        .status-error { color: #f44336; }
        .status-warning { color: #ff9800; }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .area-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
            border: 1px solid #555;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .btn.warning {
            background: #ff9800;
        }
        
        .btn.warning:hover {
            background: #f57c00;
        }
        
        .validation-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .port-type-analysis {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .analysis-item {
            background: #2a2a3a;
            padding: 15px;
            border-radius: 5px;
        }
        
        .port-type-item {
            background: #333;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .self-loop-risks {
            background: #3a1a1a;
            border: 1px solid #ff4444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .risk-item {
            background: #4a2a2a;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            color: #ff6666;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 Mode 3 Port-Type DAG Test</h1>
            <p>端口类型优先的DAG生成 - 避免自环、智能平衡、独立验证</p>
        </div>
        
        <div class="port-dag-info">
            <h3>📊 Port-Type DAG Status</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div>Current Wave</div>
                    <div class="info-value" id="current-wave">1</div>
                </div>
                <div class="info-item">
                    <div>Self-Loop Risks</div>
                    <div class="info-value status-error" id="self-loop-count">0</div>
                </div>
                <div class="info-item">
                    <div>Port Types</div>
                    <div class="info-value" id="port-type-count">0</div>
                </div>
                <div class="info-item">
                    <div>DAG Validation</div>
                    <div class="info-value status-good" id="port-dag-status">Valid</div>
                </div>
                <div class="info-item">
                    <div>Temp Nodes</div>
                    <div class="info-value" id="temp-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Placed Nodes</div>
                    <div class="info-value" id="placed-nodes">0</div>
                </div>
            </div>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="area-header" style="color: #ffaa00;">🔗 Port-DAG Node Pool</div>
                <canvas id="temporaryCanvas" width="280" height="400"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="area-header" style="color: #00aaff;">🏗️ DAG Construction Area</div>
                <canvas id="gameCanvas" width="680" height="400"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="nextPortDAGWave()">🌊 Next Wave (N)</button>
            <button class="btn" onclick="validatePortDAGState()">✅ Validate (V)</button>
            <button class="btn" onclick="regeneratePortDAGNodes()">🔄 Regenerate (R)</button>
            <button class="btn" onclick="showPortDAGStatistics()">📊 Statistics (S)</button>
            <button class="btn warning" onclick="testSelfLoopDetection()">⚠️ Test Self-Loops</button>
            <button class="btn danger" onclick="resetPortDAGMode()">🔄 Reset</button>
        </div>
        
        <div class="validation-panel">
            <h3>✅ Port-Type DAG Validation</h3>
            <div id="port-dag-details">
                Loading validation details...
            </div>
        </div>
        
        <div class="port-type-analysis">
            <h3>🔍 Port Type Analysis</h3>
            <div class="analysis-grid">
                <div class="analysis-item">
                    <h4>Port Type Balance</h4>
                    <div id="port-balance-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Node Distribution</h4>
                    <div id="node-distribution-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>DAG Properties</h4>
                    <div id="dag-properties-analysis">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="self-loop-risks">
            <h3>⚠️ Self-Loop Risk Detection</h3>
            <div id="self-loop-risks">
                No self-loop risks detected.
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 Port-Type DAG Instructions</h3>
            <ul>
                <li><strong>Port-Type Priority:</strong> Each port type (shape-color) forms an independent DAG</li>
                <li><strong>Anti-Self-Loop:</strong> Nodes cannot have same port type for both input and output</li>
                <li><strong>Smart Balance:</strong> Algorithm prioritizes balancing existing port types</li>
                <li><strong>Intermediate Consistency:</strong> Middle nodes use consistent port type sets</li>
                <li><strong>Independent Validation:</strong> Each port type validated separately for DAG property</li>
                <li><strong>Visual Indicators:</strong> Red borders indicate self-loop risks, red connections show violations</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🔬 Algorithm Features</h3>
            <ul>
                <li><strong>Expansion DAG:</strong> Generates more sink nodes when inputs needed</li>
                <li><strong>Contraction DAG:</strong> Generates more source nodes when outputs needed</li>
                <li><strong>Neutral DAG:</strong> Creates balanced chain structures</li>
                <li><strong>Risk Detection:</strong> Real-time detection of potential self-loop configurations</li>
                <li><strong>Intelligent Allocation:</strong> Smart port-to-node assignment avoiding conflicts</li>
            </ul>
        </div>
        
        <div>
            <h3>📋 Port-DAG Log</h3>
            <div class="log" id="port-dag-log"></div>
        </div>
    </div>

    <!-- Load scripts in dependency order -->
    <script src="port_type_dag_engine.js"></script>
    <script src="mode3_port_dag.js"></script>
    
    <script>
        // Logging system
        function logPortDAG(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('port-dag-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[PORT-DAG] ${message}`);
        }
        
        // UI update functions
        function updatePortDAGStatus() {
            if (typeof mode3PortDAGState !== 'undefined') {
                document.getElementById('current-wave').textContent = mode3PortDAGState.currentWave;
                document.getElementById('self-loop-count').textContent = mode3PortDAGState.selfLoopRisks.size;
                document.getElementById('port-type-count').textContent = mode3PortDAGState.portTypeStats.size;
                document.getElementById('temp-nodes').textContent = mode3PortDAGState.temporaryNodes.length;
                document.getElementById('placed-nodes').textContent = mode3PortDAGState.placedNodes.length;
                
                // Update self-loop count color
                const selfLoopElement = document.getElementById('self-loop-count');
                selfLoopElement.className = mode3PortDAGState.selfLoopRisks.size > 0 ? 'info-value status-error' : 'info-value status-good';
            }
        }
        
        function updatePortTypeAnalysis() {
            if (typeof mode3PortDAGState !== 'undefined') {
                // Port balance analysis
                const balanceItems = [];
                mode3PortDAGState.portTypeStats.forEach((stat, portType) => {
                    const balanceClass = stat.balance === 0 ? 'status-good' : 
                                        Math.abs(stat.balance) <= 2 ? 'status-warning' : 'status-error';
                    balanceItems.push(`<div class="port-type-item">
                        <span class="${balanceClass}">${portType}</span>: 
                        ${stat.inputs} in, ${stat.outputs} out 
                        (balance: ${stat.balance > 0 ? '+' : ''}${stat.balance})
                    </div>`);
                });
                document.getElementById('port-balance-analysis').innerHTML = balanceItems.join('');
                
                // Node distribution analysis
                const allNodes = [...mode3PortDAGState.temporaryNodes, ...mode3PortDAGState.placedNodes];
                const nodeTypes = { start: 0, intermediate: 0, end: 0 };
                allNodes.forEach(node => {
                    nodeTypes[node.type] = (nodeTypes[node.type] || 0) + 1;
                });
                
                document.getElementById('node-distribution-analysis').innerHTML = `
                    <div class="port-type-item">Start Nodes: ${nodeTypes.start}</div>
                    <div class="port-type-item">Intermediate Nodes: ${nodeTypes.intermediate}</div>
                    <div class="port-type-item">End Nodes: ${nodeTypes.end}</div>
                    <div class="port-type-item">Total Nodes: ${allNodes.length}</div>
                `;
                
                // DAG properties analysis
                const dagInfo = [];
                if (mode3PortDAGState.dagValidations) {
                    mode3PortDAGState.dagValidations.forEach((result, portType) => {
                        const statusClass = result.isDAG ? 'status-good' : 'status-error';
                        const selfLoopInfo = result.selfLoopNodes.size > 0 ? ` (${result.selfLoopNodes.size} self-loops)` : '';
                        dagInfo.push(`<div class="port-type-item">
                            <span class="${statusClass}">${portType}</span>: 
                            ${result.nodeCount} nodes, ${result.edgeCount} edges${selfLoopInfo}
                        </div>`);
                    });
                }
                document.getElementById('dag-properties-analysis').innerHTML = dagInfo.join('') || 'No DAG validations available';
            }
        }
        
        function updateSelfLoopRisks() {
            if (typeof mode3PortDAGState !== 'undefined') {
                const risks = Array.from(mode3PortDAGState.selfLoopRisks);
                
                if (risks.length === 0) {
                    document.getElementById('self-loop-risks').innerHTML = 
                        '<div class="status-good">No self-loop risks detected.</div>';
                } else {
                    const riskItems = risks.map(risk => {
                        const [nodeId, portType] = risk.split(':');
                        return `<div class="risk-item">⚠️ Node ${nodeId}: Port type ${portType}</div>`;
                    });
                    document.getElementById('self-loop-risks').innerHTML = riskItems.join('');
                }
            }
        }
        
        // Test functions
        function testSelfLoopDetection() {
            logPortDAG('Testing self-loop detection mechanism', 'info');
            
            // Create a test node with self-loop risk
            const testNode = {
                id: 'test_self_loop',
                type: 'intermediate',
                depth: 1,
                inputPorts: [
                    { id: 'test_in', type: 'square', color: '#ff5252', side: 'input', portTypeKey: 'square-#ff5252' }
                ],
                outputPorts: [
                    { id: 'test_out', type: 'square', color: '#ff5252', side: 'output', portTypeKey: 'square-#ff5252' }
                ],
                x: 150,
                y: 200,
                area: 'temporary'
            };
            
            mode3PortDAGState.temporaryNodes.push(testNode);
            updatePortTypeStatistics();
            updateMode3PortDAGDisplay();
            
            logPortDAG('Added test node with self-loop risk for square-#ff5252', 'warn');
            
            setTimeout(() => {
                mode3PortDAGState.temporaryNodes = mode3PortDAGState.temporaryNodes.filter(n => n.id !== 'test_self_loop');
                updatePortTypeStatistics();
                updateMode3PortDAGDisplay();
                logPortDAG('Removed test node', 'info');
            }, 3000);
        }
        
        function resetPortDAGMode() {
            if (confirm('Reset port-DAG mode? This will clear all nodes and connections.')) {
                location.reload();
            }
        }
        
        // Auto-update functions
        setInterval(updatePortDAGStatus, 1000);
        setInterval(updatePortTypeAnalysis, 2000);
        setInterval(updateSelfLoopRisks, 1500);
        
        // Initial setup
        setTimeout(() => {
            updatePortDAGStatus();
            updatePortTypeAnalysis();
            updateSelfLoopRisks();
            logPortDAG('Port-DAG test framework loaded', 'success');
            
            if (typeof mode3PortDAGState !== 'undefined') {
                logPortDAG('Mode 3 port-DAG state initialized', 'success');
            } else {
                logPortDAG('Mode 3 port-DAG state not found', 'error');
            }
        }, 1000);
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            switch(event.key.toLowerCase()) {
                case 't':
                    testSelfLoopDetection();
                    break;
            }
        });
    </script>
</body>
</html>
