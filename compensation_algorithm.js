// ========== 分层补偿算法引擎 ==========
// 基于您的设计方案：分层补偿系统 + 类型隔离验证 + 全局拓扑序维护

// ========== Mode 3 端口和节点生成多样性系统 ==========

class Mode3DiversityManager {
    constructor() {
        this.portTypePool = [
            { shape: 'square', colors: ['#ff5252', '#e91e63', '#9c27b0', '#673ab7'] },
            { shape: 'circle', colors: ['#2196F3', '#03a9f4', '#00bcd4', '#009688'] },
            { shape: 'triangle', colors: ['#4CAF50', '#8bc34a', '#cddc39', '#ffeb3b'] },
            { shape: 'diamond', colors: ['#ff9800', '#ff5722', '#795548', '#607d8b'] }
        ];

        this.nodeComplexityLevels = [
            { name: 'simple', inputRange: [1, 2], outputRange: [1, 2] },
            { name: 'medium', inputRange: [2, 3], outputRange: [2, 3] },
            { name: 'complex', inputRange: [3, 4], outputRange: [3, 4] },
            { name: 'advanced', inputRange: [2, 4], outputRange: [2, 4] }
        ];

        this.wavePortTypeHistory = new Map(); // 记录每波次使用的端口类型
    }

    // 为指定波次生成多样化的端口类型配置
    generateDiversePortConfiguration(wave, difficulty) {
        console.log(`[DIVERSITY] Generating diverse port configuration for wave ${wave}...`);

        const config = {
            portTypes: [],
            nodeConfigurations: [],
            complexityLevel: this.calculateComplexityLevel(wave, difficulty)
        };

        // 确定这一波次应该使用的端口类型数量
        const portTypeCount = this.calculatePortTypeCount(wave, difficulty);

        // 选择多样化的端口类型，避免与前一波次重复
        const selectedPortTypes = this.selectDiversePortTypes(wave, portTypeCount);
        config.portTypes = selectedPortTypes;

        // 为每种端口类型生成节点配置
        config.nodeConfigurations = this.generateNodeConfigurations(selectedPortTypes, config.complexityLevel, wave);

        // 记录这一波次的端口类型使用情况
        this.wavePortTypeHistory.set(wave, selectedPortTypes.map(pt => `${pt.shape}-${pt.color}`));

        console.log(`[DIVERSITY] Wave ${wave} configuration:`, {
            portTypeCount: selectedPortTypes.length,
            complexityLevel: config.complexityLevel,
            nodeCount: config.nodeConfigurations.length
        });

        return config;
    }

    // 计算波次复杂度等级
    calculateComplexityLevel(wave, difficulty) {
        const baseComplexity = Math.min(Math.floor((wave - 1) / 2), this.nodeComplexityLevels.length - 1);
        const difficultyBonus = Math.floor(difficulty / 3);
        return Math.min(baseComplexity + difficultyBonus, this.nodeComplexityLevels.length - 1);
    }

    // 计算端口类型数量
    calculatePortTypeCount(wave, difficulty) {
        // 第1波：2-3种端口类型
        // 第2-3波：3-4种端口类型
        // 第4+波：4种端口类型
        const baseCount = Math.min(2 + Math.floor(wave / 2), 4);
        const difficultyBonus = difficulty > 5 ? 1 : 0;
        return Math.min(baseCount + difficultyBonus, this.portTypePool.length);
    }

    // 选择多样化的端口类型
    selectDiversePortTypes(wave, count) {
        console.log(`[DIVERSITY] Selecting ${count} diverse port types for wave ${wave}...`);

        const selectedTypes = [];
        const previousWaveTypes = this.wavePortTypeHistory.get(wave - 1) || [];

        // 优先选择与前一波次不同的端口类型
        const availableShapes = [...this.portTypePool];

        for (let i = 0; i < count && availableShapes.length > 0; i++) {
            let selectedShape;

            if (i < 2) {
                // 前两个端口类型优先选择与前一波次不同的
                selectedShape = this.selectNonRepeatingShape(availableShapes, previousWaveTypes);
            } else {
                // 后续端口类型可以随机选择
                const randomIndex = Math.floor(Math.random() * availableShapes.length);
                selectedShape = availableShapes[randomIndex];
            }

            // 为选中的形状随机选择颜色
            const randomColorIndex = Math.floor(Math.random() * selectedShape.colors.length);
            const selectedColor = selectedShape.colors[randomColorIndex];

            selectedTypes.push({
                shape: selectedShape.shape,
                color: selectedColor
            });

            // 移除已选择的形状，避免重复
            const shapeIndex = availableShapes.findIndex(s => s.shape === selectedShape.shape);
            if (shapeIndex !== -1) {
                availableShapes.splice(shapeIndex, 1);
            }

            console.log(`[DIVERSITY] Selected port type ${i + 1}: ${selectedShape.shape}-${selectedColor}`);
        }

        return selectedTypes;
    }

    // 选择与前一波次不重复的形状
    selectNonRepeatingShape(availableShapes, previousWaveTypes) {
        const nonRepeatingShapes = availableShapes.filter(shape => {
            return !previousWaveTypes.some(prevType => prevType.startsWith(shape.shape));
        });

        if (nonRepeatingShapes.length > 0) {
            const randomIndex = Math.floor(Math.random() * nonRepeatingShapes.length);
            return nonRepeatingShapes[randomIndex];
        } else {
            // 如果所有形状都重复，随机选择一个
            const randomIndex = Math.floor(Math.random() * availableShapes.length);
            return availableShapes[randomIndex];
        }
    }

    // 生成多样化的节点配置
    generateNodeConfigurations(portTypes, complexityLevel, wave) {
        console.log(`[DIVERSITY] Generating node configurations with complexity level ${complexityLevel}...`);

        const configurations = [];
        const complexity = this.nodeComplexityLevels[complexityLevel];

        // 确定节点数量（基于波次和复杂度）
        const nodeCount = Math.min(3 + Math.floor(wave / 2), 6);

        for (let i = 0; i < nodeCount; i++) {
            const config = this.generateSingleNodeConfiguration(portTypes, complexity, i, nodeCount, wave);
            configurations.push(config);
        }

        console.log(`[DIVERSITY] Generated ${configurations.length} diverse node configurations`);
        return configurations;
    }

    // 生成单个节点的配置
    generateSingleNodeConfiguration(portTypes, complexity, index, totalNodes, wave) {
        // 确定节点类型
        let nodeType = 'normal';
        if (index === 0 && Math.random() < 0.3) {
            nodeType = 'start';
        } else if (index === totalNodes - 1 && Math.random() < 0.3) {
            nodeType = 'end';
        }

        // 确定端口数量
        const inputCount = nodeType === 'start' ? 0 :
                          this.randomInRange(complexity.inputRange[0], complexity.inputRange[1]);
        const outputCount = nodeType === 'end' ? 0 :
                           this.randomInRange(complexity.outputRange[0], complexity.outputRange[1]);

        // 选择端口类型
        const inputPorts = this.selectPortsForNode(portTypes, inputCount, 'input');
        const outputPorts = this.selectPortsForNode(portTypes, outputCount, 'output');

        const config = {
            id: `diverse_node_${wave}_${index}_${Date.now()}`,
            label: `Div-${wave}-${index + 1}`,
            type: nodeType,
            level: Math.floor(index / 2), // 分布在不同层级
            inputPorts: inputPorts,
            outputPorts: outputPorts,
            complexity: complexity.name
        };

        console.log(`[DIVERSITY] Node config ${index + 1}: ${config.type} (${inputPorts.length}in, ${outputPorts.length}out)`);
        return config;
    }

    // 为节点选择端口
    selectPortsForNode(portTypes, count, direction) {
        const ports = [];

        for (let i = 0; i < count; i++) {
            // 随机选择端口类型，但确保多样性
            let selectedType;
            if (i < portTypes.length) {
                // 前几个端口使用不同的类型
                selectedType = portTypes[i];
            } else {
                // 后续端口随机选择
                selectedType = portTypes[Math.floor(Math.random() * portTypes.length)];
            }

            ports.push({
                type: selectedType.shape,
                color: selectedType.color,
                direction: direction
            });
        }

        return ports;
    }

    // 在范围内生成随机数
    randomInRange(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // 获取当前波次的端口类型统计
    getWavePortTypeStats(wave) {
        const stats = {
            totalTypes: 0,
            uniqueShapes: new Set(),
            uniqueColors: new Set(),
            typeDistribution: new Map()
        };

        const waveTypes = this.wavePortTypeHistory.get(wave);
        if (waveTypes) {
            stats.totalTypes = waveTypes.length;

            waveTypes.forEach(typeKey => {
                const [shape, color] = typeKey.split('-');
                stats.uniqueShapes.add(shape);
                stats.uniqueColors.add(color);

                if (!stats.typeDistribution.has(shape)) {
                    stats.typeDistribution.set(shape, 0);
                }
                stats.typeDistribution.set(shape, stats.typeDistribution.get(shape) + 1);
            });
        }

        return stats;
    }
}

class HierarchicalCompensationAlgorithm {
    constructor() {
        this.portTypes = [
            { shape: 'square', color: 'red' },
            { shape: 'circle', color: 'blue' },
            { shape: 'triangle', color: 'green' },
            { shape: 'diamond', color: 'yellow' }
        ];
        this.nodeCounter = 0;
        this.maxLevel = 0;  // 当前最大层级
        this.minLevel = 0;  // 最小层级（起点）

        // 分层补偿系统配置
        this.maxPortsPerNode = 4;  // 每个节点最大端口数
        this.levelCache = null;    // 层级缓存

        // 动态难度参数
        this.currentWave = 1;
        this.difficultyConfig = {
            maxPortTypes: 2,
            maxModifications: 2,
            maxSourceNodes: 1,
            maxSinkNodes: 1
        };

        // 集成多样性管理器
        this.diversityManager = new Mode3DiversityManager();
    }

    // ========== 核心创新：分层补偿系统 ==========

    compensate(nodePool) {
        console.log('[HIERARCHICAL-COMPENSATION] Starting port-pair-aware compensation');

        // 核心策略：确保每种端口类型都能形成有效的层级流
        const compensationNodes = [];

        for (const portType of this.portTypes) {
            const portTypeKey = `${portType.shape}-${portType.color}`;
            const typeCompensation = this.compensatePortType(portTypeKey, nodePool);
            compensationNodes.push(...typeCompensation);
        }

        console.log(`[HIERARCHICAL-COMPENSATION] Generated ${compensationNodes.length} compensation nodes using port-pair strategy`);

        // 3. 确保全局拓扑序
        this.rebalanceLevels(nodePool, compensationNodes);

        // 4. 验证强可解性
        const isValid = this.validateStrongSolvability(nodePool, compensationNodes);
        if (!isValid) {
            console.error('[HIERARCHICAL-COMPENSATION] Strong solvability validation failed');
            return this.emergencyCompensation(nodePool);
        }

        console.log('[HIERARCHICAL-COMPENSATION] Hierarchical compensation completed successfully');
        return compensationNodes;
    }

    // 计算端口失衡量（按您的数学模型）
    calculatePortImbalance(nodePool) {
        const balance = new Map();

        // 初始化所有端口类型
        this.portTypes.forEach(portType => {
            const key = `${portType.shape}-${portType.color}`;
            balance.set(key, {
                inputs: 0,
                outputs: 0,
                delta: 0
            });
        });

        // 统计所有节点的端口
        nodePool.forEach(node => {
            // 统计输出端口
            (node.outputPorts || []).forEach(port => {
                const key = port.portTypeKey || `${port.type}-${port.color}`;
                const stat = balance.get(key);
                if (stat) {
                    stat.outputs++;
                }
            });

            // 统计输入端口
            (node.inputPorts || []).forEach(port => {
                const key = port.portTypeKey || `${port.type}-${port.color}`;
                const stat = balance.get(key);
                if (stat) {
                    stat.inputs++;
                }
            });
        });

        // 计算不平衡
        balance.forEach((stat, key) => {
            stat.delta = stat.outputs - stat.inputs;
        });

        console.log('[HIERARCHICAL-COMPENSATION] Port balance calculated:', Object.fromEntries(balance));
        return balance;
    }

    // ========== 分层补偿核心方法 ==========

    // 在层级0（起点）添加输出端口
    addOutputPortsAtLevel0(count, portType, nodePool) {
        console.log(`[HIERARCHICAL-COMPENSATION] Adding ${count} output ports for ${portType} at level 0`);

        const [shape, color] = portType.split('-');
        const compensationNodes = [];

        // 1. 优先在现有起点节点添加
        const sourceNodes = nodePool.filter(node =>
            this.getNodeLevel(node) === 0 && this.isSourceNode(node)
        );

        let added = this.addToExistingNodes(count, portType, sourceNodes, false); // false = output

        // 2. 不足部分创建新起点节点
        let remaining = count - added;
        while (remaining > 0) {
            const portsToAdd = Math.min(this.maxPortsPerNode, remaining);
            const newSourceNode = this.createSourceNode(portType, portsToAdd);
            compensationNodes.push(newSourceNode);
            remaining -= portsToAdd;

            console.log(`[HIERARCHICAL-COMPENSATION] Created new source node ${newSourceNode.id} with ${portsToAdd} ${portType} outputs`);
        }

        return compensationNodes;
    }

    // 在最高层级（终点）添加输入端口
    addInputPortsAtMaxLevel(count, portType, nodePool) {
        console.log(`[HIERARCHICAL-COMPENSATION] Adding ${count} input ports for ${portType} at max level`);

        const [shape, color] = portType.split('-');
        const compensationNodes = [];

        // 确定新的最高层级
        const currentMaxLevel = this.getCurrentMaxLevel(nodePool);
        const newMaxLevel = currentMaxLevel + 1;
        this.maxLevel = newMaxLevel;

        // 1. 优先在现有终点节点添加
        const sinkNodes = nodePool.filter(node =>
            this.getNodeLevel(node) === currentMaxLevel && this.isSinkNode(node)
        );

        let added = this.addToExistingNodes(count, portType, sinkNodes, true); // true = input

        // 2. 不足部分创建新终点节点
        let remaining = count - added;
        while (remaining > 0) {
            const portsToAdd = Math.min(this.maxPortsPerNode, remaining);
            const newSinkNode = this.createSinkNode(portType, portsToAdd, newMaxLevel);
            compensationNodes.push(newSinkNode);
            remaining -= portsToAdd;

            console.log(`[HIERARCHICAL-COMPENSATION] Created new sink node ${newSinkNode.id} with ${portsToAdd} ${portType} inputs at level ${newMaxLevel}`);
        }

        return compensationNodes;
    }

    // 智能添加端口到现有节点
    addToExistingNodes(count, portType, candidateNodes, isInput) {
        let added = 0;
        const [shape, color] = portType.split('-');

        // 按端口数排序，优先添加至较空节点
        const sortedNodes = candidateNodes.sort((a, b) =>
            this.getNodePortCount(a) - this.getNodePortCount(b)
        );

        for (const node of sortedNodes) {
            if (added >= count) break;

            // 检查节点容量
            const currentPortCount = this.getNodePortCount(node);
            const availableSlots = this.maxPortsPerNode - currentPortCount;

            if (availableSlots <= 0) continue;

            // 添加端口
            const addCount = Math.min(count - added, availableSlots);
            for (let i = 0; i < addCount; i++) {
                const portId = `${node.id}_${isInput ? 'in' : 'out'}_${Date.now()}_${i}`;
                const newPort = {
                    id: portId,
                    type: shape,
                    color: color,
                    side: isInput ? 'input' : 'output',
                    nodeId: node.id,
                    portTypeKey: portType,
                    x: 0, y: 0
                };

                if (isInput) {
                    node.inputPorts = node.inputPorts || [];
                    node.inputPorts.push(newPort);
                } else {
                    node.outputPorts = node.outputPorts || [];
                    node.outputPorts.push(newPort);
                }
            }

            added += addCount;
            console.log(`[HIERARCHICAL-COMPENSATION] Added ${addCount} ${isInput ? 'input' : 'output'} ports to existing node ${node.id}`);
        }

        return added;
    }

    // ========== 节点创建方法 ==========

    createSourceNode(portType, outputCount) {
        const [shape, color] = portType.split('-');
        const nodeId = `hierarchical_source_${portType}_${this.nodeCounter++}`;

        const sourceNode = {
            id: nodeId,
            type: 'start',
            level: 0,  // 起点固定在层级0
            depth: 0,  // 兼容现有系统
            inputPorts: [],  // 起点无输入端口
            outputPorts: [],
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'hierarchical_compensation',
            purpose: `source_${portType}`,
            compensationType: 'output_deficit'
        };

        // 添加指定数量的输出端口
        for (let i = 0; i < outputCount; i++) {
            sourceNode.outputPorts.push({
                id: `${nodeId}_out_${i}`,
                type: shape,
                color: color,
                side: 'output',
                nodeId: nodeId,
                portTypeKey: portType,
                x: 0, y: 0
            });
        }

        return sourceNode;
    }

    createSinkNode(portType, inputCount, level) {
        const [shape, color] = portType.split('-');
        const nodeId = `hierarchical_sink_${portType}_${this.nodeCounter++}`;

        const sinkNode = {
            id: nodeId,
            type: 'end',
            level: level,
            depth: level * 100,  // 兼容现有系统
            inputPorts: [],
            outputPorts: [],  // 终点无输出端口
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'hierarchical_compensation',
            purpose: `sink_${portType}`,
            compensationType: 'input_deficit'
        };

        // 添加指定数量的输入端口
        for (let i = 0; i < inputCount; i++) {
            sinkNode.inputPorts.push({
                id: `${nodeId}_in_${i}`,
                type: shape,
                color: color,
                side: 'input',
                nodeId: nodeId,
                portTypeKey: portType,
                x: 0, y: 0
            });
        }

        return sinkNode;
    }

    // ========== 层级管理方法 ==========

    getNodeLevel(node) {
        // 优先使用level属性，回退到depth计算
        if (node.level !== undefined) {
            return node.level;
        }

        // 从depth推导level
        if (node.depth === 0) return 0;
        if (node.depth >= 1000) return this.maxLevel;
        return Math.floor(node.depth / 100);
    }

    getCurrentMaxLevel(nodePool) {
        let maxLevel = 0;
        nodePool.forEach(node => {
            const level = this.getNodeLevel(node);
            if (level > maxLevel) {
                maxLevel = level;
            }
        });
        return maxLevel;
    }

    isSourceNode(node) {
        return node.type === 'start' ||
               ((!node.inputPorts || node.inputPorts.length === 0) &&
                (node.outputPorts && node.outputPorts.length > 0));
    }

    isSinkNode(node) {
        return node.type === 'end' ||
               ((node.inputPorts && node.inputPorts.length > 0) &&
                (!node.outputPorts || node.outputPorts.length === 0));
    }

    getNodePortCount(node) {
        return (node.inputPorts?.length || 0) + (node.outputPorts?.length || 0);
    }

    // ========== 全局拓扑序维护 ==========

    rebalanceLevels(nodePool, compensationNodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Rebalancing global topological order');

        const allNodes = [...nodePool, ...compensationNodes];

        // 1. 确保起点在层级0
        allNodes.forEach(node => {
            if (this.isSourceNode(node)) {
                node.level = 0;
                node.depth = 0;
            }
        });

        // 2. 确保终点在最高层级
        const maxLevel = this.getCurrentMaxLevel(allNodes);
        allNodes.forEach(node => {
            if (this.isSinkNode(node)) {
                node.level = maxLevel;
                node.depth = maxLevel * 100;
            }
        });

        // 3. 验证中间节点层级合理性
        allNodes.forEach(node => {
            if (!this.isSourceNode(node) && !this.isSinkNode(node)) {
                // 中间节点必须在起点和终点之间
                if (node.level <= 0 || node.level >= maxLevel) {
                    node.level = Math.floor(maxLevel / 2) || 1;
                    node.depth = node.level * 100;
                    console.log(`[HIERARCHICAL-COMPENSATION] Adjusted intermediate node ${node.id} to level ${node.level}`);
                }
            }
        });

        // 4. 更新全局最大层级
        this.maxLevel = maxLevel;

        // 5. 清除层级缓存
        this.invalidateLevelCache();

        console.log(`[HIERARCHICAL-COMPENSATION] Global topological order rebalanced, max level: ${maxLevel}`);
    }

    invalidateLevelCache() {
        this.levelCache = null;
    }

    // ========== 类型隔离验证 ==========

    validateStrongSolvability(nodePool, compensationNodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Validating strong solvability with type isolation');

        const allNodes = [...nodePool, ...compensationNodes];

        // 1. 验证端口平衡
        const balanceValid = this.validatePortBalance(allNodes);
        if (!balanceValid) {
            console.error('[HIERARCHICAL-COMPENSATION] Port balance validation failed');
            return false;
        }

        // 2. 验证每种端口类型的DAG属性
        const dagValid = this.validateTypeIsolatedDAGs(allNodes);
        if (!dagValid) {
            console.error('[HIERARCHICAL-COMPENSATION] Type-isolated DAG validation failed');
            return false;
        }

        // 3. 验证全局拓扑序
        const topologyValid = this.validateGlobalTopologicalOrder(allNodes);
        if (!topologyValid) {
            console.error('[HIERARCHICAL-COMPENSATION] Global topological order validation failed');
            return false;
        }

        // 4. 验证节点可达性
        const reachabilityValid = this.validateNodeReachability(allNodes);
        if (!reachabilityValid) {
            console.error('[HIERARCHICAL-COMPENSATION] Node reachability validation failed');
            return false;
        }

        console.log('[HIERARCHICAL-COMPENSATION] ✅ Strong solvability validated successfully');
        return true;
    }

    validatePortBalance(nodes) {
        const balance = this.calculatePortImbalance(nodes);

        for (const [portType, stat] of balance.entries()) {
            if (stat.delta !== 0) {
                console.error(`[HIERARCHICAL-COMPENSATION] Port balance violation: ${portType} has delta ${stat.delta}`);
                return false;
            }
        }

        console.log('[HIERARCHICAL-COMPENSATION] ✅ Port balance validation passed');
        return true;
    }

    validateTypeIsolatedDAGs(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Validating type-isolated DAGs');

        // 为每种端口类型独立验证DAG
        for (const portType of this.portTypes) {
            const portTypeKey = `${portType.shape}-${portType.color}`;

            if (!this.validateSinglePortTypeDAG(portTypeKey, nodes)) {
                console.error(`[HIERARCHICAL-COMPENSATION] DAG validation failed for port type: ${portTypeKey}`);
                return false;
            }
        }

        console.log('[HIERARCHICAL-COMPENSATION] ✅ All port types form valid DAGs');
        return true;
    }

    validateSinglePortTypeDAG(portTypeKey, nodes) {
        // 1. 检查单节点自连接问题
        if (!this.validateNoSelfConnectionRequired(portTypeKey, nodes)) {
            console.error(`[HIERARCHICAL-COMPENSATION] ❌ Port type ${portTypeKey} requires impossible self-connection`);
            return false;
        }

        // 2. 验证端口类型的连通性和流向
        if (!this.validatePortTypeConnectivity(portTypeKey, nodes)) {
            console.error(`[HIERARCHICAL-COMPENSATION] ❌ Port type ${portTypeKey} lacks valid flow path`);
            return false;
        }

        // 3. 构建该端口类型的子图
        const typeGraph = this.buildTypeSpecificGraph(portTypeKey, nodes);

        // 4. 使用Kahn算法验证DAG
        return this.isDAGUsingKahn(typeGraph, portTypeKey);
    }

    buildTypeSpecificGraph(portTypeKey, nodes) {
        const graph = new Map();
        const nodeSet = new Set();

        // 1. 找到使用该端口类型的节点
        nodes.forEach(node => {
            const hasPortType = [...(node.inputPorts || []), ...(node.outputPorts || [])].some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );

            if (hasPortType) {
                nodeSet.add(node.id);
                graph.set(node.id, {
                    node: node,
                    level: this.getNodeLevel(node),
                    outEdges: new Set(),
                    inEdges: new Set()
                });
            }
        });

        // 2. 根据层级关系构建边（层级约束即为拓扑约束）
        const nodeArray = Array.from(nodeSet).map(nodeId =>
            nodes.find(n => n.id === nodeId)
        ).sort((a, b) => this.getNodeLevel(a) - this.getNodeLevel(b));

        // 3. 为该端口类型构建潜在连接
        for (let i = 0; i < nodeArray.length; i++) {
            const sourceNode = nodeArray[i];
            const sourceLevel = this.getNodeLevel(sourceNode);

            // 检查是否有该类型的输出端口
            const hasOutput = (sourceNode.outputPorts || []).some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );

            if (!hasOutput) continue;

            // 寻找更高层级的节点作为目标
            for (let j = i + 1; j < nodeArray.length; j++) {
                const targetNode = nodeArray[j];
                const targetLevel = this.getNodeLevel(targetNode);

                if (targetLevel <= sourceLevel) continue;

                // 检查是否有该类型的输入端口
                const hasInput = (targetNode.inputPorts || []).some(port =>
                    (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
                );

                if (hasInput) {
                    // 添加边：source -> target
                    graph.get(sourceNode.id).outEdges.add(targetNode.id);
                    graph.get(targetNode.id).inEdges.add(sourceNode.id);
                }
            }
        }

        return graph;
    }

    // 验证端口层级分布是否能形成DAG
    validateNoSelfConnectionRequired(portTypeKey, nodes) {
        console.log(`[HIERARCHICAL-COMPENSATION] Validating port-level DAG for ${portTypeKey}`);

        // 构建端口的层级分布
        const levelDistribution = this.buildPortLevelDistribution(portTypeKey, nodes);

        // 验证层级分布的DAG可行性
        return this.validatePortLevelDAG(portTypeKey, levelDistribution);
    }

    // 构建端口在各层级的分布
    buildPortLevelDistribution(portTypeKey, nodes) {
        const distribution = new Map();

        // 初始化所有层级
        for (let level = 0; level <= this.maxLevel; level++) {
            distribution.set(level, { inputs: 0, outputs: 0 });
        }

        // 统计每个层级的端口数量
        nodes.forEach(node => {
            const nodeLevel = this.getNodeLevel(node);

            // 统计输入端口
            const inputPorts = (node.inputPorts || []).filter(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );

            // 统计输出端口
            const outputPorts = (node.outputPorts || []).filter(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );

            if (inputPorts.length > 0 || outputPorts.length > 0) {
                const levelData = distribution.get(nodeLevel) || { inputs: 0, outputs: 0 };
                levelData.inputs += inputPorts.length;
                levelData.outputs += outputPorts.length;
                distribution.set(nodeLevel, levelData);
            }
        });

        return distribution;
    }

    // 验证端口层级分布是否满足DAG约束
    validatePortLevelDAG(portTypeKey, levelDistribution) {
        console.log(`[HIERARCHICAL-COMPENSATION] Checking DAG feasibility for ${portTypeKey} port distribution`);

        // 核心约束：累积输出必须始终 >= 累积输入（从低层级到高层级）
        let cumulativeOutputs = 0;
        let cumulativeInputs = 0;
        let hasAnyPorts = false;

        const levels = Array.from(levelDistribution.keys()).sort((a, b) => a - b);

        for (const level of levels) {
            const levelData = levelDistribution.get(level);
            cumulativeOutputs += levelData.outputs;
            cumulativeInputs += levelData.inputs;

            if (levelData.inputs > 0 || levelData.outputs > 0) {
                hasAnyPorts = true;
            }

            // 关键检查：在任何层级，累积输出都不能少于累积输入
            if (cumulativeOutputs < cumulativeInputs) {
                console.error(`[HIERARCHICAL-COMPENSATION] ❌ DAG violation at level ${level}: cumulative outputs (${cumulativeOutputs}) < cumulative inputs (${cumulativeInputs})`);
                return false;
            }

            // 检查同层级自连接问题
            if (levelData.inputs > 0 && levelData.outputs > 0) {
                // 同层级有输入和输出，需要验证是否会导致必须的自连接
                if (this.wouldRequireSameLevelConnection(portTypeKey, level, levelData, levelDistribution)) {
                    console.error(`[HIERARCHICAL-COMPENSATION] ❌ Level ${level} would require same-level connections for ${portTypeKey}`);
                    return false;
                }
            }
        }

        if (!hasAnyPorts) {
            console.log(`[HIERARCHICAL-COMPENSATION] ✅ ${portTypeKey} has no ports, trivially valid`);
            return true;
        }

        // 最终检查：总输出必须等于总输入
        if (cumulativeOutputs !== cumulativeInputs) {
            console.error(`[HIERARCHICAL-COMPENSATION] ❌ Port imbalance: ${cumulativeOutputs} outputs vs ${cumulativeInputs} inputs`);
            return false;
        }

        console.log(`[HIERARCHICAL-COMPENSATION] ✅ ${portTypeKey} port distribution satisfies DAG constraints`);
        return true;
    }

    // 检查是否会要求同层级连接
    wouldRequireSameLevelConnection(portTypeKey, level, levelData, fullDistribution) {
        // 如果这个层级的输出端口数量超过了后续层级能消费的输入端口数量
        // 那么就必须在同层级内连接，这会违反DAG约束

        let futureInputCapacity = 0;
        const levels = Array.from(fullDistribution.keys()).sort((a, b) => a - b);

        for (const futureLevel of levels) {
            if (futureLevel > level) {
                futureInputCapacity += fullDistribution.get(futureLevel).inputs;
            }
        }

        // 如果当前层级的输出超过了未来所有层级的输入容量
        // 那么必须在当前层级内部连接，这是不允许的
        return levelData.outputs > futureInputCapacity;
    }

    // 验证端口类型的连通性
    validatePortTypeConnectivity(portTypeKey, nodes) {
        console.log(`[HIERARCHICAL-COMPENSATION] Validating connectivity for ${portTypeKey}`);

        // 计算该端口类型的输入输出端口数量
        let totalInputs = 0;
        let totalOutputs = 0;

        nodes.forEach(node => {
            const inputCount = (node.inputPorts || []).filter(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            ).length;
            const outputCount = (node.outputPorts || []).filter(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            ).length;

            totalInputs += inputCount;
            totalOutputs += outputCount;
        });

        // 基本平衡检查
        if (totalInputs !== totalOutputs) {
            console.error(`[HIERARCHICAL-COMPENSATION] ❌ ${portTypeKey} port imbalance: ${totalInputs} inputs vs ${totalOutputs} outputs`);
            return false;
        }

        if (totalInputs === 0) {
            return true; // 没有该类型端口，trivially valid
        }

        console.log(`[HIERARCHICAL-COMPENSATION] ✅ ${portTypeKey} connectivity valid: ${totalInputs} inputs, ${totalOutputs} outputs`);
        return true;
    }

    // 修复自连接问题
    fixSelfConnectionIssues(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Checking and fixing self-connection issues');
        const fixNodes = [];

        for (const portType of this.portTypes) {
            const portTypeKey = `${portType.shape}-${portType.color}`;

            // 检查该端口类型是否存在自连接问题
            const selfConnectionFix = this.createSelfConnectionFix(portTypeKey, nodes);
            if (selfConnectionFix.length > 0) {
                console.log(`[HIERARCHICAL-COMPENSATION] Generated ${selfConnectionFix.length} nodes to fix self-connection issue for ${portTypeKey}`);
                fixNodes.push(...selfConnectionFix);
            }
        }

        return fixNodes;
    }

    // 为特定端口类型创建自连接修复节点
    createSelfConnectionFix(portTypeKey, nodes) {
        // 解析端口类型
        const [shape, color] = portTypeKey.split('-');
        const portType = { shape, color };

        // 找到使用该端口类型的所有节点
        const typeNodes = nodes.filter(node => {
            const hasInput = (node.inputPorts || []).some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );
            const hasOutput = (node.outputPorts || []).some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );
            return hasInput || hasOutput;
        });

        if (typeNodes.length === 0) {
            return []; // 没有该类型端口
        }

        // 分类节点
        const sourceNodes = []; // 只有输出端口的节点
        const sinkNodes = [];   // 只有输入端口的节点
        const bridgeNodes = []; // 既有输入又有输出端口的节点

        typeNodes.forEach(node => {
            const hasInput = (node.inputPorts || []).some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );
            const hasOutput = (node.outputPorts || []).some(port =>
                (port.portTypeKey || `${port.type}-${port.color}`) === portTypeKey
            );

            if (hasInput && hasOutput) {
                bridgeNodes.push(node);
            } else if (hasOutput) {
                sourceNodes.push(node);
            } else if (hasInput) {
                sinkNodes.push(node);
            }
        });

        const fixNodes = [];

        // 情况1：只有一个桥接节点，需要添加源节点和汇节点
        if (bridgeNodes.length === 1 && sourceNodes.length === 0 && sinkNodes.length === 0) {
            const bridgeNode = bridgeNodes[0];
            const bridgeLevel = this.getNodeLevel(bridgeNode);

            // 添加源节点（层级0）
            const sourceNodeId = `fix_source_${portTypeKey}_${Date.now()}`;
            const sourceNode = {
                id: sourceNodeId,
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [{
                    id: `${sourceNodeId}_out_0`,
                    type: portType.shape,
                    color: portType.color,
                    side: 'output',
                    nodeId: sourceNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                }],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'self_connection_fix',
                purpose: `fix_source_${portTypeKey}`
            };
            fixNodes.push(sourceNode);

            // 添加汇节点（最大层级）
            const sinkNodeId = `fix_sink_${portTypeKey}_${Date.now() + 1}`;
            const sinkNode = {
                id: sinkNodeId,
                type: 'end',
                level: Math.max(this.maxLevel, bridgeLevel + 1),
                depth: Math.max(this.maxLevel, bridgeLevel + 1) * 100,
                inputPorts: [{
                    id: `${sinkNodeId}_in_0`,
                    type: portType.shape,
                    color: portType.color,
                    side: 'input',
                    nodeId: sinkNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                }],
                outputPorts: [],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'self_connection_fix',
                purpose: `fix_sink_${portTypeKey}`
            };
            fixNodes.push(sinkNode);

            console.log(`[HIERARCHICAL-COMPENSATION] Fixed single bridge node issue for ${portTypeKey}: added source and sink nodes`);
        }

        // 情况2：缺少源节点
        else if (sourceNodes.length === 0 && bridgeNodes.length === 0 && sinkNodes.length > 0) {
            const sourceNodeId = `fix_source_${portTypeKey}_${Date.now()}`;
            const sourceNode = {
                id: sourceNodeId,
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [{
                    id: `${sourceNodeId}_out_0`,
                    type: portType.shape,
                    color: portType.color,
                    side: 'output',
                    nodeId: sourceNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                }],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'self_connection_fix',
                purpose: `fix_source_${portTypeKey}`
            };
            fixNodes.push(sourceNode);

            console.log(`[HIERARCHICAL-COMPENSATION] Fixed missing source node for ${portTypeKey}`);
        }

        // 情况3：缺少汇节点
        else if (sinkNodes.length === 0 && bridgeNodes.length === 0 && sourceNodes.length > 0) {
            const sinkNodeId = `fix_sink_${portTypeKey}_${Date.now()}`;
            const sinkNode = {
                id: sinkNodeId,
                type: 'end',
                level: this.maxLevel,
                depth: this.maxLevel * 100,
                inputPorts: [{
                    id: `${sinkNodeId}_in_0`,
                    type: portType.shape,
                    color: portType.color,
                    side: 'input',
                    nodeId: sinkNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                }],
                outputPorts: [],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'self_connection_fix',
                purpose: `fix_sink_${portTypeKey}`
            };
            fixNodes.push(sinkNode);

            console.log(`[HIERARCHICAL-COMPENSATION] Fixed missing sink node for ${portTypeKey}`);
        }

        return fixNodes;
    }

    // ========== 端口成对生成的核心算法 ==========

    // 为特定端口类型进行补偿
    compensatePortType(portTypeKey, nodes) {
        console.log(`[HIERARCHICAL-COMPENSATION] Compensating port type ${portTypeKey}`);

        // 1. 分析当前端口分布
        const distribution = this.buildPortLevelDistribution(portTypeKey, nodes);

        // 2. 检查是否需要补偿
        if (this.validatePortLevelDAG(portTypeKey, distribution)) {
            console.log(`[HIERARCHICAL-COMPENSATION] ${portTypeKey} already valid, no compensation needed`);
            return [];
        }

        // 3. 应用端口成对生成策略
        return this.generatePortPairsForType(portTypeKey, distribution);
    }

    // 为特定端口类型生成成对端口
    generatePortPairsForType(portTypeKey, distribution) {
        const [shape, color] = portTypeKey.split('-');
        const compensationNodes = [];

        // 计算总的端口不平衡
        let totalInputs = 0;
        let totalOutputs = 0;

        for (const [level, data] of distribution.entries()) {
            totalInputs += data.inputs;
            totalOutputs += data.outputs;
        }

        const imbalance = totalOutputs - totalInputs;
        console.log(`[HIERARCHICAL-COMPENSATION] ${portTypeKey} port balance: ${totalOutputs} outputs, ${totalInputs} inputs, imbalance: ${imbalance}`);

        // 首先修复层级分布问题（这是关键！）
        const levelFixNodes = this.fixLevelDistributionIssues(portTypeKey, distribution);
        compensationNodes.push(...levelFixNodes);

        // 然后处理总体不平衡
        if (imbalance > 0) {
            // 输出过多：在最高层级添加输入端口（汇节点）
            console.log(`[HIERARCHICAL-COMPENSATION] ${portTypeKey} has ${imbalance} excess outputs, creating sink nodes`);

            const sinkNodeId = `pair_sink_${portTypeKey}_${Date.now()}`;
            const sinkNode = {
                id: sinkNodeId,
                type: 'end',
                level: this.maxLevel,
                depth: this.maxLevel * 100,
                inputPorts: [],
                outputPorts: [],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'port_pair_generation',
                purpose: `sink_for_${portTypeKey}`
            };

            // 添加足够的输入端口来平衡
            for (let i = 0; i < imbalance; i++) {
                sinkNode.inputPorts.push({
                    id: `${sinkNodeId}_in_${i}`,
                    type: shape,
                    color: color,
                    side: 'input',
                    nodeId: sinkNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                });
            }

            compensationNodes.push(sinkNode);

        } else if (imbalance < 0) {
            // 输入过多：在层级0添加输出端口（源节点）
            console.log(`[HIERARCHICAL-COMPENSATION] ${portTypeKey} has ${-imbalance} excess inputs, creating source nodes`);

            const sourceNodeId = `pair_source_${portTypeKey}_${Date.now()}`;
            const sourceNode = {
                id: sourceNodeId,
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [],
                x: 50, y: 50,
                area: 'temporary',
                createdBy: 'port_pair_generation',
                purpose: `source_for_${portTypeKey}`
            };

            // 添加足够的输出端口来平衡
            for (let i = 0; i < -imbalance; i++) {
                sourceNode.outputPorts.push({
                    id: `${sourceNodeId}_out_${i}`,
                    type: shape,
                    color: color,
                    side: 'output',
                    nodeId: sourceNodeId,
                    portTypeKey: portTypeKey,
                    x: 0, y: 0
                });
            }

            compensationNodes.push(sourceNode);
        }

        return compensationNodes;
    }

    // 修复层级分布问题
    fixLevelDistributionIssues(portTypeKey, distribution) {
        const [shape, color] = portTypeKey.split('-');
        const fixNodes = [];

        // 检查累积约束违规
        let cumulativeOutputs = 0;
        let cumulativeInputs = 0;
        const levels = Array.from(distribution.keys()).sort((a, b) => a - b);

        for (const level of levels) {
            const data = distribution.get(level);
            cumulativeOutputs += data.outputs;
            cumulativeInputs += data.inputs;

            // 如果累积输出 < 累积输入，需要在当前或更低层级添加输出端口
            if (cumulativeOutputs < cumulativeInputs) {
                const deficit = cumulativeInputs - cumulativeOutputs;
                console.log(`[HIERARCHICAL-COMPENSATION] Level ${level} cumulative deficit: ${deficit}, adding source node`);

                const sourceNodeId = `level_fix_source_${portTypeKey}_L${level}_${Date.now()}`;
                const sourceNode = {
                    id: sourceNodeId,
                    type: level === 0 ? 'start' : 'intermediate',
                    level: Math.max(0, level - 1), // 在更低层级添加
                    depth: Math.max(0, level - 1) * 100,
                    inputPorts: [],
                    outputPorts: [],
                    x: 50, y: 50,
                    area: 'temporary',
                    createdBy: 'level_distribution_fix',
                    purpose: `level_fix_${portTypeKey}`
                };

                for (let i = 0; i < deficit; i++) {
                    sourceNode.outputPorts.push({
                        id: `${sourceNodeId}_out_${i}`,
                        type: shape,
                        color: color,
                        side: 'output',
                        nodeId: sourceNodeId,
                        portTypeKey: portTypeKey,
                        x: 0, y: 0
                    });
                }

                fixNodes.push(sourceNode);
                cumulativeOutputs += deficit; // 更新累积计数
            }
        }

        return fixNodes;
    }

    // ========== DAG感知的端口成对生成 ==========

    // 分析端口在各层级的分布
    analyzePortDistribution(nodes) {
        const distribution = new Map();

        // 为每种端口类型创建层级分布
        this.portTypes.forEach(portType => {
            const portTypeKey = `${portType.shape}-${portType.color}`;
            distribution.set(portTypeKey, this.buildPortLevelDistribution(portTypeKey, nodes));
        });

        return distribution;
    }

    // 生成DAG兼容的端口
    generateDAGCompliantPorts(portDistribution, existingNodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Generating DAG-compliant port pairs');
        const compensationNodes = [];

        for (const [portTypeKey, levelDistribution] of portDistribution.entries()) {
            const portPairs = this.generatePortPairs(portTypeKey, levelDistribution);
            const newNodes = this.createNodesFromPortPairs(portTypeKey, portPairs);
            compensationNodes.push(...newNodes);
        }

        return compensationNodes;
    }

    // 为特定端口类型生成成对的端口
    generatePortPairs(portTypeKey, levelDistribution) {
        console.log(`[HIERARCHICAL-COMPENSATION] Analyzing port debt for ${portTypeKey}`);

        const levels = Array.from(levelDistribution.keys()).sort((a, b) => a - b);

        // 检查是否已经满足DAG约束
        if (this.validatePortLevelDAG(portTypeKey, levelDistribution)) {
            console.log(`[HIERARCHICAL-COMPENSATION] ${portTypeKey} already satisfies DAG constraints, no pairs needed`);
            return [];
        }

        // 简单策略：找到违反DAG约束的层级，直接修复
        const violations = this.findDAGViolations(portTypeKey, levelDistribution);
        const portPairs = [];

        for (const violation of violations) {
            if (violation.type === 'cumulative_debt') {
                // 在最高层级添加输入端口来消除累积债务
                for (let i = 0; i < violation.amount; i++) {
                    portPairs.push({
                        outputLevel: null, // 不需要新的输出端口
                        inputLevel: this.maxLevel,
                        portTypeKey: portTypeKey
                    });
                }
            } else if (violation.type === 'same_level_required') {
                // 在更高层级添加输入端口来避免同层级连接
                for (let i = 0; i < violation.amount; i++) {
                    portPairs.push({
                        outputLevel: null,
                        inputLevel: Math.min(violation.level + 1, this.maxLevel),
                        portTypeKey: portTypeKey
                    });
                }
            }
        }

        console.log(`[HIERARCHICAL-COMPENSATION] Generated ${portPairs.length} port pairs to fix ${violations.length} violations`);
        return portPairs;
    }

    // 找到DAG违规
    findDAGViolations(portTypeKey, levelDistribution) {
        const violations = [];
        const levels = Array.from(levelDistribution.keys()).sort((a, b) => a - b);

        let cumulativeOutputs = 0;
        let cumulativeInputs = 0;

        for (const level of levels) {
            const levelData = levelDistribution.get(level);
            cumulativeOutputs += levelData.outputs;
            cumulativeInputs += levelData.inputs;

            // 违规1：累积输出 < 累积输入
            if (cumulativeOutputs < cumulativeInputs) {
                violations.push({
                    type: 'cumulative_debt',
                    level: level,
                    amount: cumulativeInputs - cumulativeOutputs
                });
            }

            // 违规2：同层级会要求内部连接
            if (levelData.inputs > 0 && levelData.outputs > 0) {
                const futureInputCapacity = this.calculateFutureInputCapacity(level, levelDistribution);
                if (levelData.outputs > futureInputCapacity) {
                    violations.push({
                        type: 'same_level_required',
                        level: level,
                        amount: levelData.outputs - futureInputCapacity
                    });
                }
            }
        }

        return violations;
    }

    // 计算未来层级的输入容量
    calculateFutureInputCapacity(currentLevel, levelDistribution) {
        let capacity = 0;
        const levels = Array.from(levelDistribution.keys()).sort((a, b) => a - b);

        for (const level of levels) {
            if (level > currentLevel) {
                capacity += levelDistribution.get(level).inputs;
            }
        }

        return capacity;
    }

    // 从端口对创建节点
    createNodesFromPortPairs(portTypeKey, portPairs) {
        const [shape, color] = portTypeKey.split('-');
        const nodes = [];

        if (portPairs.length === 0) {
            return nodes;
        }

        // 按层级分组端口需求
        const levelNeeds = new Map();

        portPairs.forEach(pair => {
            // 只为输入端口创建节点（输出端口为null表示不需要新的输出端口）
            if (pair.inputLevel !== null) {
                if (!levelNeeds.has(pair.inputLevel)) {
                    levelNeeds.set(pair.inputLevel, { inputs: 0, outputs: 0 });
                }
                levelNeeds.get(pair.inputLevel).inputs++;
            }
        });

        // 为每个需要的层级创建节点
        for (const [level, needs] of levelNeeds.entries()) {
            if (needs.inputs > 0) {
                const nodeId = `dag_fix_${portTypeKey}_L${level}_${Date.now()}`;

                const node = {
                    id: nodeId,
                    type: level === 0 ? 'start' : level === this.maxLevel ? 'end' : 'intermediate',
                    level: level,
                    depth: level * 100,
                    inputPorts: [],
                    outputPorts: [],
                    x: 50, y: 50,
                    area: 'temporary',
                    createdBy: 'dag_port_pairs',
                    purpose: `dag_fix_${portTypeKey}_level_${level}`
                };

                // 只添加需要的输入端口
                for (let i = 0; i < needs.inputs; i++) {
                    node.inputPorts.push({
                        id: `${nodeId}_in_${i}`,
                        type: shape,
                        color: color,
                        side: 'input',
                        nodeId: nodeId,
                        portTypeKey: portTypeKey,
                        x: 0, y: 0
                    });
                }

                nodes.push(node);
                console.log(`[HIERARCHICAL-COMPENSATION] Created DAG fix node at level ${level} with ${needs.inputs} input ports`);
            }
        }

        return nodes;
    }

    // 验证DAG兼容的分布
    validateDAGCompliantDistribution(portDistribution) {
        console.log('[HIERARCHICAL-COMPENSATION] Validating DAG-compliant distribution');

        for (const [portTypeKey, levelDistribution] of portDistribution.entries()) {
            if (!this.validatePortLevelDAG(portTypeKey, levelDistribution)) {
                console.error(`[HIERARCHICAL-COMPENSATION] DAG validation failed for ${portTypeKey}`);
                return false;
            }
        }

        console.log('[HIERARCHICAL-COMPENSATION] All port types have DAG-compliant distributions');
        return true;
    }

    // 紧急DAG修复
    emergencyDAGFix(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Applying emergency DAG fix');

        const fixedNodes = [];

        // 为每种端口类型应用最简单的修复：创建线性流
        this.portTypes.forEach(portType => {
            const portTypeKey = `${portType.shape}-${portType.color}`;
            const linearFlow = this.createLinearFlow(portTypeKey);
            fixedNodes.push(...linearFlow);
        });

        return [...nodes, ...fixedNodes];
    }

    // 创建线性流（最简单的DAG）
    createLinearFlow(portTypeKey) {
        const [shape, color] = portTypeKey.split('-');
        const nodes = [];

        // 创建简单的源->汇流
        const sourceId = `emergency_source_${portTypeKey}_${Date.now()}`;
        const sinkId = `emergency_sink_${portTypeKey}_${Date.now() + 1}`;

        const sourceNode = {
            id: sourceId,
            type: 'start',
            level: 0,
            depth: 0,
            inputPorts: [],
            outputPorts: [{
                id: `${sourceId}_out_0`,
                type: shape,
                color: color,
                side: 'output',
                nodeId: sourceId,
                portTypeKey: portTypeKey,
                x: 0, y: 0
            }],
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'emergency_dag_fix',
            purpose: `emergency_linear_${portTypeKey}`
        };

        const sinkNode = {
            id: sinkId,
            type: 'end',
            level: this.maxLevel,
            depth: this.maxLevel * 100,
            inputPorts: [{
                id: `${sinkId}_in_0`,
                type: shape,
                color: color,
                side: 'input',
                nodeId: sinkId,
                portTypeKey: portTypeKey,
                x: 0, y: 0
            }],
            outputPorts: [],
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'emergency_dag_fix',
            purpose: `emergency_linear_${portTypeKey}`
        };

        nodes.push(sourceNode, sinkNode);
        return nodes;
    }

    isDAGUsingKahn(graph, portTypeKey) {
        if (graph.size === 0) {
            console.log(`[HIERARCHICAL-COMPENSATION] Port type ${portTypeKey} has no nodes, trivially valid`);
            return true;
        }

        // Kahn算法实现
        const inDegree = new Map();
        const queue = [];

        // 初始化入度
        graph.forEach((nodeData, nodeId) => {
            inDegree.set(nodeId, nodeData.inEdges.size);
            if (nodeData.inEdges.size === 0) {
                queue.push(nodeId);
            }
        });

        let processedCount = 0;

        while (queue.length > 0) {
            const currentNodeId = queue.shift();
            processedCount++;

            const currentNodeData = graph.get(currentNodeId);

            // 处理所有出边
            currentNodeData.outEdges.forEach(targetNodeId => {
                const newInDegree = inDegree.get(targetNodeId) - 1;
                inDegree.set(targetNodeId, newInDegree);

                if (newInDegree === 0) {
                    queue.push(targetNodeId);
                }
            });
        }

        const isDAG = processedCount === graph.size;

        if (isDAG) {
            console.log(`[HIERARCHICAL-COMPENSATION] ✅ Port type ${portTypeKey} forms valid DAG with ${graph.size} nodes`);
        } else {
            console.error(`[HIERARCHICAL-COMPENSATION] ❌ Port type ${portTypeKey} contains cycles: processed ${processedCount}/${graph.size} nodes`);
        }

        return isDAG;
    }

    validateGlobalTopologicalOrder(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Validating global topological order');

        // 首先更新最大层级
        this.updateMaxLevel(nodes);

        // 验证层级约束：所有连接都必须从低层级指向高层级
        for (const node of nodes) {
            const nodeLevel = this.getNodeLevel(node);

            // 检查节点类型与层级的一致性
            if (this.isSourceNode(node) && nodeLevel !== 0) {
                console.warn(`[HIERARCHICAL-COMPENSATION] Correcting source node ${node.id} level from ${nodeLevel} to 0`);
                node.level = 0;
                node.depth = 0;
            }

            if (this.isSinkNode(node) && nodeLevel > this.maxLevel) {
                console.warn(`[HIERARCHICAL-COMPENSATION] Correcting sink node ${node.id} level from ${nodeLevel} to ${this.maxLevel}`);
                node.level = this.maxLevel;
                node.depth = this.maxLevel * 100;
            }

            // 中间节点层级自动修正（不报错，只修正）
            if (!this.isSourceNode(node) && !this.isSinkNode(node)) {
                const currentLevel = this.getNodeLevel(node);

                // 如果层级不合理，自动修正
                if (currentLevel <= 0) {
                    console.log(`[HIERARCHICAL-COMPENSATION] Auto-correcting intermediate node ${node.id} level from ${currentLevel} to 1`);
                    node.level = 1;
                    node.depth = 100;
                } else if (this.maxLevel > 0 && currentLevel >= this.maxLevel) {
                    const newLevel = Math.max(1, this.maxLevel - 1);
                    console.log(`[HIERARCHICAL-COMPENSATION] Auto-correcting intermediate node ${node.id} level from ${currentLevel} to ${newLevel}`);
                    node.level = newLevel;
                    node.depth = newLevel * 100;
                }
                // 如果层级合理，保持不变
            }
        }

        console.log('[HIERARCHICAL-COMPENSATION] ✅ Global topological order validated and corrected');
        return true;
    }

    updateMaxLevel(nodes) {
        // 计算当前最大层级
        let maxLevel = 0;
        nodes.forEach(node => {
            const level = this.getNodeLevel(node);
            if (level > maxLevel) {
                maxLevel = level;
            }
        });

        // 确保最大层级至少为2（起点0，终点至少1）
        this.maxLevel = Math.max(maxLevel, 2);
        console.log(`[HIERARCHICAL-COMPENSATION] Updated max level to ${this.maxLevel}`);
    }

    validateNodeReachability(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Validating node reachability');

        // 简化的可达性检查：确保每个层级都有节点
        const levelCounts = new Map();

        nodes.forEach(node => {
            const level = this.getNodeLevel(node);
            levelCounts.set(level, (levelCounts.get(level) || 0) + 1);
        });

        // 检查层级连续性
        for (let level = 0; level <= this.maxLevel; level++) {
            if (!levelCounts.has(level) || levelCounts.get(level) === 0) {
                console.warn(`[HIERARCHICAL-COMPENSATION] No nodes at level ${level}, but this may be acceptable`);
            }
        }

        console.log('[HIERARCHICAL-COMPENSATION] ✅ Node reachability validation passed');
        return true;
    }

    // ========== 动态难度调整 ==========

    adjustDifficultyForWave(wave) {
        console.log(`[HIERARCHICAL-COMPENSATION] Adjusting difficulty for wave ${wave}`);

        this.currentWave = wave;

        // 根据您的设计方案调整难度参数
        if (wave <= 5) {
            this.difficultyConfig = {
                maxPortTypes: 2,
                maxModifications: 1 + Math.floor(wave / 2),
                maxSourceNodes: 1,
                maxSinkNodes: 1
            };
        } else if (wave <= 10) {
            this.difficultyConfig = {
                maxPortTypes: 3,
                maxModifications: 2 + Math.floor(wave / 3),
                maxSourceNodes: 2,
                maxSinkNodes: 2
            };
        } else if (wave <= 15) {
            this.difficultyConfig = {
                maxPortTypes: 4,
                maxModifications: 3 + Math.floor(wave / 4),
                maxSourceNodes: 3,
                maxSinkNodes: 3
            };
        } else {
            this.difficultyConfig = {
                maxPortTypes: Math.min(5 + Math.floor(wave / 10), this.portTypes.length),
                maxModifications: 4 + Math.floor(wave / 5),
                maxSourceNodes: 4,
                maxSinkNodes: 4
            };
        }

        console.log('[HIERARCHICAL-COMPENSATION] Difficulty adjusted:', this.difficultyConfig);
    }

    // ========== 紧急补偿机制 ==========

    emergencyCompensation(nodePool) {
        console.log('[HIERARCHICAL-COMPENSATION] Applying emergency compensation');

        // 使用最简单的策略：为每种不平衡类型创建专门节点
        const imbalance = this.calculatePortImbalance(nodePool);
        const emergencyNodes = [];

        imbalance.forEach((stat, portType) => {
            if (stat.delta > 0) {
                // 输出过多，创建终点节点
                const emergencyNode = this.createSinkNode(portType, stat.delta, this.maxLevel + 1);
                emergencyNode.createdBy = 'emergency_compensation';
                emergencyNodes.push(emergencyNode);

            } else if (stat.delta < 0) {
                // 输入过多，创建起点节点
                const emergencyNode = this.createSourceNode(portType, -stat.delta);
                emergencyNode.createdBy = 'emergency_compensation';
                emergencyNodes.push(emergencyNode);
            }
        });

        // 更新最大层级
        if (emergencyNodes.some(node => this.isSinkNode(node))) {
            this.maxLevel++;
        }

        console.log(`[HIERARCHICAL-COMPENSATION] Emergency compensation created ${emergencyNodes.length} nodes`);
        return emergencyNodes;
    }

    // ========== 游戏循环接口 ==========
    // 注意：主要的generateNextRound方法已移至文件末尾的多样性增强版本

    mergeGameStateNodes(newNodes, gameState) {
        if (!gameState) {
            return newNodes;
        }

        const allNodes = [
            ...newNodes,
            ...(gameState.temporaryNodes || []),
            ...(gameState.placedNodes || [])
        ];

        // 去重
        const uniqueNodes = this.deduplicateNodes(allNodes);

        console.log(`[HIERARCHICAL-COMPENSATION] Merged game state: ${uniqueNodes.length} unique nodes`);
        return uniqueNodes;
    }

    deduplicateNodes(nodes) {
        const seen = new Set();
        const uniqueNodes = [];

        nodes.forEach(node => {
            if (!seen.has(node.id)) {
                seen.add(node.id);
                uniqueNodes.push(node);
            }
        });

        return uniqueNodes;
    }

    // ========== 修改操作处理 ==========

    applyModifications(nodes, modifications) {
        console.log(`[HIERARCHICAL-COMPENSATION] Applying ${modifications.length} modifications`);

        let modifiedNodes = JSON.parse(JSON.stringify(nodes)); // 深拷贝

        modifications.forEach((mod, index) => {
            console.log(`[HIERARCHICAL-COMPENSATION] Applying modification ${index + 1}: ${mod.type}`);

            switch (mod.type) {
                case 'add_node':
                    modifiedNodes.push(this.createModificationNode(mod));
                    break;

                case 'del_node':
                    modifiedNodes = modifiedNodes.filter(n => n.id !== mod.nodeId);
                    break;

                case 'add_port':
                    this.addPortToNode(modifiedNodes, mod);
                    break;

                case 'del_port':
                    this.removePortFromNode(modifiedNodes, mod);
                    break;

                case 'modify_node_type':
                    this.modifyNodeType(modifiedNodes, mod);
                    break;

                default:
                    console.warn(`[HIERARCHICAL-COMPENSATION] Unknown modification type: ${mod.type}`);
            }
        });

        console.log(`[HIERARCHICAL-COMPENSATION] Modifications applied, result: ${modifiedNodes.length} nodes`);
        return modifiedNodes;
    }

    createModificationNode(mod) {
        const nodeId = `mod_node_${this.nodeCounter++}`;
        const level = this.determineSafeLevel(mod);

        const node = {
            id: nodeId,
            type: mod.nodeType || 'intermediate',
            level: level,
            depth: level * 100,
            inputPorts: [],
            outputPorts: [],
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'modification',
            purpose: `modification_${mod.type}`
        };

        // 根据节点类型添加端口
        if (mod.nodeType !== 'end') {
            const outputCount = mod.outputCount || 1;
            for (let i = 0; i < outputCount; i++) {
                const portType = this.getRandomPortType();
                node.outputPorts.push(this.createPort(nodeId, 'output', i, portType));
            }
        }

        if (mod.nodeType !== 'start') {
            const inputCount = mod.inputCount || 1;
            for (let i = 0; i < inputCount; i++) {
                const portType = this.getRandomPortType();
                node.inputPorts.push(this.createPort(nodeId, 'input', i, portType));
            }
        }

        return node;
    }

    determineSafeLevel(mod) {
        if (mod.nodeType === 'start') return 0;
        if (mod.nodeType === 'end') return this.maxLevel + 1;

        // 中间节点：在现有层级间随机选择
        const safeLevel = Math.max(1, Math.floor(Math.random() * Math.max(1, this.maxLevel - 1)) + 1);
        return safeLevel;
    }

    getRandomPortType() {
        const availableTypes = this.portTypes.slice(0, this.difficultyConfig.maxPortTypes);
        const randomType = availableTypes[Math.floor(Math.random() * availableTypes.length)];
        return `${randomType.shape}-${randomType.color}`;
    }

    createPort(nodeId, side, index, portTypeKey) {
        const [shape, color] = portTypeKey.split('-');
        return {
            id: `${nodeId}_${side}_${index}`,
            type: shape,
            color: color,
            side: side,
            nodeId: nodeId,
            portTypeKey: portTypeKey,
            x: 0, y: 0
        };
    }

    addPortToNode(nodes, mod) {
        const node = nodes.find(n => n.id === mod.nodeId);
        if (!node) {
            console.warn(`[HIERARCHICAL-COMPENSATION] Node ${mod.nodeId} not found for port addition`);
            return;
        }

        // 检查节点容量
        if (this.getNodePortCount(node) >= this.maxPortsPerNode) {
            console.warn(`[HIERARCHICAL-COMPENSATION] Node ${mod.nodeId} at max capacity, cannot add port`);
            return;
        }

        // 检查节点类型约束
        if (this.isSourceNode(node) && mod.portSpec.side === 'input') {
            console.warn(`[HIERARCHICAL-COMPENSATION] Cannot add input port to source node ${mod.nodeId}`);
            return;
        }

        if (this.isSinkNode(node) && mod.portSpec.side === 'output') {
            console.warn(`[HIERARCHICAL-COMPENSATION] Cannot add output port to sink node ${mod.nodeId}`);
            return;
        }

        // 添加端口
        const portArray = mod.portSpec.side === 'input' ? node.inputPorts : node.outputPorts;
        const portIndex = portArray.length;
        const portTypeKey = `${mod.portSpec.type}-${mod.portSpec.color}`;

        const newPort = {
            id: `${mod.nodeId}_${mod.portSpec.side}_${Date.now()}_${portIndex}`,
            type: mod.portSpec.type,
            color: mod.portSpec.color,
            side: mod.portSpec.side,
            nodeId: mod.nodeId,
            portTypeKey: portTypeKey,
            x: 0, y: 0
        };

        portArray.push(newPort);
        console.log(`[HIERARCHICAL-COMPENSATION] Added ${mod.portSpec.side} port to node ${mod.nodeId}`);
    }

    removePortFromNode(nodes, mod) {
        const node = nodes.find(n => n.id === mod.nodeId);
        if (!node) {
            console.warn(`[HIERARCHICAL-COMPENSATION] Node ${mod.nodeId} not found for port removal`);
            return;
        }

        // 移除指定端口
        node.inputPorts = (node.inputPorts || []).filter(p => p.id !== mod.portId);
        node.outputPorts = (node.outputPorts || []).filter(p => p.id !== mod.portId);

        // 检查节点有效性
        const hasInputs = (node.inputPorts || []).length > 0;
        const hasOutputs = (node.outputPorts || []).length > 0;

        if (!hasInputs && !hasOutputs) {
            console.warn(`[HIERARCHICAL-COMPENSATION] Node ${mod.nodeId} has no ports after removal, marking for deletion`);
            node._markedForDeletion = true;
        }

        console.log(`[HIERARCHICAL-COMPENSATION] Removed port ${mod.portId} from node ${mod.nodeId}`);
    }

    modifyNodeType(nodes, mod) {
        const node = nodes.find(n => n.id === mod.nodeId);
        if (!node) {
            console.warn(`[HIERARCHICAL-COMPENSATION] Node ${mod.nodeId} not found for type modification`);
            return;
        }

        const oldType = node.type;
        node.type = mod.newType;

        // 调整层级
        if (mod.newType === 'start') {
            node.level = 0;
            node.depth = 0;
            node.inputPorts = []; // 起点无输入端口
        } else if (mod.newType === 'end') {
            node.level = this.maxLevel;
            node.depth = this.maxLevel * 100;
            node.outputPorts = []; // 终点无输出端口
        }

        console.log(`[HIERARCHICAL-COMPENSATION] Modified node ${mod.nodeId} type from ${oldType} to ${mod.newType}`);
    }

    // ========== 分析和调试方法 ==========

    analyzeNodePool(nodes) {
        const analysis = {
            totalNodes: nodes.length,
            nodesByLevel: new Map(),
            nodesByType: { start: 0, intermediate: 0, end: 0 },
            portTypeDistribution: new Map(),
            levelRange: { min: Infinity, max: -Infinity }
        };

        nodes.forEach(node => {
            // 按层级统计
            const level = this.getNodeLevel(node);
            analysis.nodesByLevel.set(level, (analysis.nodesByLevel.get(level) || 0) + 1);

            // 更新层级范围
            if (level < analysis.levelRange.min) analysis.levelRange.min = level;
            if (level > analysis.levelRange.max) analysis.levelRange.max = level;

            // 按类型统计
            if (this.isSourceNode(node)) {
                analysis.nodesByType.start++;
            } else if (this.isSinkNode(node)) {
                analysis.nodesByType.end++;
            } else {
                analysis.nodesByType.intermediate++;
            }

            // 端口类型分布
            [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
                const key = port.portTypeKey || `${port.type}-${port.color}`;
                analysis.portTypeDistribution.set(key, (analysis.portTypeDistribution.get(key) || 0) + 1);
            });
        });

        return analysis;
    }

    // ========== 向后兼容方法 ==========

    // 兼容旧的端口平衡计算方法
    calculatePortBalance(nodes) {
        return this.calculatePortImbalance(nodes);
    }

    // 兼容旧的分析方法
    analyzeGeneration(nodes) {
        return this.analyzeNodePool(nodes);
    }

    // 兼容旧的紧急修复方法
    emergencyFix(nodes) {
        return this.emergencyCompensation(nodes);
    }

    // 兼容旧的游戏状态紧急修复方法
    emergencyFixWithGameState(newNodes, gameState) {
        console.log('[HIERARCHICAL-COMPENSATION] emergencyFixWithGameState called with compatibility layer');

        if (!gameState) {
            return this.emergencyFix(newNodes);
        }

        // 合并所有节点
        const allNodes = [
            ...newNodes,
            ...(gameState.temporaryNodes || []),
            ...(gameState.placedNodes || [])
        ];

        const uniqueNodes = this.deduplicateNodes(allNodes);

        console.log('[HIERARCHICAL-COMPENSATION] Applying emergency fix with complete game state');

        // 计算完整的端口平衡
        const portBalance = this.calculatePortImbalance(uniqueNodes);
        const fixNodes = [];

        portBalance.forEach((stat, portTypeKey) => {
            if (stat.delta !== 0) {
                const [shape, color] = portTypeKey.split('-');

                console.log(`[HIERARCHICAL-COMPENSATION] Emergency fix needed for ${portTypeKey}: delta=${stat.delta}`);

                if (stat.delta > 0) {
                    // 输出过多，创建终点节点
                    for (let i = 0; i < Math.abs(stat.delta); i++) {
                        const fixNodeId = `emergency_end_${portTypeKey}_${Date.now()}_${i}`;
                        const fixNode = {
                            id: fixNodeId,
                            type: 'end',
                            level: this.maxLevel + 1,
                            depth: (this.maxLevel + 1) * 100,
                            inputPorts: [{
                                id: `${fixNodeId}_in_0`,
                                type: shape,
                                color: color,
                                side: 'input',
                                nodeId: fixNodeId,
                                portTypeKey: portTypeKey,
                                x: 0, y: 0
                            }],
                            outputPorts: [],
                            x: 50, y: 50,
                            area: 'temporary',
                            createdBy: 'emergency_fix',
                            purpose: `emergency_balance_${portTypeKey}`
                        };
                        fixNodes.push(fixNode);
                    }

                } else {
                    // 输入过多，创建起点节点
                    for (let i = 0; i < Math.abs(stat.delta); i++) {
                        const fixNodeId = `emergency_start_${portTypeKey}_${Date.now()}_${i}`;
                        const fixNode = {
                            id: fixNodeId,
                            type: 'start',
                            level: 0,
                            depth: 0,
                            inputPorts: [],
                            outputPorts: [{
                                id: `${fixNodeId}_out_0`,
                                type: shape,
                                color: color,
                                side: 'output',
                                nodeId: fixNodeId,
                                portTypeKey: portTypeKey,
                                x: 0, y: 0
                            }],
                            x: 50, y: 50,
                            area: 'temporary',
                            createdBy: 'emergency_fix',
                            purpose: `emergency_balance_${portTypeKey}`
                        };
                        fixNodes.push(fixNode);
                    }
                }
            }
        });

        // 只返回新生成的节点（包括修复节点）
        const finalNewNodes = [...newNodes, ...fixNodes];

        // 验证修复后的完整状态
        const allNodesAfterFix = [...uniqueNodes, ...fixNodes];
        const validation = this.validateStrongSolvability(allNodesAfterFix, []);

        if (!validation) {
            console.error('[HIERARCHICAL-COMPENSATION] Emergency fix failed to create valid node pool');
        } else {
            console.log('[HIERARCHICAL-COMPENSATION] Emergency fix successfully created balanced node pool');
        }

        return finalNewNodes;
    }

    // 增强的多样性游戏循环方法
    generateNextRound(existingNodes, modifications, roundConfig, gameState = null) {
        console.log('[HIERARCHICAL-COMPENSATION] generateNextRound called with DIVERSITY ENHANCEMENT');

        // 生成多样化的端口配置
        const wave = roundConfig?.wave || 1;
        const difficulty = roundConfig?.difficulty || wave;
        const diversityConfig = this.diversityManager.generateDiversePortConfiguration(wave, difficulty);

        console.log(`[DIVERSITY] Wave ${wave} using diverse port types:`, diversityConfig.portTypes.map(pt => `${pt.shape}-${pt.color}`));

        // 更新算法的端口类型为多样化配置
        this.portTypes = diversityConfig.portTypes;

        // 调整难度
        if (roundConfig && roundConfig.wave) {
            this.adjustDifficultyForWave(roundConfig.wave);
        }

        // 1. 应用修改操作
        const modifiedNodes = this.applyModifications(existingNodes, modifications);

        // 2. 合并游戏状态中的所有节点
        const allNodes = this.mergeGameStateNodes(modifiedNodes, gameState);

        // 3. 验证修改后的节点池是否仍然有合理的层级分布
        this.ensureReasonableLayerDistribution(allNodes);

        // 4. 执行分层补偿（现在使用多样化的端口类型）
        const compensationNodes = this.compensate(allNodes);

        // 5. 返回所有节点（修改后的现有节点 + 新补偿节点）
        const allResultNodes = [...allNodes, ...compensationNodes];

        console.log(`[HIERARCHICAL-COMPENSATION] Generated ${allResultNodes.length} total nodes (${allNodes.length} existing + ${compensationNodes.length} new) for next round`);
        return allResultNodes;
    }

    // 确保合理的层级分布
    ensureReasonableLayerDistribution(nodes) {
        console.log('[HIERARCHICAL-COMPENSATION] Ensuring reasonable layer distribution');

        // 更新最大层级
        this.updateMaxLevel(nodes);

        // 检查每个层级是否有节点
        const layerCounts = new Map();
        nodes.forEach(node => {
            const level = this.getNodeLevel(node);
            layerCounts.set(level, (layerCounts.get(level) || 0) + 1);
        });

        // 如果中间层级缺失太多，添加桥接节点
        const missingLayers = [];
        for (let level = 1; level < this.maxLevel; level++) {
            if (!layerCounts.has(level) || layerCounts.get(level) === 0) {
                missingLayers.push(level);
            }
        }

        // 如果缺失的中间层级太多，添加桥接节点
        if (missingLayers.length > Math.floor(this.maxLevel / 2)) {
            console.log(`[HIERARCHICAL-COMPENSATION] Too many missing layers (${missingLayers.length}), adding bridge nodes`);

            // 为每个缺失的层级添加一个简单的桥接节点
            missingLayers.forEach(level => {
                const bridgeNode = this.createBridgeNode(level);
                nodes.push(bridgeNode);
                console.log(`[HIERARCHICAL-COMPENSATION] Added bridge node at level ${level}`);
            });
        }
    }

    // 创建桥接节点
    createBridgeNode(level) {
        const nodeId = `bridge_${level}_${Date.now()}`;

        // 选择一个随机端口类型
        const portType = this.portTypes[Math.floor(Math.random() * this.portTypes.length)];
        const portTypeKey = `${portType.shape}-${portType.color}`;

        return {
            id: nodeId,
            type: 'intermediate',
            level: level,
            depth: level * 100,
            inputPorts: [{
                id: `${nodeId}_in_0`,
                type: portType.shape,
                color: portType.color,
                side: 'input',
                nodeId: nodeId,
                portTypeKey: portTypeKey,
                x: 0, y: 0
            }],
            outputPorts: [{
                id: `${nodeId}_out_0`,
                type: portType.shape,
                color: portType.color,
                side: 'output',
                nodeId: nodeId,
                portTypeKey: portTypeKey,
                x: 0, y: 0
            }],
            x: 50, y: 50,
            area: 'temporary',
            createdBy: 'layer_bridge',
            purpose: `bridge_layer_${level}`
        };
    }

    // 兼容旧的验证方法
    validateSolvability(nodes) {
        return this.validateStrongSolvability(nodes, []);
    }

    validateTopology(nodes) {
        return this.validateGlobalTopologicalOrder(nodes);
    }

    validateConnectivity(nodes) {
        return this.validateNodeReachability(nodes);
    }
}

// ========== 导出 ==========
window.HierarchicalCompensationAlgorithm = HierarchicalCompensationAlgorithm;

// 向后兼容
window.CompensationAlgorithm = HierarchicalCompensationAlgorithm;
