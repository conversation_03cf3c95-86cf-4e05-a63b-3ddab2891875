// Test the structural modifications fix
console.log('🔧 测试结构修改平衡性修复...');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// Mock game state
const mockGameState = {
    nodes: [],
    temporaryNodes: [],
    portTypes: ['square', 'diamond', 'triangle', 'circle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
    infiniteMode: {
        currentWave: 2,
        adaptiveDifficulty: 1
    }
};

// Mock global functions
global.Node = TestNode;
global.gameState = mockGameState;

function getRandomPortType() {
    return mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
}

function getRandomPortColor() {
    return mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
}

// Fixed structural modifications function
function performStructuralModificationsFixed() {
    console.log('🔧 执行修复后的结构修改...');
    
    const modifications = {
        addedNodes: [],
        removedNodes: [],
        modifiedNodes: [],
        addedPorts: [],
        removedPorts: [],
        addedStartNodes: [],
        addedEndNodes: [],
        timestamp: Date.now()
    };

    const placedNodes = mockGameState.nodes.filter(node => 
        node.type !== 'start' && node.type !== 'end' && 
        !mockGameState.temporaryNodes.includes(node)
    );
    const startNodes = mockGameState.nodes.filter(node => node.type === 'start');
    const endNodes = mockGameState.nodes.filter(node => node.type === 'end');

    const wave = mockGameState.infiniteMode.currentWave;

    // 1. 随机添加新节点 (平衡端口)
    if (Math.random() < 0.5) { // 增加概率便于测试
        const newNode = new TestNode('normal');
        newNode.id = `wave_${wave}_added_${Date.now()}`;
        newNode.label = `W${wave}-Add`;
        
        const portType = getRandomPortType();
        const portColor = getRandomPortColor();
        newNode.addInputPort(portType, portColor);
        newNode.addOutputPort(portType, portColor);
        
        mockGameState.nodes.push(newNode);
        modifications.addedNodes.push(newNode);
        console.log(`➕ 添加平衡新节点: ${newNode.id} (${portType}-${portColor})`);
    }

    // 2. 修改现有节点的端口 (平衡修改)
    const nodesToModify = [...placedNodes].slice(0, Math.min(1, placedNodes.length));
    nodesToModify.forEach(node => {
        const originalState = {
            node: node,
            inputPorts: [...node.inputPorts],
            outputPorts: [...node.outputPorts]
        };

        // 平衡地添加端口对
        if (Math.random() < 0.8 && node.inputPorts.length + node.outputPorts.length < 6) { // 增加概率便于测试
            const portType = getRandomPortType();
            const portColor = getRandomPortColor();
            
            const inputPort = node.addInputPort(portType, portColor);
            const outputPort = node.addOutputPort(portType, portColor);
            
            modifications.addedPorts.push({ node, port: inputPort, side: 'input' });
            modifications.addedPorts.push({ node, port: outputPort, side: 'output' });
            
            console.log(`🔧 为节点 ${node.id} 添加平衡端口对: ${portType}-${portColor}`);
        }

        modifications.modifiedNodes.push(originalState);
    });

    // 3. 添加平衡的起点终点对
    if (Math.random() < 0.6) { // 增加概率便于测试
        const sharedPortType = getRandomPortType();
        const sharedPortColor = getRandomPortColor();
        
        const newStartNode = new TestNode('start');
        newStartNode.id = `start_wave_${wave}_${Date.now()}`;
        newStartNode.label = `Start-${wave}`;
        newStartNode.addOutputPort(sharedPortType, sharedPortColor);
        
        const newEndNode = new TestNode('end');
        newEndNode.id = `end_wave_${wave}_${Date.now()}`;
        newEndNode.label = `End-${wave}`;
        newEndNode.addInputPort(sharedPortType, sharedPortColor);
        
        mockGameState.nodes.push(newStartNode, newEndNode);
        modifications.addedStartNodes.push(newStartNode);
        modifications.addedEndNodes.push(newEndNode);
        
        console.log(`🎯 添加平衡起点终点对: ${sharedPortType}-${sharedPortColor}`);
    }

    return modifications;
}

// 分析端口平衡
function analyzePortBalance() {
    const allNodes = [...mockGameState.nodes, ...mockGameState.temporaryNodes];
    const typeCounts = new Map();
    
    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!typeCounts.has(typeKey)) {
                typeCounts.set(typeKey, { input: 0, output: 0 });
            }
            const count = typeCounts.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                count.input++;
            } else {
                count.output++;
            }
        });
    });

    const imbalances = [];
    for (const [typeKey, count] of typeCounts.entries()) {
        if (count.input !== count.output) {
            imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
        }
    }

    return {
        isBalanced: imbalances.length === 0,
        imbalances,
        typeCounts
    };
}

// 运行多轮测试
function runMultipleTests() {
    console.log('\n🧪 运行多轮结构修改测试...\n');
    
    let totalTests = 10;
    let balancedTests = 0;
    
    for (let test = 1; test <= totalTests; test++) {
        console.log(`--- 测试 ${test} ---`);
        
        // 重置状态
        mockGameState.nodes = [];
        mockGameState.temporaryNodes = [];
        
        // 创建基础场景
        const startNode = new TestNode('start');
        startNode.addOutputPort('square', '#ff5252');
        const endNode = new TestNode('end');
        endNode.addInputPort('square', '#ff5252');
        const bridgeNode = new TestNode('normal');
        bridgeNode.addInputPort('square', '#ff5252');
        bridgeNode.addOutputPort('square', '#ff5252');
        
        mockGameState.nodes.push(startNode, endNode, bridgeNode);
        
        console.log('初始状态: 1起点, 1终点, 1桥接节点');
        
        // 执行结构修改
        const modifications = performStructuralModificationsFixed();
        
        // 分析结果
        const balance = analyzePortBalance();
        
        console.log(`修改结果: 添加${modifications.addedNodes.length}节点, ${modifications.addedStartNodes.length}起点, ${modifications.addedEndNodes.length}终点`);
        console.log(`端口平衡: ${balance.isBalanced ? '✅ 平衡' : '❌ 不平衡'}`);
        
        if (!balance.isBalanced) {
            console.log(`不平衡项: ${balance.imbalances.join(', ')}`);
        } else {
            balancedTests++;
        }
        
        // 显示最终端口统计
        console.log('端口统计:');
        for (const [typeKey, count] of balance.typeCounts.entries()) {
            const status = count.input === count.output ? '✅' : '❌';
            console.log(`  ${typeKey}: ${count.output}输出/${count.input}输入 ${status}`);
        }
        
        console.log('');
    }
    
    console.log('='.repeat(50));
    console.log('📊 测试总结');
    console.log('='.repeat(50));
    console.log(`总测试数: ${totalTests}`);
    console.log(`平衡测试数: ${balancedTests}`);
    console.log(`成功率: ${(balancedTests/totalTests * 100).toFixed(1)}%`);
    
    if (balancedTests === totalTests) {
        console.log('\n🎉 所有测试通过！结构修改平衡性修复成功！');
    } else {
        console.log('\n⚠️ 部分测试失败，仍需进一步改进');
    }
}

// 执行测试
runMultipleTests();

console.log('\n✅ 结构修改测试完成');