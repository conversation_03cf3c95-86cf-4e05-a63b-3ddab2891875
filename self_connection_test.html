<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Self-Connection DAG Validation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-case {
            margin: 10px 0;
            padding: 10px;
            background: #f9f9f9;
            border-left: 4px solid #007bff;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .node-info {
            font-family: monospace;
            font-size: 12px;
            background: #f8f9fa;
            padding: 5px;
            margin: 5px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Self-Connection DAG Validation Test</h1>
        <p>Testing the enhanced hierarchical compensation algorithm's ability to detect and fix self-connection issues.</p>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="test-section">
            <h2>Test Case 1: Single Bridge Node (Critical Issue)</h2>
            <p>A single node with both input and output ports of the same type - should be detected as unsolvable.</p>
            <button onclick="testSingleBridgeNode()">Run Test 1</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 2: Missing Source Node</h2>
            <p>Only sink nodes exist for a port type - should generate source node.</p>
            <button onclick="testMissingSourceNode()">Run Test 2</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 3: Missing Sink Node</h2>
            <p>Only source nodes exist for a port type - should generate sink node.</p>
            <button onclick="testMissingSinkNode()">Run Test 3</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 4: Valid DAG Structure</h2>
            <p>Proper source → bridge → sink flow - should pass validation.</p>
            <button onclick="testValidDAG()">Run Test 4</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 5: Multiple Bridge Nodes</h2>
            <p>Multiple bridge nodes with proper sources and sinks - should be valid.</p>
            <button onclick="testMultipleBridgeNodes()">Run Test 5</button>
            <div id="test5-result"></div>
        </div>

        <div class="test-section">
            <h2>Overall Test Results</h2>
            <div id="overall-results"></div>
        </div>
    </div>

    <script src="compensation_algorithm.js"></script>
    <script>
        // Initialize the compensation algorithm
        const portTypes = [
            { shape: 'square', color: 'red' },
            { shape: 'circle', color: 'blue' },
            { shape: 'triangle', color: 'green' }
        ];

        const compensationAlgorithm = new HierarchicalCompensationAlgorithm(portTypes, 5);

        function logResult(testId, message, type = 'info') {
            const resultDiv = document.getElementById(testId + '-result');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : 'warning';
            resultDiv.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        function clearResults() {
            for (let i = 1; i <= 5; i++) {
                document.getElementById(`test${i}-result`).innerHTML = '';
            }
            document.getElementById('overall-results').innerHTML = '';
        }

        // Test Case 1: Single Bridge Node (Critical Issue)
        function testSingleBridgeNode() {
            logResult('test1', '🧪 Testing single bridge node scenario...', 'info');
            
            const problematicNode = {
                id: 'bridge_only',
                type: 'intermediate',
                level: 2,
                depth: 200,
                inputPorts: [{
                    id: 'bridge_only_in_0',
                    type: 'square',
                    color: 'red',
                    side: 'input',
                    nodeId: 'bridge_only',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                outputPorts: [{
                    id: 'bridge_only_out_0',
                    type: 'square',
                    color: 'red',
                    side: 'output',
                    nodeId: 'bridge_only',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                x: 100, y: 100,
                area: 'placed'
            };

            const nodes = [problematicNode];

            // Test validation
            const isValid = compensationAlgorithm.validateNoSelfConnectionRequired('square-red', nodes);
            
            if (!isValid) {
                logResult('test1', '✅ Correctly detected self-connection issue', 'success');
            } else {
                logResult('test1', '❌ Failed to detect self-connection issue', 'error');
                return false;
            }

            // Test compensation
            const fixNodes = compensationAlgorithm.createSelfConnectionFix('square-red', nodes);
            
            if (fixNodes.length === 2) {
                logResult('test1', `✅ Generated ${fixNodes.length} fix nodes (source + sink)`, 'success');
                
                // Verify the fix nodes
                const sourceNode = fixNodes.find(n => n.type === 'start');
                const sinkNode = fixNodes.find(n => n.type === 'end');
                
                if (sourceNode && sinkNode) {
                    logResult('test1', '✅ Fix includes proper source and sink nodes', 'success');
                    
                    // Test that the fixed scenario is now valid
                    const allNodes = [...nodes, ...fixNodes];
                    const fixedValid = compensationAlgorithm.validateSinglePortTypeDAG('square-red', allNodes);
                    
                    if (fixedValid) {
                        logResult('test1', '✅ Fixed scenario passes DAG validation', 'success');
                        return true;
                    } else {
                        logResult('test1', '❌ Fixed scenario still fails validation', 'error');
                        return false;
                    }
                } else {
                    logResult('test1', '❌ Fix nodes missing source or sink', 'error');
                    return false;
                }
            } else {
                logResult('test1', `❌ Expected 2 fix nodes, got ${fixNodes.length}`, 'error');
                return false;
            }
        }

        // Test Case 2: Missing Source Node
        function testMissingSourceNode() {
            logResult('test2', '🧪 Testing missing source node scenario...', 'info');
            
            const sinkNode = {
                id: 'sink_only',
                type: 'end',
                level: 5,
                depth: 500,
                inputPorts: [{
                    id: 'sink_only_in_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'input',
                    nodeId: 'sink_only',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                outputPorts: [],
                x: 100, y: 100,
                area: 'placed'
            };

            const nodes = [sinkNode];

            // Test validation
            const isValid = compensationAlgorithm.validateNoSelfConnectionRequired('circle-blue', nodes);
            
            if (!isValid) {
                logResult('test2', '✅ Correctly detected missing source issue', 'success');
            } else {
                logResult('test2', '❌ Failed to detect missing source issue', 'error');
                return false;
            }

            // Test compensation
            const fixNodes = compensationAlgorithm.createSelfConnectionFix('circle-blue', nodes);
            
            if (fixNodes.length === 1 && fixNodes[0].type === 'start') {
                logResult('test2', '✅ Generated source node fix', 'success');
                
                // Test that the fixed scenario is now valid
                const allNodes = [...nodes, ...fixNodes];
                const fixedValid = compensationAlgorithm.validateSinglePortTypeDAG('circle-blue', allNodes);
                
                if (fixedValid) {
                    logResult('test2', '✅ Fixed scenario passes DAG validation', 'success');
                    return true;
                } else {
                    logResult('test2', '❌ Fixed scenario still fails validation', 'error');
                    return false;
                }
            } else {
                logResult('test2', `❌ Expected 1 source node, got ${fixNodes.length} nodes`, 'error');
                return false;
            }
        }

        // Test Case 3: Missing Sink Node
        function testMissingSinkNode() {
            logResult('test3', '🧪 Testing missing sink node scenario...', 'info');
            
            const sourceNode = {
                id: 'source_only',
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [{
                    id: 'source_only_out_0',
                    type: 'triangle',
                    color: 'green',
                    side: 'output',
                    nodeId: 'source_only',
                    portTypeKey: 'triangle-green',
                    x: 0, y: 0
                }],
                x: 100, y: 100,
                area: 'placed'
            };

            const nodes = [sourceNode];

            // Test validation
            const isValid = compensationAlgorithm.validateNoSelfConnectionRequired('triangle-green', nodes);
            
            if (!isValid) {
                logResult('test3', '✅ Correctly detected missing sink issue', 'success');
            } else {
                logResult('test3', '❌ Failed to detect missing sink issue', 'error');
                return false;
            }

            // Test compensation
            const fixNodes = compensationAlgorithm.createSelfConnectionFix('triangle-green', nodes);
            
            if (fixNodes.length === 1 && fixNodes[0].type === 'end') {
                logResult('test3', '✅ Generated sink node fix', 'success');
                
                // Test that the fixed scenario is now valid
                const allNodes = [...nodes, ...fixNodes];
                const fixedValid = compensationAlgorithm.validateSinglePortTypeDAG('triangle-green', allNodes);
                
                if (fixedValid) {
                    logResult('test3', '✅ Fixed scenario passes DAG validation', 'success');
                    return true;
                } else {
                    logResult('test3', '❌ Fixed scenario still fails validation', 'error');
                    return false;
                }
            } else {
                logResult('test3', `❌ Expected 1 sink node, got ${fixNodes.length} nodes`, 'error');
                return false;
            }
        }

        // Test Case 4: Valid DAG Structure
        function testValidDAG() {
            logResult('test4', '🧪 Testing valid DAG structure...', 'info');
            
            const sourceNode = {
                id: 'valid_source',
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [{
                    id: 'valid_source_out_0',
                    type: 'square',
                    color: 'red',
                    side: 'output',
                    nodeId: 'valid_source',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                x: 50, y: 50,
                area: 'placed'
            };

            const bridgeNode = {
                id: 'valid_bridge',
                type: 'intermediate',
                level: 2,
                depth: 200,
                inputPorts: [{
                    id: 'valid_bridge_in_0',
                    type: 'square',
                    color: 'red',
                    side: 'input',
                    nodeId: 'valid_bridge',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                outputPorts: [{
                    id: 'valid_bridge_out_0',
                    type: 'square',
                    color: 'red',
                    side: 'output',
                    nodeId: 'valid_bridge',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                x: 100, y: 100,
                area: 'placed'
            };

            const sinkNode = {
                id: 'valid_sink',
                type: 'end',
                level: 5,
                depth: 500,
                inputPorts: [{
                    id: 'valid_sink_in_0',
                    type: 'square',
                    color: 'red',
                    side: 'input',
                    nodeId: 'valid_sink',
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                }],
                outputPorts: [],
                x: 150, y: 150,
                area: 'placed'
            };

            const nodes = [sourceNode, bridgeNode, sinkNode];

            // Test validation
            const isValid = compensationAlgorithm.validateSinglePortTypeDAG('square-red', nodes);
            
            if (isValid) {
                logResult('test4', '✅ Valid DAG structure correctly validated', 'success');
                
                // Test that no fix is needed
                const fixNodes = compensationAlgorithm.createSelfConnectionFix('square-red', nodes);
                
                if (fixNodes.length === 0) {
                    logResult('test4', '✅ No unnecessary fix nodes generated', 'success');
                    return true;
                } else {
                    logResult('test4', `⚠️ Unexpected ${fixNodes.length} fix nodes generated for valid DAG`, 'warning');
                    return false;
                }
            } else {
                logResult('test4', '❌ Valid DAG structure incorrectly rejected', 'error');
                return false;
            }
        }

        // Test Case 5: Multiple Bridge Nodes
        function testMultipleBridgeNodes() {
            logResult('test5', '🧪 Testing multiple bridge nodes scenario...', 'info');
            
            const sourceNode = {
                id: 'multi_source',
                type: 'start',
                level: 0,
                depth: 0,
                inputPorts: [],
                outputPorts: [{
                    id: 'multi_source_out_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'output',
                    nodeId: 'multi_source',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                x: 50, y: 50,
                area: 'placed'
            };

            const bridge1 = {
                id: 'multi_bridge1',
                type: 'intermediate',
                level: 2,
                depth: 200,
                inputPorts: [{
                    id: 'multi_bridge1_in_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'input',
                    nodeId: 'multi_bridge1',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                outputPorts: [{
                    id: 'multi_bridge1_out_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'output',
                    nodeId: 'multi_bridge1',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                x: 100, y: 100,
                area: 'placed'
            };

            const bridge2 = {
                id: 'multi_bridge2',
                type: 'intermediate',
                level: 3,
                depth: 300,
                inputPorts: [{
                    id: 'multi_bridge2_in_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'input',
                    nodeId: 'multi_bridge2',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                outputPorts: [{
                    id: 'multi_bridge2_out_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'output',
                    nodeId: 'multi_bridge2',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                x: 125, y: 125,
                area: 'placed'
            };

            const sinkNode = {
                id: 'multi_sink',
                type: 'end',
                level: 5,
                depth: 500,
                inputPorts: [{
                    id: 'multi_sink_in_0',
                    type: 'circle',
                    color: 'blue',
                    side: 'input',
                    nodeId: 'multi_sink',
                    portTypeKey: 'circle-blue',
                    x: 0, y: 0
                }],
                outputPorts: [],
                x: 150, y: 150,
                area: 'placed'
            };

            const nodes = [sourceNode, bridge1, bridge2, sinkNode];

            // Test validation
            const isValid = compensationAlgorithm.validateSinglePortTypeDAG('circle-blue', nodes);
            
            if (isValid) {
                logResult('test5', '✅ Multiple bridge nodes correctly validated', 'success');
                
                // Test that no fix is needed
                const fixNodes = compensationAlgorithm.createSelfConnectionFix('circle-blue', nodes);
                
                if (fixNodes.length === 0) {
                    logResult('test5', '✅ No unnecessary fix nodes generated', 'success');
                    return true;
                } else {
                    logResult('test5', `⚠️ Unexpected ${fixNodes.length} fix nodes generated`, 'warning');
                    return false;
                }
            } else {
                logResult('test5', '❌ Multiple bridge nodes incorrectly rejected', 'error');
                return false;
            }
        }

        function runAllTests() {
            clearResults();
            
            const results = [];
            results.push(testSingleBridgeNode());
            results.push(testMissingSourceNode());
            results.push(testMissingSinkNode());
            results.push(testValidDAG());
            results.push(testMultipleBridgeNodes());
            
            const passedTests = results.filter(r => r).length;
            const totalTests = results.length;
            
            const overallDiv = document.getElementById('overall-results');
            if (passedTests === totalTests) {
                overallDiv.innerHTML = `<div class="result success">🎉 All ${totalTests} tests passed! Self-connection validation is working correctly.</div>`;
            } else {
                overallDiv.innerHTML = `<div class="result error">❌ ${passedTests}/${totalTests} tests passed. Some issues need to be addressed.</div>`;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
