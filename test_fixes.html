<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Algorithm Fixes</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Algorithm Fixes</h1>
        
        <button onclick="testPortBalancing()">Test Port Balancing</button>
        <button onclick="testValidationFix()">Test Validation Fix</button>
        <button onclick="testFullAlgorithm()">Test Full Algorithm</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div class="test-output" id="testOutput">
            <div class="info">Ready to test algorithm fixes...</div>
        </div>
    </div>

    <script src="game.js?v=layered-dag-algorithm-2025"></script>
    
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const div = document.createElement('div');
            div.className = type;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
        }
        
        function testPortBalancing() {
            clearOutput();
            log('Testing port balancing fixes...', 'info');
            
            try {
                if (typeof generateDeterministicSolvableScenario !== 'function') {
                    log('ERROR: Algorithm function not available', 'error');
                    return;
                }
                
                // Test multiple levels to check port balancing
                for (let level = 1; level <= 5; level++) {
                    log('Testing level ' + level + ' port balancing...', 'info');
                    
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4))
                    };
                    
                    try {
                        const scenario = generateDeterministicSolvableScenario(difficulty);
                        
                        if (scenario && scenario.startNode && scenario.endNode) {
                            // Check port balance
                            const typeBalance = new Map();
                            
                            // Count all ports
                            [scenario.startNode, scenario.endNode, ...(scenario.nodes || [])].forEach(node => {
                                if (node.inputPorts) {
                                    node.inputPorts.forEach(port => {
                                        const key = port.type + ':' + port.color;
                                        if (!typeBalance.has(key)) {
                                            typeBalance.set(key, { input: 0, output: 0 });
                                        }
                                        typeBalance.get(key).input++;
                                    });
                                }
                                
                                if (node.outputPorts) {
                                    node.outputPorts.forEach(port => {
                                        const key = port.type + ':' + port.color;
                                        if (!typeBalance.has(key)) {
                                            typeBalance.set(key, { input: 0, output: 0 });
                                        }
                                        typeBalance.get(key).output++;
                                    });
                                }
                            });
                            
                            // Check balance
                            let balanced = true;
                            for (const [typeKey, balance] of typeBalance) {
                                if (balance.input !== balance.output) {
                                    log('Level ' + level + ': UNBALANCED - ' + typeKey + ' (in:' + balance.input + ', out:' + balance.output + ')', 'error');
                                    balanced = false;
                                }
                            }
                            
                            if (balanced) {
                                log('Level ' + level + ': BALANCED - All port types match', 'success');
                            }
                            
                        } else {
                            log('Level ' + level + ': FAILED - Invalid scenario', 'error');
                        }
                    } catch (levelError) {
                        log('Level ' + level + ': ERROR - ' + levelError.message, 'error');
                    }
                }
                
                log('Port balancing test completed', 'info');
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        function testValidationFix() {
            clearOutput();
            log('Testing validation function fixes...', 'info');
            
            try {
                // Test the validation function with various inputs
                const testChains = [
                    null,
                    undefined,
                    {},
                    { source: null },
                    { source: { type: 'square', color: '#ff5252' } },
                    { source: { type: 'square', color: '#ff5252' }, target: { type: 'circle', color: '#2196F3' } }
                ];
                
                testChains.forEach((chain, index) => {
                    try {
                        if (typeof validateTransformationChainExecution === 'function') {
                            const result = validateTransformationChainExecution(chain);
                            log('Chain ' + (index + 1) + ': ' + (result ? 'VALID' : 'INVALID') + ' - ' + JSON.stringify(chain), 'info');
                        } else {
                            log('validateTransformationChainExecution function not found', 'warning');
                        }
                    } catch (error) {
                        log('Chain ' + (index + 1) + ': ERROR - ' + error.message, 'error');
                    }
                });
                
                log('Validation function test completed', 'success');
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        function testFullAlgorithm() {
            clearOutput();
            log('Testing full algorithm with all fixes...', 'info');
            
            try {
                if (typeof generateDeterministicSolvableScenario !== 'function') {
                    log('ERROR: Algorithm function not available', 'error');
                    return;
                }
                
                // Test comprehensive scenario generation
                const testDifficulty = {
                    level: 3,
                    availableTypes: ['square', 'circle', 'triangle'],
                    availableColors: ['#ff5252', '#2196F3', '#4CAF50']
                };
                
                log('Generating comprehensive test scenario...', 'info');
                const scenario = generateDeterministicSolvableScenario(testDifficulty);
                
                if (!scenario) {
                    log('ERROR: Scenario generation failed', 'error');
                    return;
                }
                
                log('SUCCESS: Scenario generated', 'success');
                
                // Detailed analysis
                log('=== Scenario Analysis ===', 'info');
                log('Start node: ' + (scenario.startNode ? 'YES' : 'NO'), 'info');
                log('End node: ' + (scenario.endNode ? 'YES' : 'NO'), 'info');
                log('Intermediate nodes: ' + (scenario.nodes ? scenario.nodes.length : 0), 'info');
                log('All nodes: ' + (scenario.allNodes ? scenario.allNodes.length : 0), 'info');
                log('Connections: ' + (scenario.guaranteedConnections ? scenario.guaranteedConnections.length : 0), 'info');
                
                if (scenario.startNode) {
                    log('Start outputs: ' + scenario.startNode.outputPorts.length, 'info');
                    log('Start inputs: ' + scenario.startNode.inputPorts.length, 'info');
                }
                
                if (scenario.endNode) {
                    log('End inputs: ' + scenario.endNode.inputPorts.length, 'info');
                    log('End outputs: ' + scenario.endNode.outputPorts.length, 'info');
                }
                
                // Depth analysis
                if (scenario.allNodes) {
                    const depths = scenario.allNodes.map(n => n.depth).sort((a, b) => a - b);
                    log('Node depths: [' + depths.join(', ') + ']', 'info');
                }
                
                // Connection analysis
                if (scenario.guaranteedConnections) {
                    let validConnections = 0;
                    scenario.guaranteedConnections.forEach((conn, i) => {
                        if (conn.sourceDepth < conn.targetDepth) {
                            validConnections++;
                        } else {
                            log('Invalid connection ' + (i + 1) + ': ' + conn.sourceDepth + ' -> ' + conn.targetDepth, 'error');
                        }
                    });
                    log('Valid connections: ' + validConnections + '/' + scenario.guaranteedConnections.length, validConnections === scenario.guaranteedConnections.length ? 'success' : 'error');
                }
                
                log('=== Test Complete ===', 'success');
                
            } catch (error) {
                log('ERROR: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, ready to test fixes', 'info');
            }, 500);
        });
        
        // Catch global errors
        window.addEventListener('error', (event) => {
            log('GLOBAL ERROR: ' + event.message + ' at line ' + event.lineno, 'error');
        });
    </script>
</body>
</html>
