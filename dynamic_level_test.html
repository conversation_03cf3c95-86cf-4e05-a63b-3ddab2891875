<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dynamic Level Reassignment Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            margin: 20px 0;
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        button {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.3);
        }
        button:active {
            transform: translateY(0);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        .status-value {
            font-size: 1.2em;
            font-weight: bold;
        }
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        .log-info { background: rgba(76, 175, 80, 0.2); }
        .log-warn { background: rgba(255, 193, 7, 0.2); }
        .log-error { background: rgba(244, 67, 54, 0.2); }
        .node-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .node-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Dynamic Level Reassignment Test</h1>
            <p>测试动态层级重分配和端口成对补偿系统</p>
        </div>

        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <div class="button-group">
                <button onclick="testDynamicLevelSystem()">🔄 Test Dynamic Level System</button>
                <button onclick="testPortPairCompensation()">🔧 Test Port Pair Compensation</button>
                <button onclick="testLevelConsistency()">✅ Test Level Consistency</button>
                <button onclick="testDepthConstraints()">📏 Test Depth Constraints</button>
                <button onclick="createTestNodes()">➕ Create Test Nodes</button>
                <button onclick="clearAll()">🗑️ Clear All</button>
            </div>
        </div>

        <div class="test-section">
            <h3>📊 System Status</h3>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">Placed Nodes</div>
                    <div class="status-value" id="placedCount">0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Temporary Nodes</div>
                    <div class="status-value" id="tempCount">0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Connections</div>
                    <div class="status-value" id="connectionCount">0</div>
                </div>
                <div class="status-item">
                    <div class="status-label">Max Level</div>
                    <div class="status-value" id="maxLevel">0</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🏗️ Node Information</h3>
            <div id="nodeInfo" class="node-info">
                <div class="node-card">No nodes created yet</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 Test Log</h3>
            <div id="testLog" class="log-container">
                <div class="log-entry log-info">[INIT] Dynamic Level Reassignment Test initialized</div>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
    <script>
        // 测试日志函数
        function logTest(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[DYNAMIC-LEVEL-TEST] ${message}`);
        }

        // 更新状态显示
        function updateStatus() {
            document.getElementById('placedCount').textContent = gameState.placedNodes.length;
            document.getElementById('tempCount').textContent = gameState.temporaryNodes.length;
            document.getElementById('connectionCount').textContent = gameState.connections.length;
            
            const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
            const maxLevel = Math.max(0, ...allNodes.map(n => n.level || 0));
            document.getElementById('maxLevel').textContent = maxLevel;
            
            updateNodeInfo();
        }

        // 更新节点信息显示
        function updateNodeInfo() {
            const nodeInfoContainer = document.getElementById('nodeInfo');
            const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
            
            if (allNodes.length === 0) {
                nodeInfoContainer.innerHTML = '<div class="node-card">No nodes created yet</div>';
                return;
            }
            
            nodeInfoContainer.innerHTML = allNodes.map(node => `
                <div class="node-card">
                    <strong>${node.label || node.id}</strong><br>
                    Level: ${node.level || 0}<br>
                    Type: ${node.type}<br>
                    Inputs: ${(node.inputPorts || []).length}<br>
                    Outputs: ${(node.outputPorts || []).length}
                </div>
            `).join('');
        }

        // 测试动态层级系统
        function testDynamicLevelSystem() {
            logTest('Starting Dynamic Level System Test', 'info');
            
            try {
                // 确保有测试节点
                if (gameState.placedNodes.length === 0) {
                    createTestNodes();
                }
                
                const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
                logTest(`Testing with ${allNodes.length} nodes`, 'info');
                
                // 调用动态层级重分配
                const levelAssignments = dynamicLevelSystem.reassignLevelsBasedOnConnections(
                    allNodes, 
                    gameState.connections
                );
                
                logTest(`Level reassignment completed: ${levelAssignments.size} assignments`, 'info');
                
                // 显示结果
                for (const [nodeId, level] of levelAssignments.entries()) {
                    logTest(`Node ${nodeId}: Level ${level}`, 'info');
                }
                
                updateStatus();
                logTest('Dynamic Level System Test completed successfully', 'info');
                
            } catch (error) {
                logTest(`Dynamic Level System Test failed: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 测试端口成对补偿
        function testPortPairCompensation() {
            logTest('Starting Port Pair Compensation Test', 'info');
            
            try {
                const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
                
                if (allNodes.length === 0) {
                    createTestNodes();
                }
                
                // 验证端口深度约束
                dynamicLevelSystem.validateAndFixPortDepthConstraints(allNodes);
                
                updateStatus();
                logTest('Port Pair Compensation Test completed', 'info');
                
            } catch (error) {
                logTest(`Port Pair Compensation Test failed: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 测试层级一致性
        function testLevelConsistency() {
            logTest('Starting Level Consistency Test', 'info');
            
            try {
                const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
                const validation = validateLevelConsistency(allNodes, gameState.connections);
                
                if (validation.isValid) {
                    logTest('Level consistency validation passed', 'info');
                } else {
                    logTest(`Level consistency validation failed: ${validation.errors.length} errors`, 'warn');
                    validation.errors.forEach(error => logTest(error, 'warn'));
                }
                
            } catch (error) {
                logTest(`Level Consistency Test failed: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 测试深度约束
        function testDepthConstraints() {
            logTest('Starting Depth Constraints Test', 'info');
            
            try {
                const allNodes = [...gameState.placedNodes, ...gameState.temporaryNodes];
                const validation = validatePortDepthConstraints(allNodes);
                
                if (validation.isValid) {
                    logTest('Port depth constraints validation passed', 'info');
                } else {
                    logTest(`Port depth constraints validation failed: ${validation.errors.length} errors`, 'warn');
                    validation.errors.forEach(error => logTest(error, 'warn'));
                }
                
            } catch (error) {
                logTest(`Depth Constraints Test failed: ${error.message}`, 'error');
                console.error(error);
            }
        }

        // 创建测试节点
        function createTestNodes() {
            logTest('Creating test nodes...', 'info');
            
            // 清空现有状态
            gameState.placedNodes = [];
            gameState.temporaryNodes = [];
            gameState.connections = [];
            
            // 创建起点
            const startNode = new Node('start');
            startNode.id = 'test_start_1';
            startNode.label = 'Test-Start';
            startNode.level = 0;
            startNode.depth = 0;
            startNode.addOutputPort('square', '#ff5252');
            startNode.addOutputPort('circle', '#2196F3');
            startNode.moveTo(100, 200);
            gameState.placedNodes.push(startNode);
            
            // 创建中间节点
            const middleNode = new Node('normal');
            middleNode.id = 'test_middle_1';
            middleNode.label = 'Test-Middle';
            middleNode.level = 1;
            middleNode.depth = 100;
            middleNode.addInputPort('square', '#ff5252');
            middleNode.addOutputPort('circle', '#2196F3');
            middleNode.moveTo(300, 200);
            gameState.placedNodes.push(middleNode);
            
            // 创建终点
            const endNode = new Node('end');
            endNode.id = 'test_end_1';
            endNode.label = 'Test-End';
            endNode.level = 2;
            endNode.depth = 200;
            endNode.addInputPort('circle', '#2196F3');
            endNode.moveTo(500, 200);
            gameState.placedNodes.push(endNode);
            
            logTest(`Created ${gameState.placedNodes.length} test nodes`, 'info');
            updateStatus();
        }

        // 清空所有
        function clearAll() {
            gameState.placedNodes = [];
            gameState.temporaryNodes = [];
            gameState.connections = [];
            updateStatus();
            logTest('All nodes and connections cleared', 'info');
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            logTest('Dynamic Level Reassignment Test page loaded', 'info');
            updateStatus();
        });
    </script>
</body>
</html>
