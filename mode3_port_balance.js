// 模式3端口平衡算法
// 基于SYSTEM.md端口类型守恒约束的实现

// ========== 端口平衡器 ==========
class PortBalancer {
    constructor() {
        this.balanceHistory = [];
        this.portTypeRegistry = new Map();
    }

    // 平衡端口数量（确保每种类型的输入/输出端口数量相等）
    balancePortCounts(nodes) {
        console.log('[PORT-BALANCER] 开始端口平衡...');
        
        // 步骤1：分析当前端口分布
        const currentDistribution = this.analyzePortDistribution(nodes);
        
        // 步骤2：检测不平衡
        const imbalances = this.detectImbalances(currentDistribution);
        
        // 步骤3：生成平衡策略
        const balanceStrategy = this.generateBalanceStrategy(imbalances, nodes);
        
        // 步骤4：应用平衡调整
        this.applyBalanceAdjustments(nodes, balanceStrategy);
        
        // 步骤5：验证平衡结果
        const finalDistribution = this.analyzePortDistribution(nodes);
        const verification = this.verifyBalance(finalDistribution);
        
        console.log(`[PORT-BALANCER] 端口平衡完成，平衡状态: ${verification.isBalanced ? '已平衡' : '仍有不平衡'}`);
        
        return {
            isBalanced: verification.isBalanced,
            initialDistribution: currentDistribution,
            finalDistribution: finalDistribution,
            adjustments: balanceStrategy.adjustments,
            verification: verification
        };
    }

    analyzePortDistribution(nodes) {
        const distribution = new Map();
        
        nodes.forEach(node => {
            // 分析输入端口
            (node.inputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                if (!distribution.has(key)) {
                    distribution.set(key, { input: 0, output: 0, nodes: new Set() });
                }
                distribution.get(key).input++;
                distribution.get(key).nodes.add(node.id);
            });
            
            // 分析输出端口
            (node.outputPorts || []).forEach(port => {
                const key = `${port.type}-${port.color}`;
                if (!distribution.has(key)) {
                    distribution.set(key, { input: 0, output: 0, nodes: new Set() });
                }
                distribution.get(key).output++;
                distribution.get(key).nodes.add(node.id);
            });
        });
        
        return distribution;
    }

    detectImbalances(distribution) {
        const imbalances = [];
        
        distribution.forEach((counts, portType) => {
            if (counts.input !== counts.output) {
                imbalances.push({
                    portType: portType,
                    inputCount: counts.input,
                    outputCount: counts.output,
                    deficit: counts.output - counts.input, // 正数表示需要更多输入，负数表示需要更多输出
                    severity: Math.abs(counts.output - counts.input),
                    affectedNodes: Array.from(counts.nodes)
                });
            }
        });
        
        // 按严重程度排序
        imbalances.sort((a, b) => b.severity - a.severity);
        
        console.log(`[PORT-BALANCER] 检测到 ${imbalances.length} 个端口类型不平衡`);
        imbalances.forEach(imbalance => {
            console.log(`[PORT-BALANCER] - ${imbalance.portType}: ${imbalance.outputCount} 输出, ${imbalance.inputCount} 输入 (差值: ${imbalance.deficit})`);
        });
        
        return imbalances;
    }

    generateBalanceStrategy(imbalances, nodes) {
        const strategy = {
            adjustments: [],
            newPorts: [],
            portRemovals: []
        };
        
        imbalances.forEach(imbalance => {
            if (imbalance.deficit > 0) {
                // 需要更多输入端口
                const inputAdjustments = this.generateInputPortAdjustments(
                    imbalance.portType, 
                    imbalance.deficit, 
                    nodes
                );
                strategy.adjustments.push(...inputAdjustments);
            } else if (imbalance.deficit < 0) {
                // 需要更多输出端口
                const outputAdjustments = this.generateOutputPortAdjustments(
                    imbalance.portType, 
                    -imbalance.deficit, 
                    nodes
                );
                strategy.adjustments.push(...outputAdjustments);
            }
        });
        
        return strategy;
    }

    generateInputPortAdjustments(portType, count, nodes) {
        const [shape, color] = portType.split('-');
        const adjustments = [];
        
        // 找到可以添加输入端口的节点（中间节点和终点节点）
        const eligibleNodes = nodes.filter(node => 
            node.type === 'intermediate' || node.type === 'end'
        );
        
        if (eligibleNodes.length === 0) {
            console.warn(`[PORT-BALANCER] 没有可用节点添加输入端口 ${portType}`);
            return adjustments;
        }
        
        // 均匀分布端口到可用节点
        for (let i = 0; i < count; i++) {
            const targetNode = eligibleNodes[i % eligibleNodes.length];
            adjustments.push({
                type: 'ADD_INPUT_PORT',
                nodeId: targetNode.id,
                portType: portType,
                port: {
                    id: `${targetNode.id}_in_balance_${Date.now()}_${i}`,
                    type: shape,
                    color: color,
                    side: 'input',
                    nodeId: targetNode.id
                }
            });
        }
        
        return adjustments;
    }

    generateOutputPortAdjustments(portType, count, nodes) {
        const [shape, color] = portType.split('-');
        const adjustments = [];
        
        // 找到可以添加输出端口的节点（起点节点和中间节点）
        const eligibleNodes = nodes.filter(node => 
            node.type === 'start' || node.type === 'intermediate'
        );
        
        if (eligibleNodes.length === 0) {
            console.warn(`[PORT-BALANCER] 没有可用节点添加输出端口 ${portType}`);
            return adjustments;
        }
        
        // 均匀分布端口到可用节点
        for (let i = 0; i < count; i++) {
            const targetNode = eligibleNodes[i % eligibleNodes.length];
            adjustments.push({
                type: 'ADD_OUTPUT_PORT',
                nodeId: targetNode.id,
                portType: portType,
                port: {
                    id: `${targetNode.id}_out_balance_${Date.now()}_${i}`,
                    type: shape,
                    color: color,
                    side: 'output',
                    nodeId: targetNode.id
                }
            });
        }
        
        return adjustments;
    }

    applyBalanceAdjustments(nodes, strategy) {
        console.log(`[PORT-BALANCER] 应用 ${strategy.adjustments.length} 个平衡调整...`);
        
        strategy.adjustments.forEach(adjustment => {
            const targetNode = nodes.find(node => node.id === adjustment.nodeId);
            if (!targetNode) {
                console.warn(`[PORT-BALANCER] 找不到目标节点 ${adjustment.nodeId}`);
                return;
            }
            
            switch (adjustment.type) {
                case 'ADD_INPUT_PORT':
                    if (!targetNode.inputPorts) targetNode.inputPorts = [];
                    targetNode.inputPorts.push(adjustment.port);
                    console.log(`[PORT-BALANCER] 为节点 ${targetNode.id} 添加输入端口 ${adjustment.portType}`);
                    break;
                    
                case 'ADD_OUTPUT_PORT':
                    if (!targetNode.outputPorts) targetNode.outputPorts = [];
                    targetNode.outputPorts.push(adjustment.port);
                    console.log(`[PORT-BALANCER] 为节点 ${targetNode.id} 添加输出端口 ${adjustment.portType}`);
                    break;
                    
                default:
                    console.warn(`[PORT-BALANCER] 未知的调整类型: ${adjustment.type}`);
            }
        });
    }

    verifyBalance(distribution) {
        let isBalanced = true;
        const remainingImbalances = [];
        
        distribution.forEach((counts, portType) => {
            if (counts.input !== counts.output) {
                isBalanced = false;
                remainingImbalances.push({
                    portType: portType,
                    inputCount: counts.input,
                    outputCount: counts.output,
                    deficit: counts.output - counts.input
                });
            }
        });
        
        return {
            isBalanced: isBalanced,
            totalPortTypes: distribution.size,
            balancedTypes: distribution.size - remainingImbalances.length,
            remainingImbalances: remainingImbalances
        };
    }

    // 智能端口类型选择（避免过度复杂化）
    selectOptimalPortTypes(nodeCount, complexity) {
        const baseTypes = [
            { shape: 'square', color: '#ff5252' },
            { shape: 'circle', color: '#2196F3' },
            { shape: 'triangle', color: '#4CAF50' },
            { shape: 'diamond', color: '#FFC107' }
        ];
        
        // 根据复杂度和节点数量选择合适的端口类型数量
        const optimalTypeCount = Math.min(
            Math.max(2, Math.ceil(nodeCount / 3)), // 至少2种，最多节点数/3
            Math.min(complexity + 1, baseTypes.length) // 受复杂度和可用类型限制
        );
        
        return baseTypes.slice(0, optimalTypeCount);
    }

    // 预测端口需求（用于提前规划）
    predictPortRequirements(nodeStructure, pathComplexity) {
        const prediction = new Map();
        
        // 基于节点结构预测端口需求
        const startNodeCount = nodeStructure.startNodes || 1;
        const endNodeCount = nodeStructure.endNodes || 1;
        const intermediateNodeCount = nodeStructure.intermediateNodes || 0;
        
        // 估算每种节点类型的端口需求
        const estimatedConnections = Math.max(
            startNodeCount + intermediateNodeCount, // 最少连接数
            Math.ceil((startNodeCount + intermediateNodeCount + endNodeCount) * pathComplexity / 2)
        );
        
        // 选择最优端口类型
        const optimalTypes = this.selectOptimalPortTypes(
            startNodeCount + intermediateNodeCount + endNodeCount,
            pathComplexity
        );
        
        // 为每种类型预测需求
        optimalTypes.forEach((type, index) => {
            const key = `${type.shape}-${type.color}`;
            const baseRequirement = Math.ceil(estimatedConnections / optimalTypes.length);
            
            prediction.set(key, {
                estimatedInputs: baseRequirement,
                estimatedOutputs: baseRequirement,
                confidence: 0.8 - (index * 0.1) // 前面的类型置信度更高
            });
        });
        
        return {
            totalEstimatedConnections: estimatedConnections,
            portTypePredictions: prediction,
            recommendedTypes: optimalTypes
        };
    }

    // 动态调整端口分布（运行时优化）
    dynamicPortAdjustment(nodes, connectionAttempts) {
        console.log('[PORT-BALANCER] 执行动态端口调整...');
        
        // 分析连接尝试模式
        const connectionPatterns = this.analyzeConnectionPatterns(connectionAttempts);
        
        // 识别瓶颈端口类型
        const bottlenecks = this.identifyPortBottlenecks(connectionPatterns, nodes);
        
        // 应用动态调整
        const adjustments = this.generateDynamicAdjustments(bottlenecks, nodes);
        this.applyBalanceAdjustments(nodes, { adjustments });
        
        return {
            patternsAnalyzed: connectionPatterns.length,
            bottlenecksFound: bottlenecks.length,
            adjustmentsApplied: adjustments.length
        };
    }

    analyzeConnectionPatterns(connectionAttempts) {
        const patterns = new Map();
        
        connectionAttempts.forEach(attempt => {
            const key = `${attempt.fromPortType}-${attempt.toPortType}`;
            if (!patterns.has(key)) {
                patterns.set(key, { attempts: 0, successes: 0, failures: 0 });
            }
            
            const pattern = patterns.get(key);
            pattern.attempts++;
            
            if (attempt.successful) {
                pattern.successes++;
            } else {
                pattern.failures++;
            }
        });
        
        return Array.from(patterns.entries()).map(([key, stats]) => ({
            pattern: key,
            ...stats,
            successRate: stats.attempts > 0 ? stats.successes / stats.attempts : 0
        }));
    }

    identifyPortBottlenecks(patterns, nodes) {
        const bottlenecks = [];
        
        patterns.forEach(pattern => {
            if (pattern.successRate < 0.5 && pattern.attempts > 2) {
                bottlenecks.push({
                    portType: pattern.pattern,
                    successRate: pattern.successRate,
                    attempts: pattern.attempts,
                    severity: (1 - pattern.successRate) * pattern.attempts
                });
            }
        });
        
        return bottlenecks.sort((a, b) => b.severity - a.severity);
    }

    generateDynamicAdjustments(bottlenecks, nodes) {
        const adjustments = [];
        
        bottlenecks.forEach(bottleneck => {
            const [fromType, toType] = bottleneck.portType.split('-');
            
            // 为瓶颈类型添加更多端口
            const additionalPorts = Math.ceil(bottleneck.severity / 2);
            
            // 添加输出端口
            const outputAdjustments = this.generateOutputPortAdjustments(fromType, additionalPorts, nodes);
            adjustments.push(...outputAdjustments);
            
            // 添加输入端口
            const inputAdjustments = this.generateInputPortAdjustments(toType, additionalPorts, nodes);
            adjustments.push(...inputAdjustments);
        });
        
        return adjustments;
    }
}

// ========== 导出接口 ==========
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        PortBalancer
    };
}
