// ========== Mode 3 端口类型DAG实现 ==========

// 游戏状态
const mode3PortDAGState = {
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    
    // Canvas引用
    temporaryCanvas: null,
    temporaryCtx: null,
    gameCanvas: null,
    gameCtx: null,
    
    // 交互状态
    draggingNode: null,
    selectedPort: null,
    
    // Mode 3 端口DAG状态
    currentWave: 1,
    portDAGEngine: null,
    lastValidation: null,
    
    // 统计信息
    portTypeStats: new Map(),
    selfLoopRisks: new Set(),
    dagValidations: new Map()
};

// ========== 初始化 ==========

function initializeMode3PortDAG() {
    console.log('[MODE3-PORT-DAG] Initializing port-type DAG mode');
    
    // 获取Canvas元素
    mode3PortDAGState.temporaryCanvas = document.getElementById('temporaryCanvas');
    mode3PortDAGState.gameCanvas = document.getElementById('gameCanvas');
    
    if (!mode3PortDAGState.temporaryCanvas || !mode3PortDAGState.gameCanvas) {
        console.error('[MODE3-PORT-DAG] Canvas elements not found');
        return false;
    }
    
    mode3PortDAGState.temporaryCtx = mode3PortDAGState.temporaryCanvas.getContext('2d');
    mode3PortDAGState.gameCtx = mode3PortDAGState.gameCanvas.getContext('2d');
    
    // 初始化端口DAG引擎
    mode3PortDAGState.portDAGEngine = new PortTypeDAGEngine();
    
    // 设置事件监听器
    setupMode3PortDAGEventListeners();
    
    // 生成初始状态
    generateInitialPortDAGState();
    
    // 启动连续验证
    startPortDAGValidation();
    
    updateMode3PortDAGDisplay();
    
    console.log('[MODE3-PORT-DAG] Port-type DAG mode initialized');
    return true;
}

function setupMode3PortDAGEventListeners() {
    [mode3PortDAGState.temporaryCanvas, mode3PortDAGState.gameCanvas].forEach(canvas => {
        canvas.addEventListener('mousedown', handleMode3PortDAGMouseDown);
        canvas.addEventListener('mousemove', handleMode3PortDAGMouseMove);
        canvas.addEventListener('mouseup', handleMode3PortDAGMouseUp);
    });
    
    document.addEventListener('keydown', handleMode3PortDAGKeyDown);
}

// ========== 初始状态生成 ==========

function generateInitialPortDAGState() {
    console.log('[MODE3-PORT-DAG] Generating initial port-DAG state');
    
    // 创建基础节点 - 避免自环配置
    const initialNodes = [
        createPortDAGNode('start_red_square', 'start', 0, [], [
            { type: 'square', color: '#ff5252' }
        ]),
        createPortDAGNode('end_blue_circle', 'end', 2, [
            { type: 'circle', color: '#2196F3' }
        ], [])
    ];
    
    mode3PortDAGState.placedNodes = initialNodes.map(node => ({
        ...node,
        x: 200 + Math.random() * 300,
        y: 150 + Math.random() * 200
    }));
    
    // 使用端口DAG引擎生成第一波节点
    expandWithPortDAG();
}

function createPortDAGNode(id, type, depth, inputPortSpecs, outputPortSpecs) {
    const node = {
        id: id || `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: type,
        depth: depth,
        inputPorts: [],
        outputPorts: [],
        createdAt: Date.now(),
        portTypeRisks: new Set() // 记录自环风险的端口类型
    };
    
    // 创建输入端口
    inputPortSpecs.forEach((spec, index) => {
        const portTypeKey = `${spec.type}-${spec.color}`;
        node.inputPorts.push({
            id: `${node.id}_in_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'input',
            nodeId: node.id,
            portTypeKey: portTypeKey,
            x: 0,
            y: 0
        });
    });
    
    // 创建输出端口
    outputPortSpecs.forEach((spec, index) => {
        const portTypeKey = `${spec.type}-${spec.color}`;
        
        // 检查自环风险
        const hasInputOfSameType = node.inputPorts.some(p => p.portTypeKey === portTypeKey);
        if (hasInputOfSameType) {
            node.portTypeRisks.add(portTypeKey);
            console.warn('[MODE3-PORT-DAG] Self-loop risk detected for port type', portTypeKey, 'in node', node.id);
        }
        
        node.outputPorts.push({
            id: `${node.id}_out_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'output',
            nodeId: node.id,
            portTypeKey: portTypeKey,
            x: 0,
            y: 0
        });
    });
    
    return node;
}

// ========== 端口DAG扩展 ==========

function expandWithPortDAG() {
    console.log('[MODE3-PORT-DAG] Expanding with port-type DAG engine');
    
    try {
        // 收集当前状态
        const existingNodes = [...mode3PortDAGState.temporaryNodes, ...mode3PortDAGState.placedNodes];
        const existingConnections = mode3PortDAGState.connections;
        
        // 配置扩展参数
        const expansionConfig = {
            targetNodesPerPortType: 2,
            maxNodesPerWave: 4,
            avoidSelfLoops: true,
            balancePriority: 0.8
        };
        
        // 使用端口DAG引擎生成新节点
        const newNodes = mode3PortDAGState.portDAGEngine.generatePortTypeBasedTopology(
            existingNodes,
            existingConnections,
            expansionConfig
        );
        
        // 定位新节点到临时区域
        positionPortDAGNodesInTemporaryArea(newNodes);
        
        // 添加到临时节点池
        mode3PortDAGState.temporaryNodes.push(...newNodes);
        
        // 更新统计信息
        updatePortTypeStatistics();
        
        console.log('[MODE3-PORT-DAG] Added', newNodes.length, 'new nodes with port-type DAG constraints');
        
    } catch (error) {
        console.error('[MODE3-PORT-DAG] Expansion failed:', error);
        // 回退到简单生成
        generateFallbackPortDAGNodes();
    }
}

function positionPortDAGNodesInTemporaryArea(nodes) {
    const margin = 20;
    const nodeSize = 60;
    const spacing = 80;
    
    nodes.forEach((node, index) => {
        node.x = margin + nodeSize/2;
        node.y = margin + nodeSize/2 + index * spacing;
        node.area = 'temporary';
    });
}

function generateFallbackPortDAGNodes() {
    console.log('[MODE3-PORT-DAG] Generating fallback nodes with anti-self-loop strategy');
    
    // 生成避免自环的节点
    const fallbackNodes = [
        createPortDAGNode(
            null,
            'intermediate',
            1,
            [{ type: 'triangle', color: '#4CAF50' }], // 输入：绿三角
            [{ type: 'diamond', color: '#FFC107' }]   // 输出：黄菱形（不同类型）
        ),
        createPortDAGNode(
            null,
            'intermediate',
            1,
            [{ type: 'diamond', color: '#FFC107' }], // 输入：黄菱形
            [{ type: 'circle', color: '#2196F3' }]   // 输出：蓝圆形（不同类型）
        )
    ];
    
    fallbackNodes.forEach((node, index) => {
        node.x = 50;
        node.y = 100 + index * 80;
        node.area = 'temporary';
    });
    
    mode3PortDAGState.temporaryNodes.push(...fallbackNodes);
}

// ========== 端口类型统计 ==========

function updatePortTypeStatistics() {
    const stats = new Map();
    const allNodes = [...mode3PortDAGState.temporaryNodes, ...mode3PortDAGState.placedNodes];
    
    // 初始化统计
    ['square', 'circle', 'triangle', 'diamond'].forEach(type => {
        ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].forEach(color => {
            const key = `${type}-${color}`;
            stats.set(key, { inputs: 0, outputs: 0, balance: 0, nodes: new Set() });
        });
    });
    
    // 统计端口使用情况
    allNodes.forEach(node => {
        [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
            const key = port.portTypeKey || `${port.type}-${port.color}`;
            const stat = stats.get(key);
            
            if (stat) {
                if (port.side === 'input') {
                    stat.inputs++;
                } else {
                    stat.outputs++;
                }
                stat.nodes.add(node.id);
            }
        });
    });
    
    // 计算平衡
    stats.forEach((stat, key) => {
        stat.balance = stat.outputs - stat.inputs;
    });
    
    mode3PortDAGState.portTypeStats = stats;
    
    // 检测自环风险
    detectSelfLoopRisks(allNodes);
}

function detectSelfLoopRisks(nodes) {
    const risks = new Set();
    
    nodes.forEach(node => {
        const inputTypes = new Set();
        const outputTypes = new Set();
        
        (node.inputPorts || []).forEach(port => {
            const key = port.portTypeKey || `${port.type}-${port.color}`;
            inputTypes.add(key);
        });
        
        (node.outputPorts || []).forEach(port => {
            const key = port.portTypeKey || `${port.type}-${port.color}`;
            outputTypes.add(key);
        });
        
        // 检查重叠
        inputTypes.forEach(type => {
            if (outputTypes.has(type)) {
                risks.add(`${node.id}:${type}`);
                console.warn('[MODE3-PORT-DAG] Self-loop risk:', node.id, 'has both input and output of type', type);
            }
        });
    });
    
    mode3PortDAGState.selfLoopRisks = risks;
}

// ========== 端口DAG验证 ==========

function startPortDAGValidation() {
    setInterval(() => {
        validatePortDAGState();
    }, 3000);
}

function validatePortDAGState() {
    const allNodes = [...mode3PortDAGState.temporaryNodes, ...mode3PortDAGState.placedNodes];
    const connections = mode3PortDAGState.connections;
    
    // 使用端口DAG引擎验证
    const validation = mode3PortDAGState.portDAGEngine.validatePortTypeDAGs(allNodes, connections);
    
    mode3PortDAGState.lastValidation = validation;
    mode3PortDAGState.dagValidations = validation.portTypeResults;
    
    // 更新UI状态
    updatePortDAGValidationStatus(validation);
}

function updatePortDAGValidationStatus(validation) {
    const statusElement = document.getElementById('port-dag-status');
    if (statusElement) {
        statusElement.textContent = validation.allValid ? 'All Port Types Valid' : 'DAG Violations Detected';
        statusElement.className = validation.allValid ? 'status-good' : 'status-error';
    }
    
    // 更新详细状态
    const detailsElement = document.getElementById('port-dag-details');
    if (detailsElement) {
        const details = [];
        validation.portTypeResults.forEach((result, portType) => {
            const status = result.isDAG ? '✅' : '❌';
            const selfLoops = result.selfLoopNodes.size > 0 ? ` (${result.selfLoopNodes.size} self-loops)` : '';
            details.push(`${status} ${portType}: ${result.nodeCount} nodes, ${result.edgeCount} edges${selfLoops}`);
        });
        detailsElement.innerHTML = details.join('<br>');
    }
}

// ========== 渲染系统 ==========

function updateMode3PortDAGDisplay() {
    clearMode3PortDAGCanvases();
    drawMode3PortDAGNodes();
    drawMode3PortDAGConnections();
    drawMode3PortDAGUI();
}

function clearMode3PortDAGCanvases() {
    if (mode3PortDAGState.temporaryCtx) {
        mode3PortDAGState.temporaryCtx.clearRect(0, 0, mode3PortDAGState.temporaryCanvas.width, mode3PortDAGState.temporaryCanvas.height);
    }
    if (mode3PortDAGState.gameCtx) {
        mode3PortDAGState.gameCtx.clearRect(0, 0, mode3PortDAGState.gameCanvas.width, mode3PortDAGState.gameCanvas.height);
    }
}

function drawMode3PortDAGNodes() {
    // 绘制临时节点
    mode3PortDAGState.temporaryNodes.forEach(node => {
        drawMode3PortDAGNode(node, mode3PortDAGState.temporaryCtx);
    });
    
    // 绘制放置节点
    mode3PortDAGState.placedNodes.forEach(node => {
        drawMode3PortDAGNode(node, mode3PortDAGState.gameCtx);
    });
}

function drawMode3PortDAGNode(node, ctx) {
    const nodeSize = 60;
    const portSize = 12;
    
    // 检查自环风险
    const hasRisk = mode3PortDAGState.selfLoopRisks.size > 0 && 
                   Array.from(mode3PortDAGState.selfLoopRisks).some(risk => risk.startsWith(node.id + ':'));
    
    // 绘制节点主体
    ctx.fillStyle = hasRisk ? '#ff6b6b' : getMode3PortDAGNodeColor(node.type);
    ctx.fillRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制节点边框
    ctx.strokeStyle = hasRisk ? '#ff0000' : '#fff';
    ctx.lineWidth = hasRisk ? 3 : 2;
    ctx.strokeRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制端口
    drawMode3PortDAGNodePorts(node, ctx, nodeSize, portSize);
    
    // 绘制节点标签
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.id.substring(0, 8), node.x, node.y + 3);
    
    // 绘制深度和风险指示器
    ctx.fillStyle = 'rgba(255,255,255,0.7)';
    ctx.font = '8px Arial';
    ctx.fillText(`D:${node.depth}`, node.x, node.y - 20);
    
    if (hasRisk) {
        ctx.fillStyle = '#ff0000';
        ctx.fillText('⚠️', node.x, node.y + 25);
    }
}

function drawMode3PortDAGNodePorts(node, ctx, nodeSize, portSize) {
    // 输入端口（左侧）
    if (node.inputPorts && node.inputPorts.length > 0) {
        const spacing = nodeSize / (node.inputPorts.length + 1);
        node.inputPorts.forEach((port, index) => {
            const portX = node.x - nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3PortDAGPort(ctx, portX, portY, port, portSize);
        });
    }
    
    // 输出端口（右侧）
    if (node.outputPorts && node.outputPorts.length > 0) {
        const spacing = nodeSize / (node.outputPorts.length + 1);
        node.outputPorts.forEach((port, index) => {
            const portX = node.x + nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3PortDAGPort(ctx, portX, portY, port, portSize);
        });
    }
}

function drawMode3PortDAGPort(ctx, x, y, port, size) {
    // 检查是否有自环风险
    const portTypeKey = port.portTypeKey || `${port.type}-${port.color}`;
    const hasRisk = Array.from(mode3PortDAGState.selfLoopRisks).some(risk => 
        risk.includes(port.nodeId) && risk.includes(portTypeKey)
    );
    
    ctx.fillStyle = port.color;
    ctx.strokeStyle = hasRisk ? '#ff0000' : '#fff';
    ctx.lineWidth = hasRisk ? 2 : 1;
    
    switch (port.type) {
        case 'square':
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.strokeRect(x - size/2, y - size/2, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x - size/2, y + size/2);
            ctx.lineTo(x + size/2, y + size/2);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x + size/2, y);
            ctx.lineTo(x, y + size/2);
            ctx.lineTo(x - size/2, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
    
    // 绘制风险警告
    if (hasRisk) {
        ctx.fillStyle = '#ff0000';
        ctx.font = '8px Arial';
        ctx.textAlign = 'center';
        ctx.fillText('!', x, y + 2);
    }
}

function getMode3PortDAGNodeColor(type) {
    switch (type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        case 'intermediate': return '#2196F3';
        default: return '#666';
    }
}

function drawMode3PortDAGConnections() {
    mode3PortDAGState.connections.forEach(connection => {
        const fromPort = findPortDAGPortById(connection.fromPort);
        const toPort = findPortDAGPortById(connection.toPort);
        
        if (fromPort && toPort) {
            drawMode3PortDAGConnectionLine(mode3PortDAGState.gameCtx, fromPort, toPort);
        }
    });
}

function drawMode3PortDAGConnectionLine(ctx, fromPort, toPort) {
    // 检查连接是否违反端口类型DAG
    const fromPortType = fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`;
    const toPortType = toPort.portTypeKey || `${toPort.type}-${toPort.color}`;
    
    const isValid = fromPortType === toPortType;
    
    ctx.strokeStyle = isValid ? fromPort.color : '#ff0000';
    ctx.lineWidth = isValid ? 3 : 2;
    
    if (!isValid) {
        ctx.setLineDash([5, 5]);
    }
    
    ctx.beginPath();
    ctx.moveTo(fromPort.x, fromPort.y);
    ctx.lineTo(toPort.x, toPort.y);
    ctx.stroke();
    
    ctx.setLineDash([]);
}

function drawMode3PortDAGUI() {
    const ctx = mode3PortDAGState.gameCtx;
    
    // 绘制状态信息
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Wave: ${mode3PortDAGState.currentWave}`, 10, 25);
    ctx.fillText(`Self-Loop Risks: ${mode3PortDAGState.selfLoopRisks.size}`, 10, 45);
    
    // 绘制端口类型统计
    let yOffset = 65;
    mode3PortDAGState.portTypeStats.forEach((stat, portType) => {
        const balanceText = stat.balance === 0 ? 'Balanced' : 
                           stat.balance > 0 ? `+${stat.balance} out` : `${stat.balance} in`;
        ctx.fillText(`${portType}: ${balanceText}`, 10, yOffset);
        yOffset += 15;
    });
}

// ========== 事件处理 ==========

function handleMode3PortDAGMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const isTemporaryArea = event.target === mode3PortDAGState.temporaryCanvas;
    
    // 检查端口点击
    const clickedPort = findPortDAGPortAtPosition(x, y, isTemporaryArea);
    if (clickedPort) {
        handleMode3PortDAGPortClick(clickedPort);
        return;
    }
    
    // 检查节点点击
    const clickedNode = findPortDAGNodeAtPosition(x, y, isTemporaryArea);
    if (clickedNode) {
        startMode3PortDAGNodeDrag(clickedNode, x, y);
    }
}

function handleMode3PortDAGMouseMove(event) {
    if (mode3PortDAGState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        mode3PortDAGState.draggingNode.node.x = x;
        mode3PortDAGState.draggingNode.node.y = y;
        updateMode3PortDAGDisplay();
    }
}

function handleMode3PortDAGMouseUp(event) {
    if (mode3PortDAGState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        finishMode3PortDAGNodeDrag(x, y, event.target === mode3PortDAGState.gameCanvas);
        mode3PortDAGState.draggingNode = null;
        updateMode3PortDAGDisplay();
    }
}

function handleMode3PortDAGKeyDown(event) {
    switch (event.key) {
        case 'n':
        case 'N':
            nextPortDAGWave();
            break;
        case 'v':
        case 'V':
            validatePortDAGState();
            break;
        case 'r':
        case 'R':
            regeneratePortDAGNodes();
            break;
        case 's':
        case 'S':
            showPortDAGStatistics();
            break;
    }
}

// ========== 辅助函数 ==========

function findPortDAGNodeAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3PortDAGState.temporaryNodes : mode3PortDAGState.placedNodes;
    const nodeSize = 60;
    
    for (const node of nodes) {
        if (x >= node.x - nodeSize/2 && x <= node.x + nodeSize/2 &&
            y >= node.y - nodeSize/2 && y <= node.y + nodeSize/2) {
            return node;
        }
    }
    return null;
}

function findPortDAGPortAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3PortDAGState.temporaryNodes : mode3PortDAGState.placedNodes;
    const portSize = 12;
    
    for (const node of nodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.x !== undefined && port.y !== undefined) {
                if (x >= port.x - portSize/2 && x <= port.x + portSize/2 &&
                    y >= port.y - portSize/2 && y <= port.y + portSize/2) {
                    return port;
                }
            }
        }
    }
    return null;
}

function findPortDAGPortById(portId) {
    const allNodes = [...mode3PortDAGState.temporaryNodes, ...mode3PortDAGState.placedNodes];
    for (const node of allNodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        const port = allPorts.find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

function startMode3PortDAGNodeDrag(node, x, y) {
    mode3PortDAGState.draggingNode = {
        node: node,
        offsetX: x - node.x,
        offsetY: y - node.y
    };
}

function finishMode3PortDAGNodeDrag(x, y, isGameArea) {
    const node = mode3PortDAGState.draggingNode.node;
    
    if (isGameArea) {
        // 移动到游戏区域
        if (mode3PortDAGState.temporaryNodes.includes(node)) {
            mode3PortDAGState.temporaryNodes = mode3PortDAGState.temporaryNodes.filter(n => n !== node);
            mode3PortDAGState.placedNodes.push(node);
            node.area = 'placed';
        }
    } else {
        // 移动回临时区域
        if (mode3PortDAGState.placedNodes.includes(node)) {
            mode3PortDAGState.placedNodes = mode3PortDAGState.placedNodes.filter(n => n !== node);
            mode3PortDAGState.temporaryNodes.push(node);
            node.area = 'temporary';
        }
    }
    
    // 重新计算统计信息
    updatePortTypeStatistics();
}

function handleMode3PortDAGPortClick(port) {
    if (!mode3PortDAGState.selectedPort) {
        mode3PortDAGState.selectedPort = port;
        console.log('[MODE3-PORT-DAG] Selected port', port.id, 'type:', port.portTypeKey);
    } else {
        if (canConnectPortDAGPorts(mode3PortDAGState.selectedPort, port)) {
            createPortDAGConnection(mode3PortDAGState.selectedPort, port);
        } else {
            console.warn('[MODE3-PORT-DAG] Cannot connect ports - type mismatch or self-loop risk');
        }
        mode3PortDAGState.selectedPort = null;
    }
}

function canConnectPortDAGPorts(fromPort, toPort) {
    // 检查端口类型匹配
    const fromType = fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`;
    const toType = toPort.portTypeKey || `${toPort.type}-${toPort.color}`;
    
    if (fromType !== toType) {
        return false;
    }
    
    // 检查是否会创建自环
    if (fromPort.nodeId === toPort.nodeId) {
        return false;
    }
    
    // 检查端口方向
    if (fromPort.side === toPort.side) {
        return false;
    }
    
    return true;
}

function createPortDAGConnection(fromPort, toPort) {
    const connection = {
        id: `conn_${Date.now()}`,
        fromNode: fromPort.nodeId,
        fromPort: fromPort.id,
        toNode: toPort.nodeId,
        toPort: toPort.id,
        portType: fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`
    };
    
    mode3PortDAGState.connections.push(connection);
    console.log('[MODE3-PORT-DAG] Created connection', connection.id, 'for port type', connection.portType);
    
    // 重新验证
    validatePortDAGState();
    updateMode3PortDAGDisplay();
}

function nextPortDAGWave() {
    mode3PortDAGState.currentWave++;
    expandWithPortDAG();
    updateMode3PortDAGDisplay();
}

function regeneratePortDAGNodes() {
    mode3PortDAGState.temporaryNodes = [];
    expandWithPortDAG();
    updateMode3PortDAGDisplay();
}

function showPortDAGStatistics() {
    const stats = [];
    
    stats.push('=== Port Type DAG Statistics ===');
    stats.push(`Wave: ${mode3PortDAGState.currentWave}`);
    stats.push(`Self-Loop Risks: ${mode3PortDAGState.selfLoopRisks.size}`);
    stats.push('');
    
    stats.push('Port Type Balance:');
    mode3PortDAGState.portTypeStats.forEach((stat, portType) => {
        stats.push(`  ${portType}: ${stat.inputs} in, ${stat.outputs} out, balance: ${stat.balance}`);
    });
    
    stats.push('');
    stats.push('DAG Validations:');
    if (mode3PortDAGState.dagValidations) {
        mode3PortDAGState.dagValidations.forEach((result, portType) => {
            const status = result.isDAG ? 'VALID' : 'INVALID';
            stats.push(`  ${portType}: ${status} (${result.nodeCount} nodes, ${result.edgeCount} edges)`);
        });
    }
    
    alert(stats.join('\n'));
}

// ========== 全局函数 ==========

window.initializeMode3PortDAG = initializeMode3PortDAG;
window.updateMode3PortDAGDisplay = updateMode3PortDAGDisplay;
window.nextPortDAGWave = nextPortDAGWave;
window.validatePortDAGState = validatePortDAGState;
window.regeneratePortDAGNodes = regeneratePortDAGNodes;
window.showPortDAGStatistics = showPortDAGStatistics;

// ========== 自动初始化 ==========

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MODE3-PORT-DAG] DOM loaded, initializing port-DAG mode...');
    setTimeout(() => {
        if (typeof PortTypeDAGEngine !== 'undefined') {
            if (initializeMode3PortDAG()) {
                console.log('[MODE3-PORT-DAG] Port-DAG mode ready!');
            }
        } else {
            console.error('[MODE3-PORT-DAG] PortTypeDAGEngine not loaded');
        }
    }, 100);
});
