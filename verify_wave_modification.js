// Quick verification script for wave modification system
console.log('🌊 验证波次修改系统...');

// Mock Node class
class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = '';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// Mock game state
const mockGameState = {
    nodes: [],
    temporaryNodes: [],
    connections: [],
    portTypes: ['square', 'diamond', 'triangle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50'],
    infiniteMode: {
        currentWave: 1,
        adaptiveDifficulty: 1,
        isActive: true
    }
};

// Global assignments for compatibility
global.Node = TestNode;
global.gameState = mockGameState;

// Helper functions
function getRandomPortType() {
    return mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
}

function getRandomPortColor() {
    return mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
}

function analyzeGlobalSystemState() {
    const allNodes = [...mockGameState.nodes, ...mockGameState.temporaryNodes];
    return {
        allNodes,
        startNodes: allNodes.filter(n => n.type === 'start'),
        endNodes: allNodes.filter(n => n.type === 'end'),
        intermediateNodes: allNodes.filter(n => n.type === 'normal'),
        temporaryNodes: mockGameState.temporaryNodes,
        placedNodes: mockGameState.nodes.filter(n => !mockGameState.temporaryNodes.includes(n))
    };
}

function calculateGlobalPortBalance(globalAnalysis) {
    const typeCounts = new Map();
    
    globalAnalysis.allNodes.forEach(node => {
        [...node.inputPorts, ...node.outputPorts].forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!typeCounts.has(typeKey)) {
                typeCounts.set(typeKey, { input: 0, output: 0 });
            }
            const count = typeCounts.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                count.input++;
            } else {
                count.output++;
            }
        });
    });

    const imbalances = [];
    for (const [typeKey, count] of typeCounts.entries()) {
        if (count.input !== count.output) {
            imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
        }
    }

    return {
        isBalanced: imbalances.length === 0,
        imbalances,
        typeCounts
    };
}

function performStructuralModifications() {
    console.log('🔧 执行结构修改测试...');
    
    const modifications = {
        addedNodes: [],
        modifiedNodes: [],
        timestamp: Date.now()
    };

    // 添加新节点
    const newNode = new TestNode('normal');
    newNode.id = `test_added_${Date.now()}`;
    newNode.label = 'Test-Added';
    
    const portType = getRandomPortType();
    const portColor = getRandomPortColor();
    newNode.addInputPort(portType, portColor);
    newNode.addOutputPort(portType, portColor);
    
    mockGameState.nodes.push(newNode);
    modifications.addedNodes.push(newNode);
    
    console.log(`✅ 添加节点: ${newNode.id} (${portType}-${portColor})`);
    
    return modifications;
}

function applyConstraintPropagation(modifications) {
    console.log('🌐 应用约束传播测试...');

    try {
        const globalAnalysis = analyzeGlobalSystemState();
        const portBalance = calculateGlobalPortBalance(globalAnalysis);

        console.log(`端口平衡检查: ${portBalance.isBalanced ? '✅ 平衡' : '⚠️ 不平衡'}`);
        if (!portBalance.isBalanced) {
            console.log(`不平衡项: ${portBalance.imbalances.join(', ')}`);
        }

        return { 
            success: portBalance.isBalanced, 
            portBalance 
        };

    } catch (error) {
        console.error(`❌ 约束传播异常: ${error.message}`);
        return { success: false, error: error.message };
    }
}

// 创建测试场景
function createTestScenario() {
    console.log('🎬 创建测试场景...');
    
    mockGameState.nodes = [];
    
    // 起点
    const startNode = new TestNode('start');
    startNode.id = 'test_start';
    startNode.label = 'Start';
    startNode.addOutputPort('square', '#ff5252');
    startNode.moveTo(50, 200);

    // 终点
    const endNode = new TestNode('end');
    endNode.id = 'test_end';
    endNode.label = 'End';
    endNode.addInputPort('square', '#ff5252');
    endNode.moveTo(550, 200);

    mockGameState.nodes = [startNode, endNode];
    
    console.log(`✅ 创建场景: ${mockGameState.nodes.length} 个节点`);
    return { startNode, endNode };
}

// 运行测试
function runTests() {
    console.log('\n🧪 开始波次修改系统验证...\n');

    // 1. 创建测试场景
    const scenario = createTestScenario();
    console.log(`初始节点数: ${mockGameState.nodes.length}`);

    // 2. 执行结构修改
    const modifications = performStructuralModifications();
    console.log(`修改后节点数: ${mockGameState.nodes.length}`);

    // 3. 应用约束传播
    const propagationResult = applyConstraintPropagation(modifications);

    // 4. 输出结果
    console.log('\n📊 测试结果:');
    console.log(`- 结构修改: ${modifications.addedNodes.length > 0 ? '✅ 成功' : '❌ 失败'}`);
    console.log(`- 约束传播: ${propagationResult.success ? '✅ 成功' : '❌ 失败'}`);
    console.log(`- 系统状态: ${mockGameState.nodes.length} 个节点`);

    if (propagationResult.success) {
        console.log('\n🎉 波次修改系统验证通过！');
    } else {
        console.log('\n⚠️ 波次修改系统需要调整');
    }

    return propagationResult.success;
}

// 执行测试
const testResult = runTests();

// Node.js export
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runTests, createTestScenario, performStructuralModifications };
}

console.log('\n✅ 验证脚本完成');