<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Puzzle Mode Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .level-info {
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .area-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
            border: 1px solid #555;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .progress {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .progress-bar {
            background: #333;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧩 Puzzle Mode Test</h1>
            <p>Level-based connection puzzles with specific objectives</p>
        </div>
        
        <div class="level-info" id="level-info">
            <h3>Loading...</h3>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="area-header" style="color: #ffaa00;">🎯 Available Nodes</div>
                <canvas id="temporaryCanvas" width="280" height="400"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="area-header" style="color: #00aaff;">🏗️ Puzzle Area</div>
                <canvas id="gameCanvas" width="680" height="400"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="loadLevel(1)">🔄 Level 1</button>
            <button class="btn" onclick="loadLevel(2)">🔄 Level 2</button>
            <button class="btn" onclick="nextLevel()">⏭️ Next Level</button>
            <button class="btn" onclick="resetLevel()">🔄 Reset</button>
            <button class="btn" onclick="showHint()">💡 Hint</button>
        </div>
        
        <div class="progress">
            <h3>📊 Progress</h3>
            <div>Current Level: <span id="current-level">1</span> / <span id="max-level">10</span></div>
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill" style="width: 10%"></div>
            </div>
            <div>Score: <span id="score">0</span></div>
            <div>Connections: <span id="connection-count">0</span> / <span id="target-count">0</span></div>
        </div>
        
        <div class="instructions">
            <h3>🎮 How to Play</h3>
            <ul>
                <li><strong>Objective:</strong> Create the required connections to complete each level</li>
                <li><strong>Drag nodes:</strong> Move nodes from the available area to the puzzle area</li>
                <li><strong>Connect ports:</strong> Click matching ports (same shape and color) to connect them</li>
                <li><strong>Hints:</strong> Dotted lines show required connections</li>
                <li><strong>Complete:</strong> Make all required connections to advance to the next level</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🔍 Port Types</h3>
            <ul>
                <li><strong>🟥 Red Square:</strong> Basic connection type</li>
                <li><strong>🔵 Blue Circle:</strong> Secondary connection type</li>
                <li><strong>🟢 Green Triangle:</strong> Advanced connection type</li>
                <li><strong>🟨 Yellow Diamond:</strong> Special connection type</li>
            </ul>
        </div>
    </div>

    <!-- Load the puzzle mode implementation -->
    <script src="puzzle_mode.js"></script>
    
    <script>
        // UI update functions
        function updateProgressDisplay() {
            document.getElementById('current-level').textContent = puzzleState.currentLevel;
            document.getElementById('max-level').textContent = puzzleState.maxLevel;
            document.getElementById('score').textContent = puzzleState.score;
            document.getElementById('connection-count').textContent = puzzleState.connections.length;
            document.getElementById('target-count').textContent = puzzleState.targetConnections.length;
            
            const progress = (puzzleState.currentLevel / puzzleState.maxLevel) * 100;
            document.getElementById('progress-fill').style.width = progress + '%';
        }
        
        function resetLevel() {
            loadLevel(puzzleState.currentLevel);
        }
        
        function showHint() {
            const remaining = puzzleState.targetConnections.length - puzzleState.connections.length;
            alert(`Hint: You need to make ${remaining} more connections.\n\nLook for the dotted lines showing required connections!`);
        }
        
        // Override the original updateLevelInfo to also update progress
        const originalUpdateLevelInfo = updateLevelInfo;
        updateLevelInfo = function(level) {
            originalUpdateLevelInfo(level);
            updateProgressDisplay();
        };
        
        // Override checkLevelComplete to update progress
        const originalCheckLevelComplete = checkLevelComplete;
        checkLevelComplete = function() {
            originalCheckLevelComplete();
            updateProgressDisplay();
        };
        
        // Auto-update progress
        setInterval(updateProgressDisplay, 1000);
        
        // Initial setup
        setTimeout(() => {
            updateProgressDisplay();
            console.log('[UI] Puzzle mode UI ready');
        }, 500);
    </script>
</body>
</html>
