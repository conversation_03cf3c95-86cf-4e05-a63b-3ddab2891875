@echo off
title Mode 3 Infinite Construction - Blueprint Game

echo.
echo ========================================
echo    Mode 3 Infinite Construction
echo    Blueprint Connection Game
echo ========================================
echo.

echo Available Mode 3 Tests:
echo.
echo [1] Mode 3 Infinite Construction (Full Game)
echo [2] SYSTEM.md Compliance Test
echo [3] Port-DAG Conflict Resolution Test
echo [4] Compensation Algorithm Test
echo [5] All Tests (Open Multiple Tabs)
echo.

set /p choice="Please select an option (1-5): "

if "%choice%"=="1" goto mode3_infinite
if "%choice%"=="2" goto system_md_test
if "%choice%"=="3" goto port_dag_test
if "%choice%"=="4" goto compensation_test
if "%choice%"=="5" goto all_tests
goto invalid_choice

:mode3_infinite
echo.
echo Starting Mode 3 Infinite Construction...
echo Opening: mode3_infinite_test.html
echo.
echo Features:
echo - Infinite construction mode
echo - Dynamic node generation
echo - Automatic port balancing
echo - SYSTEM.md compliant
echo.
start "" "mode3_infinite_test.html"
goto end

:system_md_test
echo.
echo Starting SYSTEM.md Compliance Test...
echo Opening: test_system_md_compliance.html
echo.
echo Features:
echo - Full SYSTEM.md constraint validation
echo - Automated testing suite
echo - Detailed compliance reports
echo - Constraint violation detection
echo.
start "" "test_system_md_compliance.html"
goto end

:port_dag_test
echo.
echo Starting Port-DAG Conflict Resolution Test...
echo Opening: mode3_conflict_free_test.html
echo.
echo Features:
echo - Conflict-free node generation
echo - Virtual-physical DAG separation
echo - Self-loop risk detection
echo - Zero-tolerance conflict policy
echo.
start "" "mode3_conflict_free_test.html"
goto end

:compensation_test
echo.
echo Starting Compensation Algorithm Test...
echo Opening: mode3_infinite_test.html
echo.
echo Features:
echo - Port balance compensation
echo - Depth constraint management
echo - Intelligent node generation
echo - Emergency fix mechanisms
echo.
start "" "mode3_infinite_test.html"
goto end

:all_tests
echo.
echo Starting All Mode 3 Tests...
echo Opening multiple test pages...
echo.

echo Opening Mode 3 Infinite Construction...
start "" "mode3_infinite_test.html"
timeout /t 2 /nobreak >nul

echo Opening SYSTEM.md Compliance Test...
start "" "test_system_md_compliance.html"
timeout /t 2 /nobreak >nul

echo Opening Port-DAG Conflict Resolution Test...
start "" "mode3_conflict_free_test.html"
timeout /t 2 /nobreak >nul

echo.
echo All tests launched successfully!
goto end

:invalid_choice
echo.
echo Invalid choice. Please run the script again and select 1-5.
echo.
pause
goto end

:end
echo.
echo Mode 3 Testing Instructions:
echo.
echo How to Test:
echo   1. Drag nodes from temporary pool to construction area
echo   2. Click ports to create connections (matching types only)
echo   3. Press 'V' or click Validate to check solution
echo   4. Watch automatic modifications and compensation
echo   5. Observe infinite progression through rounds
echo.
echo Key Features to Test:
echo   - Port type balancing (every type should be balanced)
echo   - DAG constraint enforcement (no cycles allowed)
echo   - Depth constraint validation (start=0, end=max)
echo   - Automatic node generation and compensation
echo   - Real-time conflict detection and resolution
echo.
echo Controls:
echo   - V: Validate solution and progress
echo   - N: Generate new nodes
echo   - R: Regenerate/Reset
echo   - Mouse: Drag nodes and click ports
echo.
echo Monitor the logs and statistics for detailed information!
echo.
echo Press any key to exit...
pause >nul
