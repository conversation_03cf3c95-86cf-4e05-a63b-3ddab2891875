<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Test Execution Analysis</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .analysis-container {
            max-width: 1600px;
            margin: 0 auto;
        }
        .test-output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 11px;
        }
        .analysis-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .analysis-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
        }
        .failure-analysis {
            background: #3a1a1a;
            border-left: 5px solid #ff0000;
        }
        .success-analysis {
            background: #1a3a1a;
            border-left: 5px solid #00ff00;
        }
        .metric {
            display: inline-block;
            margin: 5px;
            padding: 5px 10px;
            background: #333;
            border-radius: 3px;
            font-size: 12px;
        }
        .level-result {
            margin: 5px 0;
            padding: 8px;
            border-radius: 3px;
        }
        .level-pass { background: #1a3a1a; }
        .level-fail { background: #3a1a1a; }
        .level-error { background: #3a2a1a; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
    </style>
</head>
<body>
    <div class="analysis-container">
        <h1>🧪 Mode 3 Comprehensive Test Execution & Analysis</h1>
        
        <div style="text-align: center; margin: 20px 0;">
            <button id="execute-btn" onclick="executeComprehensiveTest()">🚀 Execute Test Suite</button>
            <button id="analyze-btn" onclick="analyzeResults()" disabled>📊 Analyze Results</button>
            <button onclick="clearOutput()">🧹 Clear Output</button>
        </div>

        <div class="test-output" id="test-output"></div>
        
        <div class="analysis-grid" id="analysis-grid" style="display: none;">
            <div class="analysis-panel">
                <h3>📈 Test Summary</h3>
                <div id="test-summary"></div>
            </div>
            
            <div class="analysis-panel">
                <h3>🎯 SYSTEM.md Criteria</h3>
                <div id="systemmd-analysis"></div>
            </div>
            
            <div class="analysis-panel failure-analysis">
                <h3>❌ Failure Analysis</h3>
                <div id="failure-analysis"></div>
            </div>
            
            <div class="analysis-panel">
                <h3>⚡ Performance Metrics</h3>
                <div id="performance-analysis"></div>
            </div>
        </div>

        <div id="detailed-results" style="display: none;">
            <h2>📋 Detailed Level Results</h2>
            <div id="level-results"></div>
        </div>

        <div id="algorithm-analysis" style="display: none;">
            <h2>🔬 Algorithm Analysis</h2>
            <div id="algorithm-findings"></div>
        </div>
    </div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[ANALYSIS] Game script loaded successfully');
            document.getElementById('execute-btn').disabled = false;
        };
        script.onerror = function() {
            console.error('[ANALYSIS] Failed to load game script');
            appendOutput('[ERROR] Failed to load game script', 'error');
        };
        document.head.appendChild(script);

        let testResults = null;
        let executionLog = [];

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('test-output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            const formattedMessage = `${timestamp} ${prefix} ${message}\n`;
            output.textContent += formattedMessage;
            output.scrollTop = output.scrollHeight;
            
            // Store in execution log
            executionLog.push({ timestamp, type, message });
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function executeComprehensiveTest() {
            const executeBtn = document.getElementById('execute-btn');
            const analyzeBtn = document.getElementById('analyze-btn');
            
            executeBtn.disabled = true;
            executeBtn.textContent = '🔄 Executing...';
            analyzeBtn.disabled = true;
            
            try {
                console.log('[ANALYSIS] Starting comprehensive Mode 3 test execution...');
                
                // Clear previous results
                testResults = null;
                executionLog = [];
                
                // Wait for game initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Execute the test suite
                if (window.runMode3Test) {
                    testResults = await window.runMode3Test();
                    console.log('[ANALYSIS] Test execution completed');
                    
                    analyzeBtn.disabled = false;
                    executeBtn.textContent = '✅ Test Completed';
                    
                } else if (window.mode3TestSuite) {
                    testResults = await window.mode3TestSuite.runComprehensiveTest();
                    console.log('[ANALYSIS] Test suite execution completed');
                    
                    analyzeBtn.disabled = false;
                    executeBtn.textContent = '✅ Test Completed';
                    
                } else {
                    throw new Error('Test functions not available');
                }
                
            } catch (error) {
                console.error('[ANALYSIS] Test execution failed:', error);
                executeBtn.textContent = '❌ Test Failed';
            }
            
            setTimeout(() => {
                executeBtn.disabled = false;
                executeBtn.textContent = '🚀 Execute Test Suite';
            }, 3000);
        }

        function analyzeResults() {
            if (!testResults) {
                console.log('[ANALYSIS] No test results available for analysis');
                return;
            }
            
            console.log('[ANALYSIS] Starting comprehensive result analysis...');
            
            // Show analysis panels
            document.getElementById('analysis-grid').style.display = 'grid';
            document.getElementById('detailed-results').style.display = 'block';
            document.getElementById('algorithm-analysis').style.display = 'block';
            
            // Generate test summary
            generateTestSummary();
            
            // Analyze SYSTEM.md criteria
            analyzeSystemMdCriteria();
            
            // Analyze failures
            analyzeFailures();
            
            // Analyze performance
            analyzePerformance();
            
            // Generate detailed level results
            generateDetailedResults();
            
            // Generate algorithm analysis
            generateAlgorithmAnalysis();
            
            console.log('[ANALYSIS] Comprehensive analysis completed');
        }

        function generateTestSummary() {
            const summary = testResults.summary;
            const passRate = (summary.passedTests / summary.totalTests * 100).toFixed(1);
            
            document.getElementById('test-summary').innerHTML = `
                <div class="metric">Pass Rate: ${passRate}%</div>
                <div class="metric">Total Tests: ${summary.totalTests}</div>
                <div class="metric">Passed: ${summary.passedTests}</div>
                <div class="metric">Failed: ${summary.failedTests}</div>
                <div class="metric">Errors: ${summary.errorTests}</div>
                <div class="metric">Timeouts: ${summary.timeoutTests}</div>
                <div class="metric">Duration: ${testResults.totalDuration.toFixed(0)}ms</div>
            `;
        }

        function analyzeSystemMdCriteria() {
            const criteria = testResults.systemMdCriteria;
            let html = '';
            
            Object.keys(criteria).forEach(criterion => {
                const stats = criteria[criterion];
                const total = stats.passed + stats.failed;
                const rate = total > 0 ? (stats.passed / total * 100).toFixed(1) : '0.0';
                
                html += `
                    <div class="metric">
                        ${criterion}: ${stats.passed}/${total} (${rate}%)
                    </div>
                `;
            });
            
            document.getElementById('systemmd-analysis').innerHTML = html;
        }

        function analyzeFailures() {
            const failedLevels = testResults.levels.filter(level => level.status !== 'PASS');
            
            let html = `<p><strong>Failed Levels:</strong> ${failedLevels.length}/10</p>`;
            
            if (failedLevels.length > 0) {
                html += '<div style="margin-top: 10px;">';
                failedLevels.forEach(level => {
                    html += `
                        <div class="level-result level-${level.status.toLowerCase()}">
                            <strong>Level ${level.level}:</strong> ${level.status}
                            ${level.errors.length > 0 ? `<br>Errors: ${level.errors.join('; ')}` : ''}
                        </div>
                    `;
                });
                html += '</div>';
            }
            
            document.getElementById('failure-analysis').innerHTML = html;
        }

        function analyzePerformance() {
            const performance = testResults.performance || {};
            
            let html = `
                <div class="metric">Avg Time: ${(performance.averageTime || 0).toFixed(2)}ms</div>
                <div class="metric">Max Time: ${(performance.maxTime || 0).toFixed(2)}ms</div>
                <div class="metric">Min Time: ${(performance.minTime || 0).toFixed(2)}ms</div>
            `;
            
            if (performance.memoryLeaks && performance.memoryLeaks.length > 0) {
                html += `<div class="metric">Memory Leaks: ${performance.memoryLeaks.length}</div>`;
            }
            
            if (performance.bottlenecks && performance.bottlenecks.length > 0) {
                html += `<div class="metric">Bottlenecks: ${performance.bottlenecks.length}</div>`;
            }
            
            document.getElementById('performance-analysis').innerHTML = html;
        }

        function generateDetailedResults() {
            let html = '';
            
            testResults.levels.forEach(level => {
                const systemMdPassed = Object.values(level.systemMdResults || {}).filter(Boolean).length;
                const systemMdTotal = Object.keys(level.systemMdResults || {}).length;
                
                html += `
                    <div class="level-result level-${level.status.toLowerCase()}">
                        <h4>Level ${level.level} - ${level.status}</h4>
                        <p><strong>Duration:</strong> ${level.duration.toFixed(2)}ms</p>
                        <p><strong>Nodes Generated:</strong> ${level.nodeCount}</p>
                        <p><strong>SYSTEM.md Criteria:</strong> ${systemMdPassed}/${systemMdTotal} passed</p>
                        ${level.errors.length > 0 ? `<p><strong>Errors:</strong> ${level.errors.join('; ')}</p>` : ''}
                    </div>
                `;
            });
            
            document.getElementById('level-results').innerHTML = html;
        }

        function generateAlgorithmAnalysis() {
            // Analyze patterns in the execution log and test results
            const failurePatterns = analyzeFailurePatterns();
            const performanceIssues = analyzePerformanceIssues();
            const systemMdIssues = analyzeSystemMdIssues();
            
            let html = `
                <div class="analysis-panel">
                    <h4>🔍 Failure Patterns</h4>
                    ${failurePatterns}
                </div>
                <div class="analysis-panel">
                    <h4>⚡ Performance Issues</h4>
                    ${performanceIssues}
                </div>
                <div class="analysis-panel">
                    <h4>📋 SYSTEM.md Issues</h4>
                    ${systemMdIssues}
                </div>
            `;
            
            document.getElementById('algorithm-findings').innerHTML = html;
        }

        function analyzeFailurePatterns() {
            const failedLevels = testResults.levels.filter(level => level.status !== 'PASS');
            
            if (failedLevels.length === 0) {
                return '<p>No failure patterns detected - all tests passed!</p>';
            }
            
            // Analyze which levels fail most
            const failureLevels = failedLevels.map(level => level.level);
            const isEarlyFailure = failureLevels.some(level => level <= 3);
            const isLateFailure = failureLevels.some(level => level >= 7);
            
            let analysis = '<ul>';
            
            if (isEarlyFailure) {
                analysis += '<li>Early level failures (1-3) suggest fundamental algorithm issues</li>';
            }
            
            if (isLateFailure) {
                analysis += '<li>Late level failures (7-10) suggest scalability/complexity issues</li>';
            }
            
            // Analyze error types
            const errorTypes = new Set();
            failedLevels.forEach(level => {
                level.errors.forEach(error => {
                    if (error.includes('Port Mapping')) errorTypes.add('Port Mapping');
                    if (error.includes('DAG Topology')) errorTypes.add('DAG Topology');
                    if (error.includes('Port Balance')) errorTypes.add('Port Balance');
                    if (error.includes('Flow Conservation')) errorTypes.add('Flow Conservation');
                });
            });
            
            errorTypes.forEach(errorType => {
                analysis += `<li>Consistent ${errorType} failures detected</li>`;
            });
            
            analysis += '</ul>';
            
            return analysis;
        }

        function analyzePerformanceIssues() {
            const levels = testResults.levels;
            const avgTime = levels.reduce((sum, level) => sum + level.duration, 0) / levels.length;
            const slowLevels = levels.filter(level => level.duration > avgTime * 2);
            
            let analysis = '<ul>';
            
            if (slowLevels.length > 0) {
                analysis += `<li>${slowLevels.length} levels significantly slower than average</li>`;
                analysis += `<li>Slow levels: ${slowLevels.map(level => level.level).join(', ')}</li>`;
            }
            
            if (testResults.performance && testResults.performance.bottlenecks) {
                analysis += `<li>${testResults.performance.bottlenecks.length} performance bottlenecks detected</li>`;
            }
            
            analysis += `<li>Average execution time: ${avgTime.toFixed(2)}ms</li>`;
            analysis += '</ul>';
            
            return analysis;
        }

        function analyzeSystemMdIssues() {
            const criteria = testResults.systemMdCriteria;
            let analysis = '<ul>';
            
            Object.keys(criteria).forEach(criterion => {
                const stats = criteria[criterion];
                const total = stats.passed + stats.failed;
                const failureRate = total > 0 ? (stats.failed / total * 100).toFixed(1) : '0.0';
                
                if (stats.failed > 0) {
                    analysis += `<li>${criterion}: ${failureRate}% failure rate (${stats.failed}/${total})</li>`;
                }
            });
            
            if (analysis === '<ul>') {
                analysis += '<li>All SYSTEM.md criteria passing successfully!</li>';
            }
            
            analysis += '</ul>';
            
            return analysis;
        }

        function clearOutput() {
            document.getElementById('test-output').textContent = '';
            executionLog = [];
        }

        // Initialize
        console.log('[ANALYSIS] Test execution analysis framework loaded');
    </script>
</body>
</html>
