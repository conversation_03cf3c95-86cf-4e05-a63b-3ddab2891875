<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Systematic Flow-First Debug Framework</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .framework-container {
            max-width: 1900px;
            margin: 0 auto;
        }
        .debug-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .debug-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .panel-success { border-left-color: #00ff00; }
        .panel-error { border-left-color: #ff0000; }
        .panel-warning { border-left-color: #ffaa00; }
        .panel-info { border-left-color: #00aaff; }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .metric {
            background: #333;
            padding: 8px;
            border-radius: 3px;
            text-align: center;
            font-size: 12px;
        }
        .metric.pass { background: #1a3a1a; color: #00ff00; }
        .metric.fail { background: #3a1a1a; color: #ff0000; }
        .metric.warn { background: #3a3a1a; color: #ffaa00; }
        .metric.info { background: #1a2a3a; color: #00aaff; }
        .log-output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 11px;
            font-family: 'Consolas', 'Courier New', monospace;
        }
        .error-analysis {
            background: #2a1a1a;
            border: 1px solid #444;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
        }
        .iteration-history {
            background: #1a1a2a;
            border: 1px solid #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .progress-container {
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
        .log-level-error { color: #ff4444; font-weight: bold; }
        .log-level-warn { color: #ffaa44; }
        .log-level-info { color: #44aaff; }
        .log-level-debug { color: #888888; }
        .log-level-success { color: #44ff44; font-weight: bold; }
        .stack-trace { color: #ff8888; font-size: 10px; margin-left: 20px; }
    </style>
</head>
<body>
    <div class="framework-container">
        <h1>🔬 Systematic Flow-First Algorithm Debug Framework</h1>
        <p><strong>Objective:</strong> Comprehensive iterative testing with enhanced error logging until 100% solvability guarantee</p>
        
        <div class="debug-grid">
            <div class="debug-panel panel-info">
                <h3>🎯 Current Test Status</h3>
                <div class="metrics-grid">
                    <div class="metric info" id="current-iteration">Iteration: 0</div>
                    <div class="metric info" id="test-phase">Phase: Ready</div>
                    <div class="metric info" id="current-level">Level: N/A</div>
                    <div class="metric info" id="execution-time">Time: 0ms</div>
                </div>
            </div>
            
            <div class="debug-panel panel-warning">
                <h3>📊 Solvability Metrics</h3>
                <div class="metrics-grid">
                    <div class="metric warn" id="solvability-rate">Solvability: 0%</div>
                    <div class="metric warn" id="pass-rate">Pass Rate: 0%</div>
                    <div class="metric warn" id="system-md-compliance">SYSTEM.md: 0%</div>
                    <div class="metric warn" id="performance-score">Performance: N/A</div>
                </div>
            </div>
        </div>

        <div class="debug-grid">
            <div class="debug-panel panel-error">
                <h3>❌ Error Analysis</h3>
                <div class="metrics-grid">
                    <div class="metric fail" id="critical-errors">Critical: 0</div>
                    <div class="metric fail" id="validation-errors">Validation: 0</div>
                    <div class="metric warn" id="parameter-errors">Parameter: 0</div>
                    <div class="metric warn" id="logic-errors">Logic: 0</div>
                </div>
                <div id="error-patterns" class="error-analysis"></div>
            </div>
            
            <div class="debug-panel panel-success">
                <h3>✅ Success Tracking</h3>
                <div class="metrics-grid">
                    <div class="metric pass" id="successful-levels">Success: 0/10</div>
                    <div class="metric pass" id="zero-crashes">Crashes: 0</div>
                    <div class="metric pass" id="memory-stable">Memory: Stable</div>
                    <div class="metric pass" id="target-progress">Target: 0%</div>
                </div>
            </div>
        </div>

        <div class="progress-container">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div style="text-align: center; margin: 10px 0;">
                <span id="progress-text">Ready to start systematic testing</span>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button id="start-systematic-btn" onclick="startSystematicTesting()">🚀 Start Systematic Testing</button>
            <button id="single-iteration-btn" onclick="runSingleDebugIteration()" disabled>🔄 Single Iteration</button>
            <button id="analyze-patterns-btn" onclick="analyzeErrorPatterns()" disabled>🔍 Analyze Patterns</button>
            <button id="export-logs-btn" onclick="exportDebugLogs()" disabled>📄 Export Logs</button>
            <button onclick="clearAllLogs()">🧹 Clear Logs</button>
        </div>

        <div class="debug-grid">
            <div>
                <h3>📋 Real-Time Debug Log</h3>
                <div class="log-output" id="debug-log"></div>
            </div>
            <div>
                <h3>🔍 Error Stack Traces</h3>
                <div class="log-output" id="error-log"></div>
            </div>
        </div>

        <div id="iteration-history-panel" style="display: none;">
            <h2>📈 Iteration History & Pattern Analysis</h2>
            <div id="iteration-history"></div>
        </div>
    </div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[SYSTEMATIC-DEBUG] Game script loaded successfully');
            document.getElementById('start-systematic-btn').disabled = false;
            document.getElementById('single-iteration-btn').disabled = false;
            document.getElementById('analyze-patterns-btn').disabled = false;
            document.getElementById('export-logs-btn').disabled = false;
        };
        script.onerror = function() {
            console.error('[SYSTEMATIC-DEBUG] Failed to load game script');
            logError('Failed to load game script', new Error('Script load failed'));
        };
        document.head.appendChild(script);

        // Enhanced logging system
        let debugState = {
            currentIteration: 0,
            maxIterations: 20,
            iterationHistory: [],
            errorPatterns: new Map(),
            performanceMetrics: [],
            debugLogs: [],
            errorLogs: [],
            isRunning: false,
            startTime: null,
            targetSolvabilityRate: 100,
            targetPassRate: 90
        };

        // Console capture with enhanced formatting
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;
        const debugLogElement = document.getElementById('debug-log');
        const errorLogElement = document.getElementById('error-log');
        
        function logDebug(message, level = 'info', data = null) {
            const timestamp = new Date().toISOString();
            const logEntry = {
                timestamp,
                level,
                message,
                data,
                iteration: debugState.currentIteration
            };
            
            debugState.debugLogs.push(logEntry);
            
            const formattedTime = timestamp.substr(11, 12);
            const levelClass = `log-level-${level}`;
            let formattedMessage = `<span class="${levelClass}">[${formattedTime}] [${level.toUpperCase()}] ${message}</span>`;
            
            if (data) {
                formattedMessage += `\n<span class="log-level-debug">Data: ${JSON.stringify(data, null, 2)}</span>`;
            }
            
            debugLogElement.innerHTML += formattedMessage + '\n';
            debugLogElement.scrollTop = debugLogElement.scrollHeight;
        }
        
        function logError(message, error, context = null) {
            const timestamp = new Date().toISOString();
            const errorEntry = {
                timestamp,
                message,
                error: error ? {
                    name: error.name,
                    message: error.message,
                    stack: error.stack
                } : null,
                context,
                iteration: debugState.currentIteration
            };
            
            debugState.errorLogs.push(errorEntry);
            
            const formattedTime = timestamp.substr(11, 12);
            let formattedError = `<span class="log-level-error">[${formattedTime}] [ERROR] ${message}</span>`;
            
            if (error && error.stack) {
                formattedError += `\n<span class="stack-trace">${error.stack}</span>`;
            }
            
            if (context) {
                formattedError += `\n<span class="log-level-debug">Context: ${JSON.stringify(context, null, 2)}</span>`;
            }
            
            errorLogElement.innerHTML += formattedError + '\n';
            errorLogElement.scrollTop = errorLogElement.scrollHeight;
            
            // Track error patterns
            const errorPattern = extractErrorPattern(message, error);
            if (!debugState.errorPatterns.has(errorPattern)) {
                debugState.errorPatterns.set(errorPattern, []);
            }
            debugState.errorPatterns.get(errorPattern).push(errorEntry);
        }
        
        // Override console functions
        console.log = function(...args) {
            const message = args.join(' ');
            logDebug(message, 'info');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            const message = args.join(' ');
            logError(message, new Error(message));
            originalConsoleError.apply(console, args);
        };
        
        console.warn = function(...args) {
            const message = args.join(' ');
            logDebug(message, 'warn');
            originalConsoleWarn.apply(console, args);
        };

        async function startSystematicTesting() {
            const startBtn = document.getElementById('start-systematic-btn');
            startBtn.disabled = true;
            startBtn.textContent = '🔄 Running Systematic Tests...';
            
            debugState.isRunning = true;
            debugState.startTime = performance.now();
            debugState.currentIteration = 0;
            
            logDebug('========== SYSTEMATIC FLOW-FIRST ALGORITHM TESTING STARTED ==========', 'success');
            logDebug(`Target: ${debugState.targetSolvabilityRate}% solvability, ${debugState.targetPassRate}% pass rate`, 'info');
            
            try {
                for (let iteration = 1; iteration <= debugState.maxIterations; iteration++) {
                    debugState.currentIteration = iteration;
                    updateIterationUI();
                    
                    logDebug(`===== ITERATION ${iteration}/${debugState.maxIterations} =====`, 'info');
                    
                    const iterationResult = await runSingleDebugIteration();
                    debugState.iterationHistory.push(iterationResult);
                    
                    // Update UI with results
                    updateMetricsUI(iterationResult);
                    
                    // Check if target achieved
                    if (iterationResult.solvabilityRate >= debugState.targetSolvabilityRate &&
                        iterationResult.passRate >= debugState.targetPassRate) {
                        
                        logDebug('🎉 TARGET ACHIEVED! 100% solvability guarantee reached!', 'success');
                        break;
                    }
                    
                    // Analyze and fix issues
                    if (iterationResult.errors.length > 0) {
                        logDebug(`Found ${iterationResult.errors.length} issues to analyze`, 'warn');
                        await analyzeAndSuggestFixes(iterationResult.errors);
                    }
                    
                    // Add delay between iterations
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                generateSystematicReport();
                
            } catch (error) {
                logError('Systematic testing framework failed', error);
            }
            
            debugState.isRunning = false;
            startBtn.disabled = false;
            startBtn.textContent = '🚀 Start Systematic Testing';
        }

        async function runSingleDebugIteration() {
            const iterationStartTime = performance.now();
            logDebug(`Starting iteration ${debugState.currentIteration} with enhanced debugging`, 'info');
            
            const iterationResult = {
                iteration: debugState.currentIteration,
                timestamp: new Date().toISOString(),
                solvabilityRate: 0,
                passRate: 0,
                systemMdCompliance: 0,
                performanceMetrics: {},
                errors: [],
                levelResults: [],
                memoryUsage: performance.memory ? {
                    used: performance.memory.usedJSHeapSize,
                    total: performance.memory.totalJSHeapSize,
                    limit: performance.memory.jsHeapSizeLimit
                } : null
            };
            
            try {
                // Test solvability guarantee
                logDebug('Testing solvability guarantee across edge cases', 'info');
                document.getElementById('test-phase').textContent = 'Solvability Test';
                
                if (window.testSolvabilityGuarantee) {
                    const solvabilityResult = window.testSolvabilityGuarantee();
                    iterationResult.solvabilityRate = (solvabilityResult.passedTests / solvabilityResult.totalTests * 100);
                    
                    logDebug(`Solvability test completed: ${iterationResult.solvabilityRate.toFixed(1)}%`, 
                             iterationResult.solvabilityRate >= 90 ? 'success' : 'warn');
                    
                    // Collect solvability errors
                    solvabilityResult.testDetails.filter(test => !test.success).forEach(test => {
                        iterationResult.errors.push({
                            type: 'solvability',
                            level: 'solvability',
                            description: `${test.name}: ${test.errors.join('; ')}`,
                            severity: 'high',
                            context: test
                        });
                    });
                }
                
                // Test comprehensive Mode 3 (10 levels)
                logDebug('Testing comprehensive Mode 3 algorithm (10 levels)', 'info');
                document.getElementById('test-phase').textContent = 'Mode 3 Test';
                
                if (window.runMode3Test) {
                    const mode3Result = await window.runMode3Test();
                    iterationResult.passRate = (mode3Result.summary.passedTests / mode3Result.summary.totalTests * 100);
                    
                    logDebug(`Mode 3 test completed: ${iterationResult.passRate.toFixed(1)}% pass rate`, 
                             iterationResult.passRate >= 90 ? 'success' : 'warn');
                    
                    // Calculate SYSTEM.md compliance
                    const systemMdStats = mode3Result.systemMdCriteria;
                    let totalTests = 0;
                    let totalPassed = 0;
                    
                    Object.values(systemMdStats).forEach(criteria => {
                        totalTests += criteria.passed + criteria.failed;
                        totalPassed += criteria.passed;
                    });
                    
                    iterationResult.systemMdCompliance = totalTests > 0 ? (totalPassed / totalTests * 100) : 0;
                    
                    // Collect performance metrics
                    iterationResult.performanceMetrics = {
                        averageTime: mode3Result.performance?.averageTime || 0,
                        maxTime: mode3Result.performance?.maxTime || 0,
                        minTime: mode3Result.performance?.minTime || 0,
                        memoryLeaks: mode3Result.performance?.memoryLeaks?.length || 0,
                        bottlenecks: mode3Result.performance?.bottlenecks?.length || 0
                    };
                    
                    // Collect level-specific results and errors
                    mode3Result.levels.forEach(level => {
                        const levelResult = {
                            level: level.level,
                            status: level.status,
                            duration: level.duration,
                            nodeCount: level.nodeCount,
                            systemMdResults: level.systemMdResults,
                            errors: level.errors
                        };
                        
                        iterationResult.levelResults.push(levelResult);
                        
                        if (level.status !== 'PASS') {
                            level.errors.forEach(error => {
                                iterationResult.errors.push({
                                    type: 'level_failure',
                                    level: level.level,
                                    description: error,
                                    severity: level.status === 'ERROR' ? 'critical' : 'medium',
                                    context: levelResult
                                });
                            });
                        }
                    });
                }
                
                const iterationEndTime = performance.now();
                iterationResult.totalDuration = iterationEndTime - iterationStartTime;
                
                logDebug(`Iteration ${debugState.currentIteration} completed in ${iterationResult.totalDuration.toFixed(2)}ms`, 'info', {
                    solvabilityRate: iterationResult.solvabilityRate,
                    passRate: iterationResult.passRate,
                    systemMdCompliance: iterationResult.systemMdCompliance,
                    errorCount: iterationResult.errors.length
                });
                
            } catch (error) {
                logError(`Iteration ${debugState.currentIteration} failed`, error, {
                    iteration: debugState.currentIteration,
                    phase: document.getElementById('test-phase').textContent
                });
                
                iterationResult.errors.push({
                    type: 'iteration_failure',
                    level: 'system',
                    description: error.message,
                    severity: 'critical',
                    context: { stack: error.stack }
                });
            }
            
            return iterationResult;
        }

        function updateIterationUI() {
            document.getElementById('current-iteration').textContent = `Iteration: ${debugState.currentIteration}`;
            document.getElementById('progress-fill').style.width = `${(debugState.currentIteration / debugState.maxIterations) * 100}%`;
            document.getElementById('progress-text').textContent = `Iteration ${debugState.currentIteration}/${debugState.maxIterations}`;
        }

        function updateMetricsUI(result) {
            // Update solvability metrics
            document.getElementById('solvability-rate').textContent = `Solvability: ${result.solvabilityRate.toFixed(1)}%`;
            document.getElementById('solvability-rate').className = `metric ${result.solvabilityRate >= 90 ? 'pass' : 'fail'}`;
            
            document.getElementById('pass-rate').textContent = `Pass Rate: ${result.passRate.toFixed(1)}%`;
            document.getElementById('pass-rate').className = `metric ${result.passRate >= 90 ? 'pass' : 'fail'}`;
            
            document.getElementById('system-md-compliance').textContent = `SYSTEM.md: ${result.systemMdCompliance.toFixed(1)}%`;
            document.getElementById('system-md-compliance').className = `metric ${result.systemMdCompliance >= 95 ? 'pass' : 'warn'}`;
            
            // Update error counts
            const errorCounts = categorizeErrors(result.errors);
            document.getElementById('critical-errors').textContent = `Critical: ${errorCounts.critical}`;
            document.getElementById('validation-errors').textContent = `Validation: ${errorCounts.validation}`;
            document.getElementById('parameter-errors').textContent = `Parameter: ${errorCounts.parameter}`;
            document.getElementById('logic-errors').textContent = `Logic: ${errorCounts.logic}`;
            
            // Update success metrics
            const successfulLevels = result.levelResults.filter(level => level.status === 'PASS').length;
            document.getElementById('successful-levels').textContent = `Success: ${successfulLevels}/10`;
            
            // Update performance
            if (result.performanceMetrics) {
                document.getElementById('performance-score').textContent = `Performance: ${result.performanceMetrics.averageTime.toFixed(0)}ms`;
                document.getElementById('execution-time').textContent = `Time: ${result.totalDuration.toFixed(0)}ms`;
            }
            
            // Update target progress
            const targetProgress = Math.min(100, (result.solvabilityRate + result.passRate) / 2);
            document.getElementById('target-progress').textContent = `Target: ${targetProgress.toFixed(1)}%`;
        }

        function categorizeErrors(errors) {
            return {
                critical: errors.filter(e => e.severity === 'critical').length,
                validation: errors.filter(e => e.type.includes('validation') || e.description.includes('validation')).length,
                parameter: errors.filter(e => e.description.includes('parameter') || e.description.includes('iterable')).length,
                logic: errors.filter(e => e.type === 'level_failure' && e.severity === 'medium').length
            };
        }

        function extractErrorPattern(message, error) {
            if (message.includes('intermediateNodes is not iterable')) return 'intermediateNodes_not_iterable';
            if (message.includes('targetTypes is not iterable')) return 'targetTypes_not_iterable';
            if (message.includes('sourceTypes.map is not a function')) return 'sourceTypes_map_error';
            if (message.includes('Port Mapping')) return 'port_mapping_failure';
            if (message.includes('DAG Topology')) return 'dag_topology_failure';
            if (message.includes('Flow Conservation')) return 'flow_conservation_failure';
            if (message.includes('Port Balance')) return 'port_balance_failure';
            if (message.includes('Validation Error')) return 'validation_error';
            if (error && error.name === 'TypeError') return 'type_error';
            if (error && error.name === 'ReferenceError') return 'reference_error';
            return 'unknown_error';
        }

        async function analyzeAndSuggestFixes(errors) {
            logDebug('===== ERROR PATTERN ANALYSIS =====', 'info');
            
            const patternCounts = new Map();
            errors.forEach(error => {
                const pattern = extractErrorPattern(error.description, error.context);
                patternCounts.set(pattern, (patternCounts.get(pattern) || 0) + 1);
            });
            
            logDebug('Error pattern frequency:', 'info', Object.fromEntries(patternCounts));
            
            // Generate fix suggestions
            patternCounts.forEach((count, pattern) => {
                const suggestion = generateFixSuggestion(pattern, count);
                if (suggestion) {
                    logDebug(`💡 Fix suggestion for "${pattern}" (${count} occurrences): ${suggestion}`, 'warn');
                }
            });
        }

        function generateFixSuggestion(pattern, count) {
            const suggestions = {
                'intermediateNodes_not_iterable': 'Add array validation before iteration in validatePortMappingLegacy',
                'targetTypes_not_iterable': 'Fix parameter passing in calculateIntermediateSteps function',
                'sourceTypes_map_error': 'Ensure sourceTypes is an array before calling map function',
                'port_mapping_failure': 'Improve port compatibility checking algorithm',
                'dag_topology_failure': 'Fix depth assignment and cycle detection logic',
                'flow_conservation_failure': 'Ensure balanced input/output port generation',
                'port_balance_failure': 'Implement stricter port balance validation',
                'validation_error': 'Add comprehensive input validation to all validation functions',
                'type_error': 'Add type checking before operations',
                'reference_error': 'Check variable declarations and scope'
            };
            
            return suggestions[pattern] || 'Manual investigation required';
        }

        async function analyzeErrorPatterns() {
            logDebug('===== COMPREHENSIVE ERROR PATTERN ANALYSIS =====', 'info');
            
            const errorPatternsDiv = document.getElementById('error-patterns');
            let analysisHtml = '<h4>Error Pattern Analysis</h4>';
            
            debugState.errorPatterns.forEach((occurrences, pattern) => {
                analysisHtml += `<div><strong>${pattern}:</strong> ${occurrences.length} occurrences</div>`;
            });
            
            errorPatternsDiv.innerHTML = analysisHtml;
            
            document.getElementById('iteration-history-panel').style.display = 'block';
            
            // Generate iteration history
            const historyDiv = document.getElementById('iteration-history');
            let historyHtml = '';
            
            debugState.iterationHistory.forEach(iteration => {
                const success = iteration.solvabilityRate >= 90 && iteration.passRate >= 90;
                historyHtml += `
                    <div class="iteration-history">
                        <strong>Iteration ${iteration.iteration}</strong> - 
                        Solvability: ${iteration.solvabilityRate.toFixed(1)}%, 
                        Pass Rate: ${iteration.passRate.toFixed(1)}%, 
                        Errors: ${iteration.errors.length}
                        ${success ? ' ✅' : ' ❌'}
                    </div>
                `;
            });
            
            historyDiv.innerHTML = historyHtml;
        }

        function exportDebugLogs() {
            const exportData = {
                timestamp: new Date().toISOString(),
                debugState: debugState,
                debugLogs: debugState.debugLogs,
                errorLogs: debugState.errorLogs,
                iterationHistory: debugState.iterationHistory,
                errorPatterns: Object.fromEntries(debugState.errorPatterns)
            };
            
            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            
            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `flow-first-debug-logs-${new Date().toISOString().substr(0, 19).replace(/:/g, '-')}.json`;
            link.click();
            
            logDebug('Debug logs exported successfully', 'success');
        }

        function generateSystematicReport() {
            logDebug('========== SYSTEMATIC TESTING FINAL REPORT ==========', 'success');
            
            const finalIteration = debugState.iterationHistory[debugState.iterationHistory.length - 1];
            const success = finalIteration.solvabilityRate >= debugState.targetSolvabilityRate &&
                           finalIteration.passRate >= debugState.targetPassRate;
            
            logDebug(`Final Results after ${debugState.currentIteration} iterations:`, 'info', {
                solvabilityRate: finalIteration.solvabilityRate,
                passRate: finalIteration.passRate,
                systemMdCompliance: finalIteration.systemMdCompliance,
                totalErrors: finalIteration.errors.length,
                totalDuration: debugState.iterationHistory.reduce((sum, iter) => sum + iter.totalDuration, 0)
            });
            
            if (success) {
                logDebug('🎉🎉🎉 SUCCESS: 100% SOLVABILITY GUARANTEE ACHIEVED! 🎉🎉🎉', 'success');
            } else {
                logDebug('⚠️ Target not yet achieved. Further iterations needed.', 'warn');
            }
        }

        function clearAllLogs() {
            debugState.debugLogs = [];
            debugState.errorLogs = [];
            document.getElementById('debug-log').innerHTML = '';
            document.getElementById('error-log').innerHTML = '';
        }

        // Initialize
        logDebug('Systematic Flow-First Debug Framework initialized', 'success');
        logDebug('Enhanced error logging and stack trace capture enabled', 'info');
    </script>
</body>
</html>
