<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Infinite Construction Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .infinite-info {
            background: #2a2a2a;
            border: 2px solid #9C27B0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .info-item {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #00aaff;
        }
        
        .status-good { color: #4CAF50; }
        .status-error { color: #f44336; }
        .status-warning { color: #ff9800; }
        .status-building { color: #2196F3; }
        .status-validating { color: #ff9800; }
        .status-modifying { color: #9C27B0; }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #9C27B0;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .area-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
            border: 1px solid #555;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #9C27B0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #7B1FA2;
        }
        
        .btn.success {
            background: #4CAF50;
        }
        
        .btn.success:hover {
            background: #45a049;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .btn.warning {
            background: #ff9800;
        }
        
        .btn.warning:hover {
            background: #f57c00;
        }
        
        .game-loop-panel {
            background: #2a2a2a;
            border: 1px solid #9C27B0;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .loop-step {
            background: #3a2a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #9C27B0;
        }
        
        .loop-step.active {
            background: #4a3a4a;
            border-left-color: #E91E63;
        }
        
        .compensation-analysis {
            background: #1a3a1a;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .analysis-item {
            background: #2a4a2a;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #4CAF50;
        }
        
        .round-history {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .history-item {
            background: #2a2a3a;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .validation-status {
            background: #3a1a1a;
            border: 1px solid #ff4444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        
        .validation-status.success {
            background: #1a3a1a;
            border-color: #44ff44;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>♾️ Mode 3 Infinite Construction Test</h1>
            <p>无限构筑模式 - 补偿算法、动态修改、连续可解保证</p>
        </div>
        
        <div class="infinite-info">
            <h3>📊 Infinite Construction Status</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div>Current Round</div>
                    <div class="info-value" id="current-round">1</div>
                </div>
                <div class="info-item">
                    <div>Game Phase</div>
                    <div class="info-value status-building" id="game-phase">Building</div>
                </div>
                <div class="info-item">
                    <div>Temp Nodes</div>
                    <div class="info-value" id="temp-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Placed Nodes</div>
                    <div class="info-value" id="placed-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Connections</div>
                    <div class="info-value" id="connections">0</div>
                </div>
                <div class="info-item">
                    <div>Modifications</div>
                    <div class="info-value" id="modifications">0</div>
                </div>
                <div class="info-item">
                    <div>Port Balance</div>
                    <div class="info-value status-good" id="port-balance-status">Checking...</div>
                </div>
            </div>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="area-header" style="color: #9C27B0;">♾️ Infinite Node Pool</div>
                <canvas id="temporaryCanvas" width="280" height="400"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="area-header" style="color: #00aaff;">🏗️ Construction Blueprint</div>
                <canvas id="gameCanvas" width="680" height="400"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn success" onclick="validateCurrentSolution()">✅ Validate & Progress (V)</button>
            <button class="btn" onclick="generateNextRoundNodes()">➕ Add Nodes (N)</button>
            <button class="btn warning" onclick="showCompensationAnalysis()">🔍 Analyze Compensation</button>
            <button class="btn" onclick="showRoundHistory()">📚 Round History</button>
            <button class="btn" onclick="monitorPortBalance()">⚖️ Check Balance</button>
            <button class="btn warning" onclick="correctPortBalance()">🔧 Fix Balance</button>
            <button class="btn info" onclick="forceWaveProgress()">🌊 Force Wave Progress</button>
            <button class="btn danger" onclick="resetInfiniteMode()">🔄 Reset (R)</button>
        </div>
        
        <div class="validation-status" id="validation-status">
            <h3>🎯 Validation Status</h3>
            <div id="validation-message">Ready for validation</div>
        </div>
        
        <div class="game-loop-panel">
            <h3>🔄 Game Loop Phases</h3>
            <div class="loop-step active" id="building-phase">
                <h4>1. Building Phase</h4>
                <p>Players connect nodes from temporary pool to placed area. Drag nodes and click ports to create connections.</p>
            </div>
            <div class="loop-step" id="validating-phase">
                <h4>2. Validation Phase</h4>
                <p>System validates all connections are complete and correct. All ports must be connected with matching types.</p>
            </div>
            <div class="loop-step" id="modifying-phase">
                <h4>3. Modification Phase</h4>
                <p>Random modifications applied: add/remove nodes/ports, change node types. Compensation algorithm ensures solvability.</p>
            </div>
        </div>
        
        <div class="compensation-analysis">
            <h3>🧮 Compensation Algorithm Analysis</h3>
            <div class="analysis-grid">
                <div class="analysis-item">
                    <h4>Port Balance</h4>
                    <div id="port-balance-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Node Generation</h4>
                    <div id="node-generation-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Depth Distribution</h4>
                    <div id="depth-distribution-analysis">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="round-history">
            <h3>📚 Round History</h3>
            <div id="round-history-content">
                No rounds completed yet.
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 Infinite Construction Instructions</h3>
            <ul>
                <li><strong>Building Phase:</strong> Drag nodes from temporary pool to blueprint area, connect matching ports</li>
                <li><strong>Validation:</strong> Press V or click Validate to check your solution and progress to next round</li>
                <li><strong>Automatic Modifications:</strong> System randomly modifies existing nodes/ports between rounds</li>
                <li><strong>Compensation Algorithm:</strong> Ensures every round remains solvable through mathematical balancing</li>
                <li><strong>Infinite Progression:</strong> Each round builds upon previous results with increasing complexity</li>
                <li><strong>Visual Indicators:</strong> Green nodes = compensation nodes, Orange nodes = emergency fixes</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🔬 Algorithm Features</h3>
            <ul>
                <li><strong>Port Type Balance:</strong> ∀τ: |Out_τ| = |In_τ| maintained automatically</li>
                <li><strong>Depth Constraints:</strong> DAG topology preserved through depth assignment</li>
                <li><strong>Bridge Nodes:</strong> Automatically generated to balance port type imbalances</li>
                <li><strong>Emergency Fixes:</strong> Fallback mechanisms ensure 100% solvability</li>
                <li><strong>Dynamic Modifications:</strong> Supports add/remove nodes/ports while maintaining solvability</li>
                <li><strong>Mathematical Proof:</strong> Algorithm guarantees solvable output based on compensation theory</li>
            </ul>
        </div>
        
        <div>
            <h3>📋 Infinite Construction Log</h3>
            <div class="log" id="infinite-log"></div>
        </div>
    </div>

    <!-- Load scripts in dependency order -->
    <script src="compensation_algorithm.js"></script>
    <script src="mode3_infinite_construction.js"></script>
    
    <script>
        // Logging system
        function logInfinite(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('infinite-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[INFINITE] ${message}`);
        }
        
        // UI update functions
        function updateInfiniteStatus() {
            if (typeof mode3InfiniteState !== 'undefined') {
                document.getElementById('current-round').textContent = mode3InfiniteState.currentRound;
                document.getElementById('temp-nodes').textContent = mode3InfiniteState.temporaryNodes.length;
                document.getElementById('placed-nodes').textContent = mode3InfiniteState.placedNodes.length;
                document.getElementById('connections').textContent = mode3InfiniteState.connections.length;
                document.getElementById('modifications').textContent = mode3InfiniteState.modificationHistory.length;
                
                // Update game phase
                const phaseElement = document.getElementById('game-phase');
                phaseElement.textContent = mode3InfiniteState.gamePhase.charAt(0).toUpperCase() + mode3InfiniteState.gamePhase.slice(1);
                phaseElement.className = `info-value status-${mode3InfiniteState.gamePhase}`;
                
                // Update phase indicators
                document.querySelectorAll('.loop-step').forEach(step => step.classList.remove('active'));
                document.getElementById(`${mode3InfiniteState.gamePhase}-phase`).classList.add('active');
            }
        }
        
        function updateCompensationAnalysis() {
            if (typeof mode3InfiniteState !== 'undefined' && mode3InfiniteState.compensationAlgorithm) {
                const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
                
                // Port balance analysis
                const portBalance = mode3InfiniteState.compensationAlgorithm.calculatePortBalance(allNodes);
                const balanceItems = [];
                portBalance.forEach((stat, portType) => {
                    const balanceClass = stat.delta === 0 ? 'status-good' : 'status-warning';
                    balanceItems.push(`<div><span class="${balanceClass}">${portType}</span>: ${stat.inputs} in, ${stat.outputs} out (Δ${stat.delta})</div>`);
                });
                document.getElementById('port-balance-analysis').innerHTML = balanceItems.join('');
                
                // Node generation analysis
                const nodesByCreator = { original: 0, compensation: 0, emergency_fix: 0 };
                allNodes.forEach(node => {
                    nodesByCreator[node.createdBy || 'original']++;
                });
                
                document.getElementById('node-generation-analysis').innerHTML = `
                    <div>Original: ${nodesByCreator.original}</div>
                    <div>Compensation: ${nodesByCreator.compensation}</div>
                    <div>Emergency Fix: ${nodesByCreator.emergency_fix}</div>
                    <div>Total: ${allNodes.length}</div>
                `;
                
                // Depth distribution analysis
                const depthDistribution = new Map();
                allNodes.forEach(node => {
                    const depth = node.depth || 0;
                    depthDistribution.set(depth, (depthDistribution.get(depth) || 0) + 1);
                });
                
                const depthItems = Array.from(depthDistribution.entries())
                    .sort((a, b) => a[0] - b[0])
                    .map(([depth, count]) => `<div>Depth ${depth}: ${count} nodes</div>`);
                
                document.getElementById('depth-distribution-analysis').innerHTML = depthItems.join('');
            }
        }
        
        function updateRoundHistory() {
            if (typeof mode3InfiniteState !== 'undefined') {
                const history = mode3InfiniteState.roundHistory;
                
                if (history.length === 0) {
                    document.getElementById('round-history-content').innerHTML = 'No rounds completed yet.';
                    return;
                }
                
                const historyItems = history.slice(-5).map(record => {
                    const timestamp = new Date(record.timestamp).toLocaleTimeString();
                    return `<div class="history-item">
                        Round ${record.round} (${record.phase}): ${record.nodeCount} nodes, ${record.connections} connections
                        <br><small>${timestamp} - ${record.modifications.length} modifications</small>
                    </div>`;
                });
                
                document.getElementById('round-history-content').innerHTML = historyItems.join('');
            }
        }
        
        // Analysis functions
        function showCompensationAnalysis() {
            if (typeof mode3InfiniteState !== 'undefined' && mode3InfiniteState.compensationAlgorithm) {
                const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
                const analysis = mode3InfiniteState.compensationAlgorithm.analyzeGeneration(allNodes);
                
                const report = [
                    '=== Compensation Algorithm Analysis ===',
                    `Total Nodes: ${analysis.totalNodes}`,
                    '',
                    'Nodes by Type:',
                    ...Object.entries(analysis.nodesByType).map(([type, count]) => `  ${type}: ${count}`),
                    '',
                    'Nodes by Creator:',
                    ...Object.entries(analysis.nodesByCreator).map(([creator, count]) => `  ${creator}: ${count}`),
                    '',
                    'Port Type Distribution:',
                    ...Array.from(analysis.portTypeDistribution.entries()).map(([type, count]) => `  ${type}: ${count} ports`),
                    '',
                    'Depth Distribution:',
                    ...Array.from(analysis.depthDistribution.entries()).sort((a, b) => a[0] - b[0]).map(([depth, count]) => `  Depth ${depth}: ${count} nodes`)
                ];
                
                alert(report.join('\n'));
                logInfinite('Compensation analysis displayed', 'info');
            }
        }
        
        function showRoundHistory() {
            if (typeof mode3InfiniteState !== 'undefined') {
                const history = mode3InfiniteState.roundHistory;
                const modifications = mode3InfiniteState.modificationHistory;
                
                const report = [
                    '=== Round History ===',
                    `Current Round: ${mode3InfiniteState.currentRound}`,
                    `Total Rounds: ${history.length}`,
                    `Total Modifications: ${modifications.length}`,
                    '',
                    'Recent Rounds:',
                    ...history.slice(-5).map(record => {
                        const timestamp = new Date(record.timestamp).toLocaleString();
                        return `Round ${record.round} (${record.phase}): ${record.nodeCount} nodes, ${record.connections} connections - ${timestamp}`;
                    })
                ];
                
                alert(report.join('\n'));
                logInfinite('Round history displayed', 'info');
            }
        }
        
        // Auto-update functions
        setInterval(updateInfiniteStatus, 1000);
        setInterval(updateCompensationAnalysis, 2000);
        setInterval(updateRoundHistory, 3000);
        
        // Initial setup
        setTimeout(() => {
            updateInfiniteStatus();
            updateCompensationAnalysis();
            updateRoundHistory();
            logInfinite('Infinite construction test framework loaded', 'success');
            
            if (typeof mode3InfiniteState !== 'undefined') {
                logInfinite('Mode 3 infinite state initialized', 'success');
                
                if (mode3InfiniteState.compensationAlgorithm) {
                    logInfinite('Compensation algorithm loaded and ready', 'success');
                } else {
                    logInfinite('Compensation algorithm not found', 'error');
                }
            } else {
                logInfinite('Mode 3 infinite state not found', 'error');
            }
        }, 1000);
        
        // Demonstrate the game loop
        setTimeout(() => {
            logInfinite('=== INFINITE CONSTRUCTION GAME LOOP ===', 'info');
            logInfinite('1. Building Phase: Connect nodes and ports', 'info');
            logInfinite('2. Validation Phase: Verify solution completeness', 'info');
            logInfinite('3. Modification Phase: Apply random changes', 'info');
            logInfinite('4. Compensation: Ensure continued solvability', 'success');
            logInfinite('5. Next Round: Repeat with increased complexity', 'info');
        }, 2000);

        // ========== 测试函数 ==========

        function resetInfiniteMode() {
            if (confirm('Reset infinite construction mode? This will clear all progress.')) {
                resetMode3Infinite();
            }
        }

        function forceWaveProgress() {
            console.log('[INFINITE] Force wave progress triggered');

            // 检查当前是否可解
            const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
            const isSolvable = mode3InfiniteState.compensationAlgorithm.validateSolvability(allNodes);

            if (isSolvable) {
                console.log('[INFINITE] Current state is solvable, forcing wave progress');

                // 直接触发修改阶段
                applyRandomModifications();

                // 更新显示
                updateMode3InfiniteDisplay();

                // 显示成功消息
                document.getElementById('validation-message').innerHTML =
                    `✅ Wave ${mode3InfiniteState.currentRound} completed! Progressing to next wave...`;

            } else {
                console.log('[INFINITE] Current state is not solvable, applying emergency fix first');

                // 应用紧急修复
                const fixedNodes = mode3InfiniteState.compensationAlgorithm.emergencyFixWithGameState(
                    allNodes,
                    {
                        temporaryNodes: mode3InfiniteState.temporaryNodes,
                        placedNodes: mode3InfiniteState.placedNodes,
                        connections: mode3InfiniteState.connections
                    }
                );

                // 分离修复后的节点
                const { temporaryNodes, placedNodes } = separateNodesByArea(fixedNodes);
                mode3InfiniteState.temporaryNodes = temporaryNodes;
                mode3InfiniteState.placedNodes = placedNodes;

                // 然后触发波次进度
                setTimeout(() => {
                    applyRandomModifications();
                    updateMode3InfiniteDisplay();
                }, 1000);

                // 显示修复消息
                document.getElementById('validation-message').innerHTML =
                    `🔧 Applied emergency fix, then progressed to wave ${mode3InfiniteState.currentRound}`;
            }
        }
    </script>
</body>
</html>
