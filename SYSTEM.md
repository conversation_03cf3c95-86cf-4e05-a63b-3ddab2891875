我需要基于蓝图连接（类似unreal engine里的蓝图）做一个游戏
Port：不同形状+不同颜色的node端点

Node：左右都可能有一个或多个port，port之间应该用线连接，由玩家拖拽来进行连接，每种port只能连接相同的port，且只能连接不同方向的port（左port应该连右port， input应该接output）
简化 node 
左右port为图形（方形，菱形 三角等）
起点node只有右侧有port
终点node只有左侧port

左侧port为input port
右侧port为 output port

规则：区域上分为
Node 临时区：存放临时的生成的node
node 摆放区：存在起点和终点node，供玩家摆放新node和连接node

Node摆放区默认会有起点 和终点node

可解：
所有node的port需要能够连接起来（input接output）（node需要按depth流动，起点为0，终点为max）有向无环图隐式执行顺序：数据依赖隐含执行顺序（如节点B需要节点A的输出，则A必先于B执行）。
设计一种算法来正确快速的生成node池子（生成随机的n个起点，m个终点，起点只有output port，终点只有input port，每个node的左右两端都可能有1-x个port，需要保证这个池子里上所有的node的port都能够连接，且最终连接出来的图符合执行顺序【前置node的input不能依赖后置node的output，图不可以成环】，可以通过尝试从起点进行流动（播放流动动画，和从起点来进行搜索来提前验证）来进行验证，找出不符合要求的node和连接

交互：
整体交互逻辑类似ue蓝图玩家拖拽来连接output port到 input port，一个port只能连接一个port，如果玩家拖拽连接到了一个已经连接的port，则断开之前的连接（后置连接取代之前的连接），每一个node随时都可以拖动，port连接是从一个port拖动到另一个port的范围之后松开即为连接
<mode 1>
模式设计1（俄罗斯方块模式）
限制时间
类似俄罗斯方块 逐渐生成node
限制暂停区的node数量
生成的过程需要保证整体生成的node池子可解
俄罗斯模式需要一个可解的整体池子 每关限制生成的块数 逐渐增加
</mode 1>
<mode 2>
模式设计2（难度上升模式）
不限制时间
一个回合接一个回合
难度逐渐上升，每个回合增加总的node和port数，每个回合的内容要不同并逐渐提高难度
</mode 2>
<mode 3>
模式设计3（无限构筑模式）
不限制时间，与模式2不同
波次之间不应该有时间限制，而是通过验证的形式进行到下一关
一个回合接一个回合
后续回合要在前面回合的连接好的结果基础上构建
之前做好的（连接好的）结果可能会出现变化（增删port，增删node，增加begin，end等），由此产生的不合法连接自动删除并提示
逐渐拓展蓝图的大小，逐渐构筑一个很大的 很复杂的蓝图 
在每次修改前和修改后都应该可解，每个波次自身都需要可解
不能只是增加port的形式，port的增加需要分配到不同的node上，
可以增加起点和终点
每个回合生成m个起点，n个终点
game loop：
1.生成node pool(包含起点终点)，保证临时区和摆放区的node pool整体可解
2.玩家进行连接
3.玩家点击播放流动/验证
4.验证通过后过关，随机修改已经连接好的node pool (增删node，增删port，增删起点终点)
5.回到1，以修改后的node pool为基础生成新的总体的node pool，并保证摆放区和临时区的node pool作为一个整体可解 (这里可能需要算法能够以部分node为基础进行生成，而不是从头进行生成)

注意：增删的过程不应该只是增加port，也可以出现增删node的情况，尽量避免port都加到同一个node上（限制单个node的单边最大port数量，或者是增加port时各个node有不同的权重），增删port和增删node都应该随机的出现且互不影响，这个过程为一个单独的过程互不相关

算法思路层面： 
可以通过先生产平衡的port数，再在node之间分配这些port来保证生成的随机性和可解性
分配过程中可能会随机产生起点node和终点node，起点和终点node的数量也应该在level增加的过程中逐渐增加随机数量的范围（例如第一关 1个起点1个终点，第二关 1-2个起点和终点，第三关 2-3个起点和终点，以此类推）


node的深度不应该是固定的（只是用于算法生成的参考逻辑，并不限制玩家连接） 毕竟按照每回合逐渐增删改，层级关系可能随时变动

是需要在这个过程中

增删改已有的（已连接）的node

然后根据已有node重新生成池子

但是要保持随机性 不是按照一个node一个node来生成 而是作为整体来生成

意味着算法需要支持从已有池子生成而不是从零生成

也就是说每种port的连接的唯一解都不能成环 

即成环的考虑要每种port单独考虑 而不是node作为整体考虑
注意 solvable shall always be true along the game process
</mode 3>



<define>
### 拓展后的可解定义（算法题级别）

#### 问题描述
给定一个节点池，包含：
- **起点集合** \( S = \{s_1, s_2, \dots, s_n\} \): 每个起点 \( s_i \) 有且仅有 **输出端口**（右侧端口）。
- **终点集合** \( T = \{t_1, t_2, \dots, t_m\} \): 每个终点 \( t_j \) 有且仅有 **输入端口**（左侧端口）。
- **中间节点集合** \( N = \{n_1, n_2, \dots, n_k\} \): 每个中间节点 \( n_p \) 有 **输入端口**（左侧）和 **输出端口**（右侧），数量均 \(\geq 1\)。

每个端口有唯一类型 \( \tau = (\text{形状}, \text{颜色}) \)。要求验证是否存在一个 **连接方案**，满足以下条件：

---

#### 约束条件
1. **端口连接规则**  
   - 每个连接必须是一个 **输出端口** 连接到一个 **输入端口**（方向匹配）。
   - 连接的端口必须 **类型相同**（相同形状和颜色）。
   - 每个端口最多参与一个连接（一一对应）。

2. **拓扑规则**  
   - **有向无环图 (DAG)**：连接后形成的图 \( G = (V, E) \) 必须是无环有向图，其中：
     - 节点集 \( V = S \cup N \cup T \)。
     - 边集 \( E \) 表示端口连接关系（从输出端口指向输入端口）。
   - **起点与终点的角色**：
     - 起点只有出边（无入边）。
     - 终点只有入边（无出边）。
     - 中间节点既有入边也有出边（至少一条入边和一条出边）。
   - **连通性**：每个节点必须位于至少一条 **有效路径** 上，即：
     - 从某个起点出发，经过若干中间节点，到达某个终点。

3. **端口覆盖性**  
   所有端口必须被完全连接（无孤立端口）。

---

#### 可解性验证
节点池可解 **当且仅当** 存在一个连接方案满足以下算法条件：

1. **类型匹配约束**  
   定义端口类型集合 \( \Gamma \)。对每个类型 \( \tau \in \Gamma \)：
   - 输出端口数（起点 + 中间节点的输出端口） = 输入端口数（中间节点 + 终点的输入端口）。

2. **拓扑可行性（DAG 构造）**  
   - **层分配**：将节点分配到层级 \( L \) 上：
     - \( L_0 \)：起点集合 \( S \)（深度 0）。
     - \( L_d \)：中间节点（深度 \( d \geq 1 \))。
     - \( L_{\text{max}} \)：终点集合 \( T \)（最大深度）。
   - **无环约束**：若存在边 \( (u, v) \)，则 \( \text{depth}(u) < \text{depth}(v) \)。
   - **层级连通性**：
     - 每个深度 \( d \geq 1 \) 的节点必须有至少一个前驱在深度 \( d-1 \)。
     - 每个深度 \( d \leq \text{max}-1 \) 的节点必须有至少一个后继在深度 \( d+1 \).

3. **端口流守恒**  
   对每个节点 \( v \in V \)：
   - 若 \( v \in S \): 输出端口数 = 出边数。
   - 若 \( v \in T \): 输入端口数 = 入边数。
   - 若 \( v \in N \): 输入端口数 = 入边数，输出端口数 = 出边数。

4. **存在性证明**  
   存在一个 **双向映射函数** \( \Phi \):
   - \( \Phi: \text{输出端口} \to \text{输入端口} \)，满足：
     - 类型相同：\( \tau(\text{out\_port}) = \tau(\text{in\_port}) \).
     - 方向有效：若 \( \text{out\_port} \) 属于节点 \( u \)，\( \text{in\_port} \) 属于节点 \( v \)，则 \( \text{depth}(u) < \text{depth}(v) \).

---

#### 生成算法要求
生成节点池时，需保证：
1. **层分配与端口流守恒**  
   - 显式分配每个节点的深度（确保起点深度最小，终点深度最大）。
   - 按深度分配端口：节点在深度 \( d \) 的输出端口只能连接深度 \( >d \) 的输入端口。
2. **类型平衡**  
   - 对每个类型 \( \tau \)，随机生成相同数量的输出端口和输入端口。
3. **无环保证**  
   - 连接仅在深度递增的节点间发生（禁止跨层反向连接）。
4. **复杂度**  
   - 时间复杂度：\( O(|V| + |E|) \)（通过层级遍历验证）。
   - 空间复杂度：\( O(|\Gamma|) \)（存储端口类型计数）。

---

### 总结
节点池 **可解** 的充要条件是：
- **端口类型平衡** + **层级化 DAG 结构** + **深度约束的连接映射**。  
生成算法必须显式构建层级结构并分配端口类型，确保以上条件成立。任何玩家连接方案均需遵守此规则，否则视为非法（自动断开并提示）。

</define>


node的深度不应该是固定的 毕竟按照每回合逐渐增删改，层级关系可能随时变动

是需要在这个过程中

增删改已有的（已连接）的node

然后根据已有node重新生成池子

但是要保持随机性 不是按照一个node一个node来生成 而是作为整体来生成

意味着算法需要支持从已有池子生成而不是从零生成

也就是说每种port的连接的唯一解都不能成环 

即成环的考虑要每种port单独考虑 而不是node作为整体考虑

考虑只有一种port 
nodeA 左1 右1
nodeB 左1 右2
nodeC 左1

虽然他们平衡 但是他们要连接到一起一定会成环，不满足条件
思考 如何让每种port保证不成环（DAG）
问题的症结并不在层级上
因为不成环的前提还需要所有的port相连
也就是说在保证平衡的基础上
分配port的时候需要将port分配在能够形成DAG的node路径上
比如只有两个port分配在同一个node两端就不合法
也就是说node只是一种表现形式（实际上是不同层级的拆分单元），即node是层级拆分的表现形式
（也就是说生成的时候假设不拆分node，同层级都在一个node上，然后再进行拆分）
且两个port要相互连接 他们就不能在同一个层级上（成环式连接或者自身成环连接）
实际上是将port分布在不同层级来保证各种port的DAG和可全部连通属性
将port分布在同层级的哪个node上可能无所谓？
仔细思考上述说法
也就是说 生成port的过程需要成对生成，假如在层级0上生成了一个output，那一定要在层级1+上生成一个input，以此类推 来保证port成对生成来保证可解


每个回合 按照连接好的node重新分配层次（从起点开始标记层级，每个node的层级为max（depth）） 来增删改port 重新分配port
当一个port需要被增删改时 需要成对的补偿这个修改， 就像每个port要成对存在一样
然后再随机添加一些新的port种类（当然也是要成对添加）

mode3中生成了大量的单port的node，这不是预期之中的 同层级应该进行组合 来避免大量的单port的node
而不是将补充的port都变成单一node
应该是拆分再组合再拆分的过程
node->layer->node->layer

或者说对于所有port
如果是output port 则 存在 >=depth+1的input port
如果是input port 则 存在 <=depth-1的output port
否则进行补充

