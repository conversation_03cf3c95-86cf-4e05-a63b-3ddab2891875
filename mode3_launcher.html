<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Test Launcher</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: 'Courier New', monospace;
            background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
            color: #00ff00;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .launcher-container {
            max-width: 800px;
            padding: 40px;
            background: rgba(42, 42, 42, 0.9);
            border-radius: 20px;
            border: 2px solid #00ff00;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.3);
            text-align: center;
        }
        
        .header {
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 0 0 10px #00ff00;
            animation: glow 2s ease-in-out infinite alternate;
        }
        
        @keyframes glow {
            from { text-shadow: 0 0 10px #00ff00; }
            to { text-shadow: 0 0 20px #00ff00, 0 0 30px #00ff00; }
        }
        
        .subtitle {
            font-size: 1.2em;
            color: #88ff88;
            margin-top: 10px;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }
        
        .test-card {
            background: #333;
            border: 2px solid #555;
            border-radius: 15px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        
        .test-card:hover {
            border-color: #00ff00;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 255, 0, 0.2);
        }
        
        .test-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 0, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .test-card:hover::before {
            left: 100%;
        }
        
        .test-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .test-title {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 10px;
            color: #00aaff;
        }
        
        .test-description {
            font-size: 0.9em;
            color: #aaa;
            line-height: 1.4;
            margin-bottom: 15px;
        }
        
        .test-features {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        
        .test-features li {
            font-size: 0.8em;
            color: #888;
            margin: 5px 0;
            padding-left: 15px;
            position: relative;
        }
        
        .test-features li::before {
            content: '▶';
            position: absolute;
            left: 0;
            color: #00ff00;
        }
        
        .launch-all {
            margin-top: 30px;
            padding: 15px 30px;
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1em;
            font-family: 'Courier New', monospace;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .launch-all:hover {
            background: linear-gradient(45deg, #45a049, #4CAF50);
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            background: #1a1a3a;
            border-radius: 5px;
            font-size: 0.9em;
            color: #aaa;
        }
        
        .footer {
            margin-top: 30px;
            font-size: 0.8em;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="launcher-container">
        <div class="header">
            <h1>♾️ Mode 3 Test Launcher</h1>
            <div class="subtitle">Blueprint Connection Game - Infinite Construction Mode</div>
        </div>
        
        <div class="test-grid">
            <div class="test-card" onclick="launchTest('mode3_infinite_test.html')">
                <div class="test-icon">🎮</div>
                <div class="test-title">Infinite Construction</div>
                <div class="test-description">完整的Mode 3无限构筑模式游戏体验</div>
                <ul class="test-features">
                    <li>无限轮次进度</li>
                    <li>动态节点生成</li>
                    <li>自动端口平衡</li>
                    <li>实时修改和补偿</li>
                </ul>
            </div>
            
            <div class="test-card" onclick="launchTest('test_system_md_compliance.html')">
                <div class="test-icon">📋</div>
                <div class="test-title">SYSTEM.md Compliance</div>
                <div class="test-description">验证算法是否严格遵循SYSTEM.md约束</div>
                <ul class="test-features">
                    <li>端口平衡约束验证</li>
                    <li>拓扑结构检查</li>
                    <li>深度约束验证</li>
                    <li>自动化测试套件</li>
                </ul>
            </div>
            
            <div class="test-card" onclick="launchTest('mode3_conflict_free_test.html')">
                <div class="test-icon">🛡️</div>
                <div class="test-title">Conflict Resolution</div>
                <div class="test-description">端口DAG冲突解决和自环预防测试</div>
                <ul class="test-features">
                    <li>零冲突节点生成</li>
                    <li>虚拟-物理分离</li>
                    <li>自环风险检测</li>
                    <li>智能冲突解决</li>
                </ul>
            </div>
            
            <div class="test-card" onclick="launchTest('hierarchical_compensation_test.html')">
                <div class="test-icon">🏗️</div>
                <div class="test-title">Hierarchical Compensation</div>
                <div class="test-description">分层补偿算法 - 类型隔离验证 - 全局拓扑序</div>
                <ul class="test-features">
                    <li>分层补偿系统</li>
                    <li>类型隔离验证</li>
                    <li>全局拓扑序维护</li>
                    <li>动态难度调整</li>
                </ul>
            </div>
        </div>
        
        <button class="launch-all" onclick="launchAllTests()">
            🚀 Launch All Tests
        </button>
        
        <div class="status" id="status">
            Ready to launch Mode 3 tests. Click on any test card or launch all tests.
        </div>
        
        <div class="footer">
            <p>🎯 <strong>Testing Instructions:</strong></p>
            <p>• Drag nodes from temporary pool to construction area</p>
            <p>• Click ports to create connections (matching types only)</p>
            <p>• Press 'V' to validate solution and progress to next round</p>
            <p>• Monitor logs and statistics for detailed algorithm behavior</p>
        </div>
    </div>

    <script>
        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('status');
            statusElement.textContent = message;
            
            // Add visual feedback
            statusElement.style.background = type === 'success' ? '#1a3a1a' : 
                                           type === 'error' ? '#3a1a1a' : '#1a1a3a';
            statusElement.style.borderLeft = type === 'success' ? '4px solid #4CAF50' : 
                                           type === 'error' ? '4px solid #f44336' : '4px solid #2196F3';
        }
        
        function launchTest(testFile) {
            updateStatus(`Launching ${testFile}...`, 'info');
            
            try {
                window.open(testFile, '_blank');
                updateStatus(`✅ Successfully launched ${testFile}`, 'success');
                
                // Reset status after 3 seconds
                setTimeout(() => {
                    updateStatus('Ready to launch more tests.');
                }, 3000);
                
            } catch (error) {
                updateStatus(`❌ Failed to launch ${testFile}: ${error.message}`, 'error');
            }
        }
        
        function launchAllTests() {
            updateStatus('Launching all Mode 3 tests...', 'info');
            
            const tests = [
                'mode3_infinite_test.html',
                'test_system_md_compliance.html',
                'mode3_conflict_free_test.html',
                'hierarchical_compensation_test.html'
            ];
            
            let launched = 0;
            
            tests.forEach((test, index) => {
                setTimeout(() => {
                    try {
                        window.open(test, '_blank');
                        launched++;
                        updateStatus(`Launched ${launched}/${tests.length} tests...`, 'info');
                        
                        if (launched === tests.length) {
                            setTimeout(() => {
                                updateStatus(`🎉 Successfully launched all ${tests.length} Mode 3 tests!`, 'success');
                            }, 500);
                        }
                    } catch (error) {
                        updateStatus(`❌ Failed to launch ${test}: ${error.message}`, 'error');
                    }
                }, index * 1000); // Stagger launches by 1 second
            });
        }
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case '1':
                    launchTest('mode3_infinite_test.html');
                    break;
                case '2':
                    launchTest('test_system_md_compliance.html');
                    break;
                case '3':
                    launchTest('mode3_conflict_free_test.html');
                    break;
                case '4':
                    launchTest('hierarchical_compensation_test.html');
                    break;
                case 'a':
                case 'A':
                    launchAllTests();
                    break;
            }
        });
        
        // Add hover effects
        document.querySelectorAll('.test-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                const title = this.querySelector('.test-title').textContent;
                updateStatus(`Hover: ${title} - Click to launch`, 'info');
            });
            
            card.addEventListener('mouseleave', function() {
                updateStatus('Ready to launch Mode 3 tests.');
            });
        });
        
        // Initialize
        updateStatus('Mode 3 Test Launcher initialized. Use keyboard shortcuts 1-4 or A for all tests.');
    </script>
</body>
</html>
