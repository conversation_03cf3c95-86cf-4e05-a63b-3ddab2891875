<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证层级DAG算法</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-section {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 层级DAG算法验证</h1>
        
        <div class="test-section">
            <h3>快速验证测试</h3>
            <button onclick="runQuickTest()">🚀 运行快速测试</button>
            <button onclick="testSingleLevel()">🎯 测试单关卡</button>
            <button onclick="clearOutput()">🗑️ 清空输出</button>
        </div>
        
        <div class="test-output" id="testOutput">
            <div class="info">准备运行层级DAG算法验证...</div>
        </div>
    </div>

    <!-- 加载游戏代码 -->
    <script src="game.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type;
            
            const div = document.createElement('div');
            div.className = colorClass;
            div.textContent = `[${timestamp}] ${message}`;
            output.appendChild(div);
            
            // 自动滚动到底部
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
        }
        
        function runQuickTest() {
            clearOutput();
            log('Start quick verification of layered DAG algorithm...', 'info');

            try {
                // 测试算法函数是否存在
                if (typeof generateDeterministicSolvableScenario === 'undefined') {
                    log('ERROR: generateDeterministicSolvableScenario function not found', 'error');
                    return;
                }

                log('SUCCESS: Algorithm function loaded', 'success');
                
                // 测试基本难度配置
                const testDifficulty = {
                    level: 3,
                    nodes: 4,
                    chains: 3,
                    types: 2,
                    colors: 2,
                    duplicateNodes: false,
                    maxPortsPerNode: 3,
                    availableTypes: ['square', 'circle', 'triangle', 'diamond'],
                    availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
                    multipleSolutions: true
                };
                
                log('Generating test scenario...', 'info');

                // 生成场景
                const scenario = generateDeterministicSolvableScenario(testDifficulty);

                if (!scenario) {
                    log('ERROR: Scenario generation failed', 'error');
                    return;
                }

                log('SUCCESS: Scenario generated successfully', 'success');
                
                // 验证基本结构
                const hasStartNode = scenario.startNode && scenario.startNode.type === 'start';
                const hasEndNode = scenario.endNode && scenario.endNode.type === 'end';
                const hasNodes = scenario.nodes && Array.isArray(scenario.nodes);
                
                log(`✅ 起点节点: ${hasStartNode ? '存在' : '缺失'}`, hasStartNode ? 'success' : 'error');
                log(`✅ 终点节点: ${hasEndNode ? '存在' : '缺失'}`, hasEndNode ? 'success' : 'error');
                log(`✅ 中间节点: ${hasNodes ? scenario.nodes.length + '个' : '缺失'}`, hasNodes ? 'success' : 'error');
                
                // 验证深度分配
                if (hasStartNode && hasEndNode) {
                    const startDepth = scenario.startNode.depth;
                    const endDepth = scenario.endNode.depth;
                    
                    log(`📊 起点深度: ${startDepth}`, startDepth === 0 ? 'success' : 'error');
                    log(`📊 终点深度: ${endDepth}`, endDepth > 0 ? 'success' : 'error');
                    
                    if (hasNodes) {
                        const intermediateDepths = scenario.nodes.map(n => n.depth);
                        log(`📊 中间节点深度: [${intermediateDepths.join(', ')}]`, 'info');
                        
                        // 验证深度递增
                        const depthsValid = intermediateDepths.every(d => d > 0 && d < endDepth);
                        log(`✅ 深度约束: ${depthsValid ? '满足' : '违反'}`, depthsValid ? 'success' : 'error');
                    }
                }
                
                // 验证端口
                if (hasStartNode) {
                    const startOutputs = scenario.startNode.outputPorts?.length || 0;
                    const startInputs = scenario.startNode.inputPorts?.length || 0;
                    log(`🔌 起点端口: ${startOutputs}输出, ${startInputs}输入`, startOutputs > 0 && startInputs === 0 ? 'success' : 'error');
                }
                
                if (hasEndNode) {
                    const endInputs = scenario.endNode.inputPorts?.length || 0;
                    const endOutputs = scenario.endNode.outputPorts?.length || 0;
                    log(`🔌 终点端口: ${endInputs}输入, ${endOutputs}输出`, endInputs > 0 && endOutputs === 0 ? 'success' : 'error');
                }
                
                // 验证连接
                if (scenario.guaranteedConnections) {
                    const connectionCount = scenario.guaranteedConnections.length;
                    log(`🔗 保证连接: ${connectionCount}个`, connectionCount > 0 ? 'success' : 'warning');
                    
                    // 验证连接的深度约束
                    let validConnections = 0;
                    scenario.guaranteedConnections.forEach((conn, i) => {
                        if (conn.sourceDepth < conn.targetDepth) {
                            validConnections++;
                        } else {
                            log(`❌ 连接${i+1}违反深度约束: ${conn.sourceDepth} → ${conn.targetDepth}`, 'error');
                        }
                    });
                    
                    log(`✅ 有效连接: ${validConnections}/${connectionCount}`, validConnections === connectionCount ? 'success' : 'error');
                }
                
                log('🎉 快速验证完成！', 'success');
                
            } catch (error) {
                log(`💥 测试失败: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        function testSingleLevel() {
            clearOutput();
            log('🎯 测试单关卡生成...', 'info');
            
            try {
                for (let level = 1; level <= 5; level++) {
                    log(`\n--- 测试关卡 ${level} ---`, 'info');
                    
                    const difficulty = {
                        level: level,
                        nodes: level + 1,
                        chains: level,
                        types: Math.min(level + 1, 3),
                        colors: Math.min(level + 1, 3),
                        duplicateNodes: level > 3,
                        maxPortsPerNode: Math.min(level + 1, 4),
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4)),
                        multipleSolutions: level > 2
                    };
                    
                    const scenario = generateDeterministicSolvableScenario(difficulty);
                    
                    if (scenario) {
                        const nodeCount = scenario.nodes ? scenario.nodes.length : 0;
                        const connectionCount = scenario.guaranteedConnections ? scenario.guaranteedConnections.length : 0;
                        log(`✅ 关卡${level}: ${nodeCount}节点, ${connectionCount}连接`, 'success');
                    } else {
                        log(`❌ 关卡${level}: 生成失败`, 'error');
                    }
                }
                
                log('\n🎉 单关卡测试完成！', 'success');
                
            } catch (error) {
                log(`💥 单关卡测试失败: ${error.message}`, 'error');
                console.error(error);
            }
        }
        
        // 页面加载完成后自动运行一次快速测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('页面加载完成，可以开始测试', 'info');
            }, 1000);
        });
    </script>
</body>
</html>
