<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automated Flow-First Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 1400px;
            margin: 0 auto;
        }
        .status-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
        .results-panel {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .metrics-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .metric-box {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .metric-value {
            font-size: 28px;
            font-weight: bold;
            margin: 10px 0;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
        .status-indicator {
            display: inline-block;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .status-running { background: #ffaa00; animation: pulse 1s infinite; }
        .status-success { background: #00ff00; }
        .status-error { background: #ff0000; }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 Automated Flow-First Algorithm Testing</h1>
        
        <div class="status-panel">
            <h2>
                <span class="status-indicator" id="status-indicator"></span>
                <span id="test-status">Initializing...</span>
            </h2>
            <p id="test-progress">Waiting for game script to load...</p>
        </div>

        <div class="metrics-summary" id="metrics-summary" style="display: none;">
            <div class="metric-box">
                <div>Overall Pass Rate</div>
                <div class="metric-value" id="overall-pass-rate">0%</div>
                <div>Target: ≥90%</div>
            </div>
            <div class="metric-box">
                <div>SYSTEM.md Compliance</div>
                <div class="metric-value" id="systemmd-compliance">0%</div>
                <div>Target: ≥95%</div>
            </div>
            <div class="metric-box">
                <div>Port Mapping Success</div>
                <div class="metric-value" id="port-mapping-rate">0%</div>
                <div>Target: ≥95%</div>
            </div>
            <div class="metric-box">
                <div>Average Execution Time</div>
                <div class="metric-value" id="avg-execution-time">0ms</div>
                <div>Target: ≤1000ms</div>
            </div>
        </div>

        <div class="results-panel" id="results-output"></div>
    </div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[AUTO-TEST] Game script loaded successfully');
            updateStatus('running', 'Game script loaded, starting tests...');
            setTimeout(runAutomatedTests, 2000);
        };
        script.onerror = function() {
            console.error('[AUTO-TEST] Failed to load game script');
            updateStatus('error', 'Failed to load game script');
        };
        document.head.appendChild(script);

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const resultsOutput = document.getElementById('results-output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            const className = type === 'error' ? 'fail' : 
                             (message.includes('SUCCESS') || message.includes('PASS')) ? 'pass' : 
                             (message.includes('FAIL') || message.includes('ERROR')) ? 'fail' : '';
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            resultsOutput.innerHTML += formattedMessage;
            resultsOutput.scrollTop = resultsOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        function updateStatus(status, message) {
            const indicator = document.getElementById('status-indicator');
            const statusText = document.getElementById('test-status');
            const progressText = document.getElementById('test-progress');
            
            indicator.className = `status-indicator status-${status}`;
            statusText.textContent = message;
            
            if (status === 'running') {
                progressText.textContent = 'Running comprehensive analysis...';
            } else if (status === 'success') {
                progressText.textContent = 'All tests completed successfully!';
            } else if (status === 'error') {
                progressText.textContent = 'Test execution encountered errors.';
            }
        }

        function updateMetrics(analysis) {
            if (!analysis || !analysis.summary) return;
            
            document.getElementById('metrics-summary').style.display = 'grid';
            
            // Overall pass rate
            const passRate = (analysis.summary.passRate * 100).toFixed(1);
            document.getElementById('overall-pass-rate').textContent = `${passRate}%`;
            document.getElementById('overall-pass-rate').className = `metric-value ${passRate >= 90 ? 'pass' : 'fail'}`;
            
            // SYSTEM.md compliance
            const complianceRate = (analysis.summary.systemMdCompliance * 100).toFixed(1);
            document.getElementById('systemmd-compliance').textContent = `${complianceRate}%`;
            document.getElementById('systemmd-compliance').className = `metric-value ${complianceRate >= 95 ? 'pass' : 'fail'}`;
            
            // Port mapping success (extract from comprehensive test)
            let portMappingRate = '0.0';
            if (analysis.comprehensiveTest && analysis.comprehensiveTest.systemMdCriteria && analysis.comprehensiveTest.systemMdCriteria.portMapping) {
                const stats = analysis.comprehensiveTest.systemMdCriteria.portMapping;
                const total = stats.passed + stats.failed;
                portMappingRate = total > 0 ? (stats.passed / total * 100).toFixed(1) : '0.0';
            }
            document.getElementById('port-mapping-rate').textContent = `${portMappingRate}%`;
            document.getElementById('port-mapping-rate').className = `metric-value ${portMappingRate >= 95 ? 'pass' : 'fail'}`;
            
            // Average execution time
            const avgTime = analysis.summary.performanceMetrics.averageTime.toFixed(0);
            document.getElementById('avg-execution-time').textContent = `${avgTime}ms`;
            document.getElementById('avg-execution-time').className = `metric-value ${avgTime <= 1000 ? 'pass' : 'warn'}`;
        }

        async function runAutomatedTests() {
            try {
                updateStatus('running', 'Running Automated Tests...');
                
                console.log('[AUTO-TEST] Starting automated Flow-First algorithm testing...');
                
                // Wait for game initialization
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Execute comprehensive Flow-First analysis
                if (window.executeFlowFirstAnalysis) {
                    console.log('[AUTO-TEST] Executing comprehensive Flow-First analysis...');
                    const analysis = await window.executeFlowFirstAnalysis();
                    
                    // Update metrics display
                    updateMetrics(analysis);
                    
                    // Determine overall success
                    const success = analysis.summary.overallSuccess;
                    updateStatus(success ? 'success' : 'error', 
                        success ? 'Flow-First Algorithm: All Targets Achieved!' : 'Flow-First Algorithm: Targets Not Met');
                    
                    // Generate summary report
                    generateSummaryReport(analysis);
                    
                } else {
                    throw new Error('executeFlowFirstAnalysis function not available');
                }
                
            } catch (error) {
                console.error('[AUTO-TEST] Automated test execution failed:', error);
                updateStatus('error', 'Automated Test Failed');
                appendOutput(`Automated test failed: ${error.message}`, 'error');
            }
        }

        function generateSummaryReport(analysis) {
            console.log('\n[AUTO-TEST] ========== FLOW-FIRST ALGORITHM SUMMARY REPORT ==========');
            console.log(`[AUTO-TEST] Test Execution Time: ${analysis.timestamp}`);
            console.log(`[AUTO-TEST] Overall Success: ${analysis.summary.overallSuccess ? 'YES' : 'NO'}`);
            console.log(`[AUTO-TEST] Pass Rate: ${(analysis.summary.passRate * 100).toFixed(1)}% (Target: ≥90%)`);
            console.log(`[AUTO-TEST] SYSTEM.md Compliance: ${(analysis.summary.systemMdCompliance * 100).toFixed(1)}% (Target: ≥95%)`);
            console.log(`[AUTO-TEST] Average Execution Time: ${analysis.summary.performanceMetrics.averageTime.toFixed(0)}ms (Target: ≤1000ms)`);
            
            if (analysis.summary.performanceMetrics.memoryLeaks > 0) {
                console.log(`[AUTO-TEST] Memory Leaks Detected: ${analysis.summary.performanceMetrics.memoryLeaks}`);
            }
            
            if (analysis.summary.performanceMetrics.bottlenecks > 0) {
                console.log(`[AUTO-TEST] Performance Bottlenecks: ${analysis.summary.performanceMetrics.bottlenecks}`);
            }
            
            if (analysis.summary.recommendations.length > 0) {
                console.log('[AUTO-TEST] Recommendations for Improvement:');
                analysis.summary.recommendations.forEach((rec, index) => {
                    console.log(`[AUTO-TEST] ${index + 1}. ${rec}`);
                });
            } else {
                console.log('[AUTO-TEST] No recommendations - algorithm performing optimally!');
            }
            
            // Detailed breakdown
            if (analysis.comprehensiveTest && analysis.comprehensiveTest.levels) {
                console.log('\n[AUTO-TEST] Level-by-Level Results:');
                analysis.comprehensiveTest.levels.forEach(level => {
                    const status = level.status === 'PASS' ? 'PASS' : 'FAIL';
                    console.log(`[AUTO-TEST] Level ${level.level}: ${status} (${level.duration.toFixed(0)}ms, ${level.nodeCount} nodes)`);
                });
            }
            
            console.log('[AUTO-TEST] ========== END SUMMARY REPORT ==========\n');
            
            // Store final results
            window.finalFlowFirstResults = analysis;
        }

        // Initialize
        console.log('[AUTO-TEST] Automated Flow-First testing framework initialized');
    </script>
</body>
</html>
