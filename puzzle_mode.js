// ========== Puzzle Mode Implementation ==========

// Global game state for puzzle mode
const puzzleState = {
    currentLevel: 1,
    maxLevel: 10,
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    targetConnections: [],
    
    // Canvas references
    temporaryCanvas: null,
    temporaryCtx: null,
    gameCanvas: null,
    gameCtx: null,
    
    // Interaction state
    draggingNode: null,
    selectedPort: null,
    
    // Game state
    isComplete: false,
    score: 0,
    startTime: null
};

// ========== Level Definitions ==========

const puzzleLevels = {
    1: {
        name: "Basic Connection",
        description: "Connect the square ports",
        temporaryNodes: [
            {
                id: 'temp1',
                type: 'intermediate',
                x: 50, y: 100,
                inputPorts: [{ id: 'temp1_in', type: 'square', color: '#ff5252' }],
                outputPorts: [{ id: 'temp1_out', type: 'square', color: '#ff5252' }]
            }
        ],
        placedNodes: [
            {
                id: 'start1',
                type: 'start',
                x: 100, y: 200,
                inputPorts: [],
                outputPorts: [{ id: 'start1_out', type: 'square', color: '#ff5252' }]
            },
            {
                id: 'end1',
                type: 'end',
                x: 500, y: 200,
                inputPorts: [{ id: 'end1_in', type: 'square', color: '#ff5252' }],
                outputPorts: []
            }
        ],
        targetConnections: [
            { from: 'start1_out', to: 'temp1_in' },
            { from: 'temp1_out', to: 'end1_in' }
        ]
    },
    
    2: {
        name: "Multiple Types",
        description: "Connect different port types",
        temporaryNodes: [
            {
                id: 'temp2a',
                type: 'intermediate',
                x: 50, y: 80,
                inputPorts: [{ id: 'temp2a_in', type: 'square', color: '#ff5252' }],
                outputPorts: [{ id: 'temp2a_out', type: 'circle', color: '#2196F3' }]
            },
            {
                id: 'temp2b',
                type: 'intermediate',
                x: 50, y: 180,
                inputPorts: [{ id: 'temp2b_in', type: 'circle', color: '#2196F3' }],
                outputPorts: [{ id: 'temp2b_out', type: 'triangle', color: '#4CAF50' }]
            }
        ],
        placedNodes: [
            {
                id: 'start2',
                type: 'start',
                x: 100, y: 150,
                inputPorts: [],
                outputPorts: [{ id: 'start2_out', type: 'square', color: '#ff5252' }]
            },
            {
                id: 'end2',
                type: 'end',
                x: 500, y: 150,
                inputPorts: [{ id: 'end2_in', type: 'triangle', color: '#4CAF50' }],
                outputPorts: []
            }
        ],
        targetConnections: [
            { from: 'start2_out', to: 'temp2a_in' },
            { from: 'temp2a_out', to: 'temp2b_in' },
            { from: 'temp2b_out', to: 'end2_in' }
        ]
    }
};

// ========== Initialization ==========

function initializePuzzleMode() {
    console.log('[PUZZLE] Initializing puzzle mode');
    
    // Get canvas elements
    puzzleState.temporaryCanvas = document.getElementById('temporaryCanvas');
    puzzleState.gameCanvas = document.getElementById('gameCanvas');
    
    if (!puzzleState.temporaryCanvas || !puzzleState.gameCanvas) {
        console.error('[PUZZLE] Canvas elements not found');
        return false;
    }
    
    puzzleState.temporaryCtx = puzzleState.temporaryCanvas.getContext('2d');
    puzzleState.gameCtx = puzzleState.gameCanvas.getContext('2d');
    
    // Setup event listeners
    setupPuzzleEventListeners();
    
    // Load first level
    loadLevel(1);
    
    console.log('[PUZZLE] Puzzle mode initialized');
    return true;
}

function setupPuzzleEventListeners() {
    puzzleState.temporaryCanvas.addEventListener('mousedown', handlePuzzleMouseDown);
    puzzleState.temporaryCanvas.addEventListener('mousemove', handlePuzzleMouseMove);
    puzzleState.temporaryCanvas.addEventListener('mouseup', handlePuzzleMouseUp);
    
    puzzleState.gameCanvas.addEventListener('mousedown', handlePuzzleMouseDown);
    puzzleState.gameCanvas.addEventListener('mousemove', handlePuzzleMouseMove);
    puzzleState.gameCanvas.addEventListener('mouseup', handlePuzzleMouseUp);
}

// ========== Level Management ==========

function loadLevel(levelNumber) {
    console.log('[PUZZLE] Loading level', levelNumber);
    
    const level = puzzleLevels[levelNumber];
    if (!level) {
        console.error('[PUZZLE] Level not found:', levelNumber);
        return false;
    }
    
    puzzleState.currentLevel = levelNumber;
    puzzleState.temporaryNodes = JSON.parse(JSON.stringify(level.temporaryNodes));
    puzzleState.placedNodes = JSON.parse(JSON.stringify(level.placedNodes));
    puzzleState.connections = [];
    puzzleState.targetConnections = level.targetConnections;
    puzzleState.isComplete = false;
    puzzleState.startTime = Date.now();
    
    // Position temporary nodes
    positionTemporaryNodes();
    
    updatePuzzleDisplay();
    updateLevelInfo(level);
    
    console.log('[PUZZLE] Level', levelNumber, 'loaded:', level.name);
    return true;
}

function positionTemporaryNodes() {
    const margin = 20;
    const nodeSize = 60;
    const spacing = 80;
    
    puzzleState.temporaryNodes.forEach((node, index) => {
        node.x = margin + nodeSize/2;
        node.y = margin + nodeSize/2 + index * spacing;
    });
}

function checkLevelComplete() {
    if (puzzleState.isComplete) return;
    
    // Check if all target connections are made
    const requiredConnections = puzzleState.targetConnections;
    let completedConnections = 0;
    
    requiredConnections.forEach(target => {
        const exists = puzzleState.connections.some(conn => 
            (conn.fromPort === target.from && conn.toPort === target.to) ||
            (conn.fromPort === target.to && conn.toPort === target.from)
        );
        
        if (exists) {
            completedConnections++;
        }
    });
    
    if (completedConnections === requiredConnections.length) {
        puzzleState.isComplete = true;
        const completionTime = Date.now() - puzzleState.startTime;
        puzzleState.score += Math.max(1000 - Math.floor(completionTime / 100), 100);
        
        console.log('[PUZZLE] Level completed!');
        showLevelComplete(completionTime);
    }
}

function nextLevel() {
    if (puzzleState.currentLevel < puzzleState.maxLevel) {
        loadLevel(puzzleState.currentLevel + 1);
    } else {
        showGameComplete();
    }
}

// ========== Rendering ==========

function updatePuzzleDisplay() {
    clearPuzzleCanvases();
    drawPuzzleNodes();
    drawPuzzleConnections();
    drawTargetHints();
}

function clearPuzzleCanvases() {
    if (puzzleState.temporaryCtx) {
        puzzleState.temporaryCtx.clearRect(0, 0, puzzleState.temporaryCanvas.width, puzzleState.temporaryCanvas.height);
    }
    if (puzzleState.gameCtx) {
        puzzleState.gameCtx.clearRect(0, 0, puzzleState.gameCanvas.width, puzzleState.gameCanvas.height);
    }
}

function drawPuzzleNodes() {
    // Draw temporary nodes
    puzzleState.temporaryNodes.forEach(node => {
        drawPuzzleNode(node, puzzleState.temporaryCtx);
    });
    
    // Draw placed nodes
    puzzleState.placedNodes.forEach(node => {
        drawPuzzleNode(node, puzzleState.gameCtx);
    });
}

function drawPuzzleNode(node, ctx) {
    const nodeSize = 60;
    const portSize = 12;
    
    // Draw node body
    ctx.fillStyle = getPuzzleNodeColor(node.type);
    ctx.fillRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // Draw node border
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.strokeRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // Draw ports
    drawNodePorts(node, ctx, nodeSize, portSize);
    
    // Draw node label
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.id, node.x, node.y + 3);
}

function drawNodePorts(node, ctx, nodeSize, portSize) {
    // Input ports (left side)
    if (node.inputPorts && node.inputPorts.length > 0) {
        const spacing = nodeSize / (node.inputPorts.length + 1);
        node.inputPorts.forEach((port, index) => {
            const portX = node.x - nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawPuzzlePort(ctx, portX, portY, port, portSize);
        });
    }
    
    // Output ports (right side)
    if (node.outputPorts && node.outputPorts.length > 0) {
        const spacing = nodeSize / (node.outputPorts.length + 1);
        node.outputPorts.forEach((port, index) => {
            const portX = node.x + nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawPuzzlePort(ctx, portX, portY, port, portSize);
        });
    }
}

function drawPuzzlePort(ctx, x, y, port, size) {
    ctx.fillStyle = port.color;
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 1;
    
    switch (port.type) {
        case 'square':
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.strokeRect(x - size/2, y - size/2, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x - size/2, y + size/2);
            ctx.lineTo(x + size/2, y + size/2);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x + size/2, y);
            ctx.lineTo(x, y + size/2);
            ctx.lineTo(x - size/2, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
}

function getPuzzleNodeColor(type) {
    switch (type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        case 'intermediate': return '#2196F3';
        default: return '#666';
    }
}

function drawPuzzleConnections() {
    puzzleState.connections.forEach(connection => {
        const fromPort = findPuzzlePortById(connection.fromPort);
        const toPort = findPuzzlePortById(connection.toPort);
        
        if (fromPort && toPort) {
            drawPuzzleConnectionLine(puzzleState.gameCtx, fromPort, toPort);
        }
    });
}

function drawPuzzleConnectionLine(ctx, fromPort, toPort) {
    ctx.strokeStyle = fromPort.color;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(fromPort.x, fromPort.y);
    ctx.lineTo(toPort.x, toPort.y);
    ctx.stroke();
}

function drawTargetHints() {
    // Draw subtle hints for target connections
    const ctx = puzzleState.gameCtx;
    ctx.strokeStyle = 'rgba(255,255,255,0.3)';
    ctx.lineWidth = 1;
    ctx.setLineDash([5, 5]);
    
    puzzleState.targetConnections.forEach(target => {
        const fromPort = findPuzzlePortById(target.from);
        const toPort = findPuzzlePortById(target.to);
        
        if (fromPort && toPort) {
            const connectionExists = puzzleState.connections.some(conn => 
                (conn.fromPort === target.from && conn.toPort === target.to) ||
                (conn.fromPort === target.to && conn.toPort === target.from)
            );
            
            if (!connectionExists) {
                ctx.beginPath();
                ctx.moveTo(fromPort.x, fromPort.y);
                ctx.lineTo(toPort.x, toPort.y);
                ctx.stroke();
            }
        }
    });
    
    ctx.setLineDash([]);
}

// ========== Event Handling ==========

function handlePuzzleMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const isTemporaryArea = event.target === puzzleState.temporaryCanvas;
    
    // Check for port click
    const clickedPort = findPuzzlePortAtPosition(x, y, isTemporaryArea);
    if (clickedPort) {
        handlePuzzlePortClick(clickedPort);
        return;
    }
    
    // Check for node click (only in temporary area)
    if (isTemporaryArea) {
        const clickedNode = findPuzzleNodeAtPosition(x, y, true);
        if (clickedNode) {
            startPuzzleNodeDrag(clickedNode, x, y);
        }
    }
}

function handlePuzzleMouseMove(event) {
    if (puzzleState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        puzzleState.draggingNode.node.x = x;
        puzzleState.draggingNode.node.y = y;
        updatePuzzleDisplay();
    }
}

function handlePuzzleMouseUp(event) {
    if (puzzleState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        finishPuzzleNodeDrag(x, y, event.target === puzzleState.gameCanvas);
        puzzleState.draggingNode = null;
        updatePuzzleDisplay();
    }
}

// ========== Utility Functions ==========

function findPuzzleNodeAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? puzzleState.temporaryNodes : puzzleState.placedNodes;
    const nodeSize = 60;
    
    for (const node of nodes) {
        if (x >= node.x - nodeSize/2 && x <= node.x + nodeSize/2 &&
            y >= node.y - nodeSize/2 && y <= node.y + nodeSize/2) {
            return node;
        }
    }
    return null;
}

function findPuzzlePortAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? puzzleState.temporaryNodes : puzzleState.placedNodes;
    const portSize = 12;
    
    for (const node of nodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.x !== undefined && port.y !== undefined) {
                if (x >= port.x - portSize/2 && x <= port.x + portSize/2 &&
                    y >= port.y - portSize/2 && y <= port.y + portSize/2) {
                    return port;
                }
            }
        }
    }
    return null;
}

function findPuzzlePortById(portId) {
    const allNodes = [...puzzleState.temporaryNodes, ...puzzleState.placedNodes];
    for (const node of allNodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.id === portId) {
                return port;
            }
        }
    }
    return null;
}

function startPuzzleNodeDrag(node, x, y) {
    puzzleState.draggingNode = {
        node: node,
        offsetX: x - node.x,
        offsetY: y - node.y
    };
}

function finishPuzzleNodeDrag(x, y, isGameArea) {
    const node = puzzleState.draggingNode.node;
    
    if (isGameArea) {
        // Move to game area
        if (puzzleState.temporaryNodes.includes(node)) {
            puzzleState.temporaryNodes = puzzleState.temporaryNodes.filter(n => n !== node);
            puzzleState.placedNodes.push(node);
        }
    } else {
        // Move back to temporary area
        if (puzzleState.placedNodes.includes(node)) {
            puzzleState.placedNodes = puzzleState.placedNodes.filter(n => n !== node);
            puzzleState.temporaryNodes.push(node);
            positionTemporaryNodes();
        }
    }
}

function handlePuzzlePortClick(port) {
    if (!puzzleState.selectedPort) {
        puzzleState.selectedPort = port;
        console.log('[PUZZLE] Selected port', port.id);
    } else {
        if (canPuzzleConnect(puzzleState.selectedPort, port)) {
            createPuzzleConnection(puzzleState.selectedPort, port);
        }
        puzzleState.selectedPort = null;
    }
}

function canPuzzleConnect(fromPort, toPort) {
    return fromPort.type === toPort.type && fromPort.color === toPort.color && fromPort.id !== toPort.id;
}

function createPuzzleConnection(fromPort, toPort) {
    const connection = {
        id: `conn_${Date.now()}`,
        fromPort: fromPort.id,
        toPort: toPort.id
    };
    
    puzzleState.connections.push(connection);
    console.log('[PUZZLE] Created connection', connection.id);
    updatePuzzleDisplay();
    checkLevelComplete();
}

// ========== UI Functions ==========

function updateLevelInfo(level) {
    const levelInfoElement = document.getElementById('level-info');
    if (levelInfoElement) {
        levelInfoElement.innerHTML = `
            <h3>Level ${puzzleState.currentLevel}: ${level.name}</h3>
            <p>${level.description}</p>
            <p>Score: ${puzzleState.score}</p>
        `;
    }
}

function showLevelComplete(completionTime) {
    const message = `Level ${puzzleState.currentLevel} completed in ${(completionTime/1000).toFixed(1)}s!`;
    console.log('[PUZZLE]', message);
    
    setTimeout(() => {
        if (confirm(message + '\n\nProceed to next level?')) {
            nextLevel();
        }
    }, 500);
}

function showGameComplete() {
    const message = `Congratulations! All levels completed!\nFinal Score: ${puzzleState.score}`;
    console.log('[PUZZLE]', message);
    alert(message);
}

// ========== Global Functions ==========

window.loadLevel = loadLevel;
window.nextLevel = nextLevel;
window.updatePuzzleDisplay = updatePuzzleDisplay;

// ========== Auto-Initialize ==========

document.addEventListener('DOMContentLoaded', function() {
    console.log('[PUZZLE] DOM loaded, initializing puzzle mode...');
    setTimeout(() => {
        if (initializePuzzleMode()) {
            console.log('[PUZZLE] Puzzle mode ready!');
        }
    }, 100);
});
