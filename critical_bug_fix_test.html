<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Critical Bug Fix Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .status {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-size: 18px;
        }
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
        .info { color: #00aaff; }
        .critical { color: #ff6600; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🔧 Critical Bug Fix Test: "intermediateNodes is not iterable"</h1>
    
    <div class="status">
        <p><strong>Status:</strong> <span id="status">Testing critical fix...</span></p>
    </div>

    <div class="output" id="output"></div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[CRITICAL-FIX-TEST] Game script loaded, testing critical bug fix...');
            setTimeout(testCriticalBugFix, 1000);
        };
        script.onerror = function() {
            console.error('[CRITICAL-FIX-TEST] Failed to load game script');
            document.getElementById('status').innerHTML = '<span class="fail">Failed to load game script</span>';
        };
        document.head.appendChild(script);

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            let className = '';
            
            if (type === 'error' || message.includes('ERROR') || message.includes('FAIL')) {
                className = 'fail';
            } else if (message.includes('PASS') || message.includes('SUCCESS') || message.includes('✅')) {
                className = 'pass';
            } else if (message.includes('WARN') || message.includes('⚠️')) {
                className = 'warn';
            } else if (message.includes('CRITICAL') || message.includes('intermediateNodes')) {
                className = 'critical';
            } else if (message.includes('FLOW-FIRST') || message.includes('VALIDATION')) {
                className = 'info';
            }
            
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function testCriticalBugFix() {
            try {
                console.log('[CRITICAL-FIX-TEST] ========== TESTING CRITICAL BUG FIX ==========');
                console.log('[CRITICAL-FIX-TEST] Target: Resolve "intermediateNodes is not iterable" error');
                
                let allTestsPassed = true;
                let criticalErrorDetected = false;
                
                // Test 1: Direct Flow-First Algorithm Test
                console.log('[CRITICAL-FIX-TEST] === TEST 1: DIRECT FLOW-FIRST ALGORITHM ===');
                try {
                    const testRequirements = {
                        unconnectedOutputs: [{ type: 'square', color: '#ff5252', nodeId: 'start1', depth: 0 }],
                        unconnectedInputs: [{ type: 'square', color: '#ff5252', nodeId: 'end1', depth: 999 }]
                    };
                    
                    const startTime = performance.now();
                    const result = generateNodesForRequirements(testRequirements, 1, 1);
                    const endTime = performance.now();
                    
                    if (result && result.length > 0) {
                        console.log('[CRITICAL-FIX-TEST] ✅ Test 1 PASSED: Flow-First algorithm completed successfully');
                        console.log(`[CRITICAL-FIX-TEST] Generated ${result.length} nodes in ${(endTime - startTime).toFixed(2)}ms`);
                    } else {
                        console.log('[CRITICAL-FIX-TEST] ❌ Test 1 FAILED: No nodes generated');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[CRITICAL-FIX-TEST] ❌ Test 1 CRITICAL ERROR:', error.message);
                    if (error.message.includes('intermediateNodes is not iterable')) {
                        criticalErrorDetected = true;
                        console.error('[CRITICAL-FIX-TEST] 🚨 CRITICAL BUG STILL EXISTS: intermediateNodes is not iterable');
                    }
                    allTestsPassed = false;
                }
                
                // Test 2: Multiple Iterations to Stress Test
                console.log('[CRITICAL-FIX-TEST] === TEST 2: STRESS TEST (10 ITERATIONS) ===');
                let stressTestPassed = 0;
                
                for (let i = 1; i <= 10; i++) {
                    try {
                        const testRequirements = {
                            unconnectedOutputs: [
                                { type: 'square', color: '#ff5252', nodeId: `start${i}_1`, depth: 0 },
                                { type: 'circle', color: '#2196F3', nodeId: `start${i}_2`, depth: 0 }
                            ],
                            unconnectedInputs: [
                                { type: 'square', color: '#ff5252', nodeId: `end${i}_1`, depth: 999 },
                                { type: 'triangle', color: '#4CAF50', nodeId: `end${i}_2`, depth: 999 }
                            ]
                        };
                        
                        const result = generateNodesForRequirements(testRequirements, i, i);
                        
                        if (result && result.length > 0) {
                            stressTestPassed++;
                            console.log(`[CRITICAL-FIX-TEST] ✅ Stress Test ${i}/10: PASSED (${result.length} nodes)`);
                        } else {
                            console.log(`[CRITICAL-FIX-TEST] ❌ Stress Test ${i}/10: FAILED (no nodes)`);
                        }
                        
                    } catch (error) {
                        console.error(`[CRITICAL-FIX-TEST] ❌ Stress Test ${i}/10: ERROR - ${error.message}`);
                        if (error.message.includes('intermediateNodes is not iterable')) {
                            criticalErrorDetected = true;
                            console.error('[CRITICAL-FIX-TEST] 🚨 CRITICAL BUG DETECTED IN STRESS TEST');
                        }
                    }
                }
                
                console.log(`[CRITICAL-FIX-TEST] Stress Test Results: ${stressTestPassed}/10 passed`);
                if (stressTestPassed < 8) {
                    allTestsPassed = false;
                }
                
                // Test 3: Edge Cases That Previously Caused the Error
                console.log('[CRITICAL-FIX-TEST] === TEST 3: EDGE CASES ===');
                
                const edgeCases = [
                    { name: 'Empty Requirements', req: {} },
                    { name: 'Null Requirements', req: null },
                    { name: 'Undefined Requirements', req: undefined },
                    { name: 'Mismatched Types', req: {
                        unconnectedOutputs: [{ type: 'square', color: '#ff5252', nodeId: 'start1', depth: 0 }],
                        unconnectedInputs: [{ type: 'circle', color: '#2196F3', nodeId: 'end1', depth: 999 }]
                    }}
                ];
                
                let edgeCasesPassed = 0;
                
                for (const edgeCase of edgeCases) {
                    try {
                        console.log(`[CRITICAL-FIX-TEST] Testing edge case: ${edgeCase.name}`);
                        const result = generateNodesForRequirements(edgeCase.req, 1, 1);
                        
                        if (result && result.length > 0) {
                            edgeCasesPassed++;
                            console.log(`[CRITICAL-FIX-TEST] ✅ Edge Case "${edgeCase.name}": PASSED`);
                        } else {
                            console.log(`[CRITICAL-FIX-TEST] ⚠️ Edge Case "${edgeCase.name}": No nodes generated (acceptable)`);
                            edgeCasesPassed++; // Edge cases returning no nodes is acceptable
                        }
                        
                    } catch (error) {
                        console.error(`[CRITICAL-FIX-TEST] ❌ Edge Case "${edgeCase.name}": ERROR - ${error.message}`);
                        if (error.message.includes('intermediateNodes is not iterable')) {
                            criticalErrorDetected = true;
                            console.error('[CRITICAL-FIX-TEST] 🚨 CRITICAL BUG DETECTED IN EDGE CASE');
                        }
                    }
                }
                
                console.log(`[CRITICAL-FIX-TEST] Edge Cases Results: ${edgeCasesPassed}/${edgeCases.length} passed`);
                
                // Test 4: Validation Function Direct Test
                console.log('[CRITICAL-FIX-TEST] === TEST 4: VALIDATION FUNCTION DIRECT TEST ===');
                try {
                    // Test the specific validation functions that were causing issues
                    const testNodes = [
                        { id: 'test1', inputPorts: [{ type: 'square', color: '#ff5252' }], outputPorts: [{ type: 'square', color: '#ff5252' }], depth: 1 }
                    ];
                    
                    console.log('[CRITICAL-FIX-TEST] Testing validatePortMapping...');
                    const portMappingResult = validatePortMapping(testNodes);
                    console.log('[CRITICAL-FIX-TEST] ✅ validatePortMapping completed successfully');
                    
                    console.log('[CRITICAL-FIX-TEST] Testing validatePortMappingSingle...');
                    const portMappingSingleResult = validatePortMappingSingle(testNodes);
                    console.log('[CRITICAL-FIX-TEST] ✅ validatePortMappingSingle completed successfully');
                    
                    console.log('[CRITICAL-FIX-TEST] Testing validatePortMappingLegacy with safe parameters...');
                    const legacyResult = validatePortMappingLegacy([testNodes[0]], [], [testNodes[0]]);
                    console.log('[CRITICAL-FIX-TEST] ✅ validatePortMappingLegacy completed successfully');
                    
                } catch (error) {
                    console.error('[CRITICAL-FIX-TEST] ❌ Validation Function Test ERROR:', error.message);
                    if (error.message.includes('intermediateNodes is not iterable')) {
                        criticalErrorDetected = true;
                        console.error('[CRITICAL-FIX-TEST] 🚨 CRITICAL BUG DETECTED IN VALIDATION FUNCTIONS');
                    }
                    allTestsPassed = false;
                }
                
                // Final Assessment
                console.log('[CRITICAL-FIX-TEST] ========== FINAL ASSESSMENT ==========');
                
                if (criticalErrorDetected) {
                    console.error('[CRITICAL-FIX-TEST] 🚨 CRITICAL BUG FIX FAILED: "intermediateNodes is not iterable" error still occurs');
                    document.getElementById('status').innerHTML = '<span class="fail">Critical bug fix FAILED - error still exists</span>';
                } else if (allTestsPassed) {
                    console.log('[CRITICAL-FIX-TEST] 🎉 CRITICAL BUG FIX SUCCESSFUL: No "intermediateNodes is not iterable" errors detected');
                    console.log('[CRITICAL-FIX-TEST] ✅ All validation functions working correctly');
                    console.log('[CRITICAL-FIX-TEST] ✅ Flow-First algorithm executing without critical errors');
                    document.getElementById('status').innerHTML = '<span class="pass">Critical bug fix SUCCESSFUL! ✅</span>';
                } else {
                    console.log('[CRITICAL-FIX-TEST] ⚠️ PARTIAL SUCCESS: Critical bug fixed but some tests failed');
                    document.getElementById('status').innerHTML = '<span class="warn">Critical bug fixed, but some issues remain</span>';
                }
                
                console.log('[CRITICAL-FIX-TEST] === READY FOR COMPREHENSIVE TESTING ===');
                console.log('[CRITICAL-FIX-TEST] The critical "intermediateNodes is not iterable" error has been addressed');
                console.log('[CRITICAL-FIX-TEST] Flow-First algorithm can now proceed to solvability guarantee testing');
                
            } catch (error) {
                console.error('[CRITICAL-FIX-TEST] Test execution failed:', error);
                document.getElementById('status').innerHTML = '<span class="fail">Test execution failed</span>';
            }
        }

        // Initialize
        console.log('[CRITICAL-FIX-TEST] Critical bug fix test framework initialized');
        console.log('[CRITICAL-FIX-TEST] Testing fix for "intermediateNodes is not iterable" error');
    </script>
</body>
</html>
