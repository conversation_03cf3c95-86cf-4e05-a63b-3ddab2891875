<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SYSTEM.md Compliance Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .test-section {
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
        }
        
        .test-pass {
            background: #1a3a1a;
            border: 1px solid #4CAF50;
            color: #4CAF50;
        }
        
        .test-fail {
            background: #3a1a1a;
            border: 1px solid #f44336;
            color: #f44336;
        }
        
        .test-warn {
            background: #3a3a1a;
            border: 1px solid #ff9800;
            color: #ff9800;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.test {
            background: #2196F3;
        }
        
        .btn.test:hover {
            background: #1976D2;
        }
        
        .constraint-details {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .constraint-item {
            background: #2a2a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 SYSTEM.md Compliance Test</h1>
            <p>验证补偿算法是否严格遵循SYSTEM.md中定义的所有约束条件</p>
        </div>
        
        <div class="controls">
            <button class="btn test" onclick="runFullComplianceTest()">🧪 Run Full Test</button>
            <button class="btn test" onclick="testPortBalanceConstraints()">⚖️ Test Port Balance</button>
            <button class="btn test" onclick="testTopologyConstraints()">🔗 Test Topology</button>
            <button class="btn test" onclick="testDepthConstraints()">📏 Test Depth</button>
            <button class="btn" onclick="generateTestNodePool()">🎲 Generate Test Pool</button>
        </div>
        
        <div class="test-section">
            <h3>🎯 Test Results</h3>
            <div id="test-results">
                Click "Run Full Test" to start compliance testing...
            </div>
        </div>
        
        <div class="constraint-details">
            <h3>📋 SYSTEM.md Constraints</h3>
            <div class="constraint-item">
                <h4>1. 端口连接规则</h4>
                <ul>
                    <li>每个连接必须是输出端口连接到输入端口</li>
                    <li>连接的端口必须类型相同（相同形状和颜色）</li>
                    <li>每个端口最多参与一个连接</li>
                </ul>
            </div>
            <div class="constraint-item">
                <h4>2. 拓扑规则</h4>
                <ul>
                    <li>连接后形成的图必须是有向无环图(DAG)</li>
                    <li>起点只有出边，终点只有入边</li>
                    <li>中间节点既有入边也有出边</li>
                    <li>每个节点必须位于有效路径上</li>
                </ul>
            </div>
            <div class="constraint-item">
                <h4>3. 端口平衡约束</h4>
                <ul>
                    <li>对每种端口类型τ：可用输出端口数 ≥ 必需输入端口数</li>
                    <li>可用输出端口数 = 起点和中间节点的该类型输出端口总数</li>
                    <li>必需输入端口数 = 终点和中间节点的该类型输入端口总数</li>
                </ul>
            </div>
            <div class="constraint-item">
                <h4>4. 深度约束</h4>
                <ul>
                    <li>起点深度为0，终点深度为最大值</li>
                    <li>中间节点深度在起点和终点之间</li>
                    <li>连接只能从低深度节点指向高深度节点</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Current Test Node Pool</h3>
            <div id="node-pool-info">
                No test node pool generated yet.
            </div>
        </div>
        
        <div>
            <h3>📋 Test Log</h3>
            <div class="log" id="test-log"></div>
        </div>
    </div>

    <!-- Load the compensation algorithm -->
    <script src="compensation_algorithm.js"></script>
    
    <script>
        let testCompensationAlgorithm = null;
        let currentTestNodePool = null;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            testCompensationAlgorithm = new CompensationAlgorithm();
            logTest('SYSTEM.md Compliance Test initialized', 'success');
        });
        
        // Logging system
        function logTest(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('test-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[SYSTEM-MD-TEST] ${message}`);
        }
        
        // Generate test node pool
        function generateTestNodePool() {
            logTest('Generating test node pool...', 'info');
            
            // Create initial unbalanced nodes
            const initialNodes = [
                {
                    id: 'test_start_1',
                    type: 'start',
                    depth: 0,
                    inputPorts: [],
                    outputPorts: [
                        { id: 'start1_out1', type: 'square', color: 'red', side: 'output', nodeId: 'test_start_1', portTypeKey: 'square-red' },
                        { id: 'start1_out2', type: 'circle', color: 'blue', side: 'output', nodeId: 'test_start_1', portTypeKey: 'circle-blue' }
                    ]
                },
                {
                    id: 'test_mid_1',
                    type: 'intermediate',
                    depth: 500,
                    inputPorts: [
                        { id: 'mid1_in1', type: 'square', color: 'red', side: 'input', nodeId: 'test_mid_1', portTypeKey: 'square-red' }
                    ],
                    outputPorts: [
                        { id: 'mid1_out1', type: 'triangle', color: 'green', side: 'output', nodeId: 'test_mid_1', portTypeKey: 'triangle-green' },
                        { id: 'mid1_out2', type: 'triangle', color: 'green', side: 'output', nodeId: 'test_mid_1', portTypeKey: 'triangle-green' }
                    ]
                },
                {
                    id: 'test_end_1',
                    type: 'end',
                    depth: 1000,
                    inputPorts: [
                        { id: 'end1_in1', type: 'triangle', color: 'green', side: 'input', nodeId: 'test_end_1', portTypeKey: 'triangle-green' }
                    ],
                    outputPorts: []
                }
            ];
            
            // Apply compensation algorithm
            const modifications = [
                { type: 'add_port', nodeId: 'test_mid_1', portSpec: { type: 'diamond', color: 'yellow', side: 'output' } }
            ];
            
            currentTestNodePool = testCompensationAlgorithm.generateNextRound(
                initialNodes, 
                modifications, 
                { targetNewNodes: 2, maxCompensationNodes: 5 }
            );
            
            updateNodePoolDisplay();
            logTest(`Generated test node pool with ${currentTestNodePool.length} nodes`, 'success');
        }
        
        // Update node pool display
        function updateNodePoolDisplay() {
            if (!currentTestNodePool) return;
            
            const infoElement = document.getElementById('node-pool-info');
            const nodesByType = { start: 0, intermediate: 0, end: 0 };
            const portTypeCounts = new Map();
            
            currentTestNodePool.forEach(node => {
                const nodeType = testCompensationAlgorithm.getNodeTypeByPorts(node);
                nodesByType[nodeType]++;
                
                [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
                    const key = port.portTypeKey || `${port.type}-${port.color}`;
                    portTypeCounts.set(key, (portTypeCounts.get(key) || 0) + 1);
                });
            });
            
            const portTypeInfo = Array.from(portTypeCounts.entries())
                .map(([type, count]) => `${type}: ${count}`)
                .join(', ');
            
            infoElement.innerHTML = `
                <div><strong>Total Nodes:</strong> ${currentTestNodePool.length}</div>
                <div><strong>By Type:</strong> Start: ${nodesByType.start}, Intermediate: ${nodesByType.intermediate}, End: ${nodesByType.end}</div>
                <div><strong>Port Types:</strong> ${portTypeInfo}</div>
            `;
        }
        
        // Run full compliance test
        function runFullComplianceTest() {
            if (!currentTestNodePool) {
                generateTestNodePool();
            }
            
            logTest('=== RUNNING FULL SYSTEM.MD COMPLIANCE TEST ===', 'info');
            
            const results = [];
            
            // Test 1: Port Balance Constraints
            const portBalanceResult = testPortBalanceConstraints();
            results.push(portBalanceResult);
            
            // Test 2: Topology Constraints
            const topologyResult = testTopologyConstraints();
            results.push(topologyResult);
            
            // Test 3: Depth Constraints
            const depthResult = testDepthConstraints();
            results.push(depthResult);
            
            // Test 4: Overall Solvability
            const solvabilityResult = testOverallSolvability();
            results.push(solvabilityResult);
            
            // Display results
            displayTestResults(results);
            
            const passCount = results.filter(r => r.passed).length;
            const totalTests = results.length;
            
            logTest(`=== TEST SUMMARY: ${passCount}/${totalTests} TESTS PASSED ===`, 
                   passCount === totalTests ? 'success' : 'error');
        }
        
        // Test port balance constraints
        function testPortBalanceConstraints() {
            logTest('Testing port balance constraints...', 'info');
            
            if (!currentTestNodePool) {
                return { name: 'Port Balance', passed: false, message: 'No test node pool available' };
            }
            
            const portBalance = testCompensationAlgorithm.calculatePortBalance(currentTestNodePool);
            const validation = testCompensationAlgorithm.validateSystemMDPortBalance(portBalance);
            
            if (validation.valid) {
                logTest('✅ Port balance constraints satisfied', 'success');
                return { name: 'Port Balance', passed: true, message: 'All port types are balanced' };
            } else {
                logTest('❌ Port balance constraint violations found', 'error');
                validation.violations.forEach(violation => {
                    logTest(`  - ${JSON.stringify(violation)}`, 'error');
                });
                return { name: 'Port Balance', passed: false, message: `${validation.violations.length} violations found` };
            }
        }
        
        // Test topology constraints
        function testTopologyConstraints() {
            logTest('Testing topology constraints...', 'info');
            
            if (!currentTestNodePool) {
                return { name: 'Topology', passed: false, message: 'No test node pool available' };
            }
            
            const validation = testCompensationAlgorithm.validateSystemMDTopology(currentTestNodePool);
            
            if (validation.valid) {
                logTest('✅ Topology constraints satisfied', 'success');
                return { name: 'Topology', passed: true, message: 'DAG structure is valid' };
            } else {
                logTest('❌ Topology constraint violations found', 'error');
                validation.violations.forEach(violation => {
                    logTest(`  - ${violation}`, 'error');
                });
                return { name: 'Topology', passed: false, message: `${validation.violations.length} violations found` };
            }
        }
        
        // Test depth constraints
        function testDepthConstraints() {
            logTest('Testing depth constraints...', 'info');
            
            if (!currentTestNodePool) {
                return { name: 'Depth', passed: false, message: 'No test node pool available' };
            }
            
            const validation = testCompensationAlgorithm.validateSystemMDDepthConstraints(currentTestNodePool);
            
            if (validation.valid) {
                logTest('✅ Depth constraints satisfied', 'success');
                return { name: 'Depth', passed: true, message: 'All nodes have correct depths' };
            } else {
                logTest('❌ Depth constraint violations found', 'error');
                validation.violations.forEach(violation => {
                    logTest(`  - ${violation}`, 'error');
                });
                return { name: 'Depth', passed: false, message: `${validation.violations.length} violations found` };
            }
        }
        
        // Test overall solvability
        function testOverallSolvability() {
            logTest('Testing overall solvability...', 'info');
            
            if (!currentTestNodePool) {
                return { name: 'Solvability', passed: false, message: 'No test node pool available' };
            }
            
            const isValid = testCompensationAlgorithm.validateSolvability(currentTestNodePool);
            
            if (isValid) {
                logTest('✅ Node pool is solvable according to SYSTEM.md', 'success');
                return { name: 'Solvability', passed: true, message: 'Node pool satisfies all SYSTEM.md constraints' };
            } else {
                logTest('❌ Node pool is not solvable', 'error');
                return { name: 'Solvability', passed: false, message: 'Node pool violates SYSTEM.md constraints' };
            }
        }
        
        // Display test results
        function displayTestResults(results) {
            const resultsElement = document.getElementById('test-results');
            
            const resultHtml = results.map(result => {
                const className = result.passed ? 'test-pass' : 'test-fail';
                const icon = result.passed ? '✅' : '❌';
                return `<div class="${className}">${icon} <strong>${result.name}:</strong> ${result.message}</div>`;
            }).join('');
            
            resultsElement.innerHTML = resultHtml;
        }
        
        // Auto-generate test pool on load
        setTimeout(() => {
            generateTestNodePool();
        }, 1000);
    </script>
</body>
</html>
