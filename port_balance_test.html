<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Port Balance Test - SYSTEM.md Compliance</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .test-section {
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .balance-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .balance-item {
            background: #333;
            border: 2px solid #555;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
        }
        
        .balance-item.balanced {
            border-color: #4CAF50;
            background: #1a3a1a;
        }
        
        .balance-item.imbalanced {
            border-color: #f44336;
            background: #3a1a1a;
        }
        
        .port-type {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .port-stats {
            font-size: 0.9em;
            margin: 5px 0;
        }
        
        .delta {
            font-size: 1.5em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .delta.balanced {
            color: #4CAF50;
        }
        
        .delta.imbalanced {
            color: #f44336;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.test {
            background: #2196F3;
        }
        
        .btn.test:hover {
            background: #1976D2;
        }
        
        .btn.fix {
            background: #ff9800;
        }
        
        .btn.fix:hover {
            background: #f57c00;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .node-pool {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .node-item {
            background: #2a2a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚖️ Port Balance Test</h1>
            <p>SYSTEM.md Compliance - Port Balance Constraint Verification</p>
        </div>
        
        <div class="controls">
            <button class="btn test" onclick="generateTestScenario()">🎲 Generate Test Scenario</button>
            <button class="btn" onclick="checkPortBalance()">⚖️ Check Balance</button>
            <button class="btn fix" onclick="fixPortBalance()">🔧 Fix Imbalances</button>
            <button class="btn test" onclick="runBalanceStressTest()">🧪 Stress Test</button>
            <button class="btn danger" onclick="clearTestData()">🗑️ Clear</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Current Port Balance Status</h3>
            <div id="balance-display" class="balance-grid">
                <div class="balance-item">
                    <div class="port-type">No test data</div>
                    <div class="port-stats">Click "Generate Test Scenario" to start</div>
                </div>
            </div>
        </div>
        
        <div class="node-pool">
            <h3>🎯 Current Node Pool</h3>
            <div id="node-pool-display">
                No nodes generated yet.
            </div>
        </div>
        
        <div class="test-section">
            <h3>🧪 Test Results</h3>
            <div id="test-results">
                Ready to run port balance tests.
            </div>
        </div>
        
        <div>
            <h3>📋 Port Balance Log</h3>
            <div class="log" id="balance-log"></div>
        </div>
    </div>

    <!-- Load the compensation algorithm -->
    <script src="compensation_algorithm.js"></script>
    
    <script>
        let testCompensationAlgorithm = null;
        let currentTestNodes = [];
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            testCompensationAlgorithm = new CompensationAlgorithm();
            logBalance('Port Balance Test initialized', 'success');
        });
        
        // Logging system
        function logBalance(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('balance-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[PORT-BALANCE-TEST] ${message}`);
        }
        
        // Generate test scenario with intentional imbalances
        function generateTestScenario() {
            logBalance('Generating test scenario with intentional port imbalances', 'info');
            
            currentTestNodes = [
                // Start node with 2 red squares
                {
                    id: 'test_start_1',
                    type: 'start',
                    depth: 0,
                    inputPorts: [],
                    outputPorts: [
                        { id: 'start1_out1', type: 'square', color: 'red', side: 'output', nodeId: 'test_start_1', portTypeKey: 'square-red' },
                        { id: 'start1_out2', type: 'square', color: 'red', side: 'output', nodeId: 'test_start_1', portTypeKey: 'square-red' }
                    ]
                },
                // Intermediate node consuming 1 red square, producing 2 blue circles
                {
                    id: 'test_mid_1',
                    type: 'intermediate',
                    depth: 500,
                    inputPorts: [
                        { id: 'mid1_in1', type: 'square', color: 'red', side: 'input', nodeId: 'test_mid_1', portTypeKey: 'square-red' }
                    ],
                    outputPorts: [
                        { id: 'mid1_out1', type: 'circle', color: 'blue', side: 'output', nodeId: 'test_mid_1', portTypeKey: 'circle-blue' },
                        { id: 'mid1_out2', type: 'circle', color: 'blue', side: 'output', nodeId: 'test_mid_1', portTypeKey: 'circle-blue' }
                    ]
                },
                // End node consuming only 1 blue circle
                {
                    id: 'test_end_1',
                    type: 'end',
                    depth: 1000,
                    inputPorts: [
                        { id: 'end1_in1', type: 'circle', color: 'blue', side: 'input', nodeId: 'test_end_1', portTypeKey: 'circle-blue' }
                    ],
                    outputPorts: []
                }
            ];
            
            logBalance(`Generated test scenario with ${currentTestNodes.length} nodes`, 'success');
            updateNodePoolDisplay();
            checkPortBalance();
        }
        
        // Check port balance
        function checkPortBalance() {
            if (currentTestNodes.length === 0) {
                logBalance('No test nodes available. Generate a test scenario first.', 'warn');
                return;
            }
            
            logBalance('Checking port balance across all test nodes', 'info');
            
            const portBalance = testCompensationAlgorithm.calculatePortBalance(currentTestNodes);
            
            // Update display
            updateBalanceDisplay(portBalance);
            
            // Log results
            let hasImbalance = false;
            portBalance.forEach((stat, portType) => {
                if (stat.delta !== 0) {
                    hasImbalance = true;
                    logBalance(`IMBALANCE DETECTED: ${portType} has delta=${stat.delta}`, 'error');
                } else {
                    logBalance(`BALANCED: ${portType} is perfectly balanced`, 'success');
                }
            });
            
            if (!hasImbalance) {
                logBalance('✅ ALL PORT TYPES ARE PERFECTLY BALANCED!', 'success');
                document.getElementById('test-results').innerHTML = 
                    '<div style="color: #4CAF50; font-size: 1.2em;">✅ All port types satisfy SYSTEM.md balance constraints!</div>';
            } else {
                logBalance('❌ PORT BALANCE VIOLATIONS DETECTED', 'error');
                document.getElementById('test-results').innerHTML = 
                    '<div style="color: #f44336; font-size: 1.2em;">❌ Port balance violations found. Use "Fix Imbalances" to correct.</div>';
            }
        }
        
        // Fix port balance using compensation algorithm
        function fixPortBalance() {
            if (currentTestNodes.length === 0) {
                logBalance('No test nodes available. Generate a test scenario first.', 'warn');
                return;
            }
            
            logBalance('Applying port balance fixes using compensation algorithm', 'info');
            
            // Create a mock game state
            const mockGameState = {
                temporaryNodes: currentTestNodes.filter(n => n.area === 'temporary'),
                placedNodes: currentTestNodes.filter(n => n.area !== 'temporary'),
                connections: []
            };
            
            // Apply emergency fix
            const fixedNodes = testCompensationAlgorithm.emergencyFixWithGameState([], mockGameState);
            
            // Add fixed nodes to current test nodes
            fixedNodes.forEach(node => {
                node.area = 'temporary';
            });
            
            currentTestNodes.push(...fixedNodes);
            
            logBalance(`Added ${fixedNodes.length} compensation nodes`, 'success');
            updateNodePoolDisplay();
            
            // Re-check balance
            setTimeout(() => {
                checkPortBalance();
            }, 500);
        }
        
        // Run stress test with multiple scenarios
        function runBalanceStressTest() {
            logBalance('Running port balance stress test', 'info');
            
            const testScenarios = [
                'Excess outputs scenario',
                'Deficit outputs scenario', 
                'Mixed imbalances scenario',
                'Complex multi-type scenario'
            ];
            
            let passedTests = 0;
            
            testScenarios.forEach((scenario, index) => {
                setTimeout(() => {
                    logBalance(`Testing scenario ${index + 1}: ${scenario}`, 'info');
                    
                    // Generate different test scenarios
                    generateStressTestScenario(index);
                    
                    // Check initial balance
                    const initialBalance = testCompensationAlgorithm.calculatePortBalance(currentTestNodes);
                    let hasInitialImbalance = false;
                    initialBalance.forEach(stat => {
                        if (stat.delta !== 0) hasInitialImbalance = true;
                    });
                    
                    if (hasInitialImbalance) {
                        // Apply fix
                        const mockGameState = {
                            temporaryNodes: currentTestNodes,
                            placedNodes: [],
                            connections: []
                        };
                        
                        const fixedNodes = testCompensationAlgorithm.emergencyFixWithGameState([], mockGameState);
                        currentTestNodes.push(...fixedNodes);
                        
                        // Check final balance
                        const finalBalance = testCompensationAlgorithm.calculatePortBalance(currentTestNodes);
                        let isFixed = true;
                        finalBalance.forEach(stat => {
                            if (stat.delta !== 0) isFixed = false;
                        });
                        
                        if (isFixed) {
                            passedTests++;
                            logBalance(`✅ Scenario ${index + 1} PASSED: Balance restored`, 'success');
                        } else {
                            logBalance(`❌ Scenario ${index + 1} FAILED: Balance not restored`, 'error');
                        }
                    } else {
                        passedTests++;
                        logBalance(`✅ Scenario ${index + 1} PASSED: Already balanced`, 'success');
                    }
                    
                    // Final summary
                    if (index === testScenarios.length - 1) {
                        setTimeout(() => {
                            logBalance(`=== STRESS TEST COMPLETE: ${passedTests}/${testScenarios.length} PASSED ===`, 
                                     passedTests === testScenarios.length ? 'success' : 'error');
                            
                            document.getElementById('test-results').innerHTML = 
                                `<div style="color: ${passedTests === testScenarios.length ? '#4CAF50' : '#f44336'}; font-size: 1.2em;">
                                    Stress Test: ${passedTests}/${testScenarios.length} scenarios passed
                                </div>`;
                        }, 500);
                    }
                }, index * 2000);
            });
        }
        
        // Generate different stress test scenarios
        function generateStressTestScenario(scenarioIndex) {
            switch (scenarioIndex) {
                case 0: // Excess outputs
                    currentTestNodes = [
                        {
                            id: 'excess_start',
                            type: 'start',
                            depth: 0,
                            inputPorts: [],
                            outputPorts: [
                                { id: 'ex_out1', type: 'square', color: 'red', side: 'output', nodeId: 'excess_start', portTypeKey: 'square-red' },
                                { id: 'ex_out2', type: 'square', color: 'red', side: 'output', nodeId: 'excess_start', portTypeKey: 'square-red' },
                                { id: 'ex_out3', type: 'square', color: 'red', side: 'output', nodeId: 'excess_start', portTypeKey: 'square-red' }
                            ]
                        }
                    ];
                    break;
                    
                case 1: // Deficit outputs
                    currentTestNodes = [
                        {
                            id: 'deficit_end',
                            type: 'end',
                            depth: 1000,
                            inputPorts: [
                                { id: 'def_in1', type: 'circle', color: 'blue', side: 'input', nodeId: 'deficit_end', portTypeKey: 'circle-blue' },
                                { id: 'def_in2', type: 'circle', color: 'blue', side: 'input', nodeId: 'deficit_end', portTypeKey: 'circle-blue' }
                            ],
                            outputPorts: []
                        }
                    ];
                    break;
                    
                case 2: // Mixed imbalances
                    currentTestNodes = [
                        {
                            id: 'mixed_start',
                            type: 'start',
                            depth: 0,
                            inputPorts: [],
                            outputPorts: [
                                { id: 'mix_out1', type: 'square', color: 'red', side: 'output', nodeId: 'mixed_start', portTypeKey: 'square-red' },
                                { id: 'mix_out2', type: 'square', color: 'red', side: 'output', nodeId: 'mixed_start', portTypeKey: 'square-red' }
                            ]
                        },
                        {
                            id: 'mixed_end',
                            type: 'end',
                            depth: 1000,
                            inputPorts: [
                                { id: 'mix_in1', type: 'circle', color: 'blue', side: 'input', nodeId: 'mixed_end', portTypeKey: 'circle-blue' },
                                { id: 'mix_in2', type: 'circle', color: 'blue', side: 'input', nodeId: 'mixed_end', portTypeKey: 'circle-blue' },
                                { id: 'mix_in3', type: 'circle', color: 'blue', side: 'input', nodeId: 'mixed_end', portTypeKey: 'circle-blue' }
                            ],
                            outputPorts: []
                        }
                    ];
                    break;
                    
                case 3: // Complex multi-type
                    generateTestScenario(); // Use the default complex scenario
                    break;
            }
        }
        
        // Update balance display
        function updateBalanceDisplay(portBalance) {
            const displayElement = document.getElementById('balance-display');
            const balanceItems = [];
            
            portBalance.forEach((stat, portType) => {
                const isBalanced = stat.delta === 0;
                const itemClass = isBalanced ? 'balance-item balanced' : 'balance-item imbalanced';
                const deltaClass = isBalanced ? 'delta balanced' : 'delta imbalanced';
                
                balanceItems.push(`
                    <div class="${itemClass}">
                        <div class="port-type">${portType}</div>
                        <div class="port-stats">Inputs: ${stat.inputs}</div>
                        <div class="port-stats">Outputs: ${stat.outputs}</div>
                        <div class="${deltaClass}">Δ ${stat.delta}</div>
                        <div class="port-stats">${isBalanced ? '✅ Balanced' : '❌ Imbalanced'}</div>
                    </div>
                `);
            });
            
            displayElement.innerHTML = balanceItems.join('');
        }
        
        // Update node pool display
        function updateNodePoolDisplay() {
            const displayElement = document.getElementById('node-pool-display');
            
            if (currentTestNodes.length === 0) {
                displayElement.innerHTML = 'No nodes generated yet.';
                return;
            }
            
            const nodeItems = currentTestNodes.map(node => {
                const inputCount = (node.inputPorts || []).length;
                const outputCount = (node.outputPorts || []).length;
                const createdBy = node.createdBy || 'original';
                
                return `
                    <div class="node-item">
                        <strong>${node.id}</strong> (${node.type}) - Depth: ${node.depth}
                        <br>Inputs: ${inputCount}, Outputs: ${outputCount}
                        <br>Created by: ${createdBy}
                    </div>
                `;
            }).join('');
            
            displayElement.innerHTML = nodeItems;
        }
        
        // Clear test data
        function clearTestData() {
            currentTestNodes = [];
            document.getElementById('balance-display').innerHTML = 
                '<div class="balance-item"><div class="port-type">No test data</div><div class="port-stats">Click "Generate Test Scenario" to start</div></div>';
            document.getElementById('node-pool-display').innerHTML = 'No nodes generated yet.';
            document.getElementById('test-results').innerHTML = 'Ready to run port balance tests.';
            logBalance('Test data cleared', 'info');
        }
    </script>
</body>
</html>
