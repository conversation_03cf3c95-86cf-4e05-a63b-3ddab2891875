<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Test & Fix Framework</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .framework-container {
            max-width: 1800px;
            margin: 0 auto;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .status-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .status-pass { border-left-color: #00ff00; }
        .status-fail { border-left-color: #ff0000; }
        .status-warn { border-left-color: #ffaa00; }
        .status-info { border-left-color: #00aaff; }
        .metric {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background: #333;
            border-radius: 3px;
            font-size: 12px;
        }
        .metric.pass { background: #1a3a1a; color: #00ff00; }
        .metric.fail { background: #3a1a1a; color: #ff0000; }
        .metric.warn { background: #3a3a1a; color: #ffaa00; }
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 11px;
        }
        .iteration-log {
            background: #1a1a2a;
            border: 1px solid #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="framework-container">
        <h1>🔬 Comprehensive Test & Fix Framework</h1>
        <p><strong>Objective:</strong> Systematically test and iterate until 100% solvability guarantee is achieved</p>
        
        <div class="status-grid">
            <div class="status-panel status-info">
                <h3>🎯 Current Iteration</h3>
                <div class="metric" id="current-iteration">Iteration 0</div>
                <div class="metric" id="test-phase">Ready</div>
            </div>
            
            <div class="status-panel status-warn">
                <h3>📊 Solvability Metrics</h3>
                <div class="metric warn" id="solvability-rate">Rate: 0%</div>
                <div class="metric warn" id="system-md-compliance">SYSTEM.md: 0%</div>
            </div>
            
            <div class="status-panel status-fail">
                <h3>❌ Current Issues</h3>
                <div class="metric fail" id="critical-errors">Critical: 0</div>
                <div class="metric warn" id="validation-failures">Validation: 0</div>
            </div>
            
            <div class="status-panel status-info">
                <h3>⚡ Performance</h3>
                <div class="metric" id="avg-time">Avg: 0ms</div>
                <div class="metric" id="success-rate">Success: 0%</div>
            </div>
        </div>

        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill"></div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button id="start-framework-btn" onclick="startComprehensiveFramework()">🚀 Start Comprehensive Framework</button>
            <button id="single-iteration-btn" onclick="runSingleIteration()" disabled>🔄 Single Iteration</button>
            <button id="analyze-issues-btn" onclick="analyzeCurrentIssues()" disabled>🔍 Analyze Issues</button>
            <button onclick="clearOutput()">🧹 Clear Output</button>
        </div>

        <div class="output" id="output"></div>
        
        <div id="iteration-history" style="display: none;">
            <h2>📋 Iteration History</h2>
            <div id="iteration-logs"></div>
        </div>
    </div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[FRAMEWORK] Game script loaded successfully');
            document.getElementById('start-framework-btn').disabled = false;
            document.getElementById('single-iteration-btn').disabled = false;
            document.getElementById('analyze-issues-btn').disabled = false;
        };
        script.onerror = function() {
            console.error('[FRAMEWORK] Failed to load game script');
            appendOutput('[ERROR] Failed to load game script', 'error');
        };
        document.head.appendChild(script);

        let frameworkState = {
            currentIteration: 0,
            maxIterations: 10,
            targetSolvabilityRate: 100,
            targetSystemMdCompliance: 95,
            iterationHistory: [],
            currentIssues: [],
            isRunning: false
        };

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            let className = '';
            
            if (type === 'error' || message.includes('ERROR') || message.includes('FAIL')) {
                className = 'fail';
            } else if (message.includes('PASS') || message.includes('SUCCESS') || message.includes('✅')) {
                className = 'pass';
            } else if (message.includes('WARN') || message.includes('⚠️')) {
                className = 'warn';
            } else if (message.includes('FRAMEWORK') || message.includes('ITERATION')) {
                className = 'info';
            }
            
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function startComprehensiveFramework() {
            const startBtn = document.getElementById('start-framework-btn');
            startBtn.disabled = true;
            startBtn.textContent = '🔄 Running Framework...';
            
            frameworkState.isRunning = true;
            frameworkState.currentIteration = 0;
            
            console.log('[FRAMEWORK] ========== COMPREHENSIVE TEST & FIX FRAMEWORK STARTED ==========');
            console.log('[FRAMEWORK] Target: 100% solvability guarantee with systematic iteration');
            
            try {
                for (let iteration = 1; iteration <= frameworkState.maxIterations; iteration++) {
                    frameworkState.currentIteration = iteration;
                    updateUI();
                    
                    console.log(`[FRAMEWORK] ===== ITERATION ${iteration}/${frameworkState.maxIterations} =====`);
                    
                    const iterationResult = await runSingleIteration();
                    frameworkState.iterationHistory.push(iterationResult);
                    
                    // Check if we've achieved the target
                    if (iterationResult.solvabilityRate >= frameworkState.targetSolvabilityRate &&
                        iterationResult.systemMdCompliance >= frameworkState.targetSystemMdCompliance) {
                        
                        console.log('[FRAMEWORK] 🎉 TARGET ACHIEVED! 100% solvability guarantee reached!');
                        break;
                    }
                    
                    // Analyze issues and suggest fixes
                    if (iterationResult.issues.length > 0) {
                        console.log(`[FRAMEWORK] Found ${iterationResult.issues.length} issues to address`);
                        await analyzeAndSuggestFixes(iterationResult.issues);
                    }
                    
                    // Add delay between iterations
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                
                generateFinalReport();
                
            } catch (error) {
                console.error('[FRAMEWORK] Framework execution failed:', error);
            }
            
            frameworkState.isRunning = false;
            startBtn.disabled = false;
            startBtn.textContent = '🚀 Start Comprehensive Framework';
        }

        async function runSingleIteration() {
            console.log(`[ITERATION-${frameworkState.currentIteration}] Starting single iteration test...`);
            
            const iterationResult = {
                iteration: frameworkState.currentIteration,
                timestamp: new Date().toISOString(),
                solvabilityRate: 0,
                systemMdCompliance: 0,
                performanceMetrics: {},
                issues: [],
                testResults: null
            };
            
            try {
                // Run comprehensive tests
                console.log(`[ITERATION-${frameworkState.currentIteration}] Running solvability guarantee test...`);
                
                if (window.testSolvabilityGuarantee) {
                    const solvabilityResult = window.testSolvabilityGuarantee();
                    iterationResult.solvabilityRate = (solvabilityResult.passedTests / solvabilityResult.totalTests * 100);
                    
                    if (solvabilityResult.failedTests > 0) {
                        solvabilityResult.testDetails.filter(test => !test.success).forEach(test => {
                            iterationResult.issues.push({
                                type: 'solvability',
                                description: `${test.name}: ${test.errors.join('; ')}`,
                                severity: 'high'
                            });
                        });
                    }
                }
                
                // Run comprehensive Mode 3 test
                console.log(`[ITERATION-${frameworkState.currentIteration}] Running comprehensive Mode 3 test...`);
                
                if (window.runMode3Test) {
                    const mode3Result = await window.runMode3Test();
                    iterationResult.testResults = mode3Result;
                    
                    // Calculate SYSTEM.md compliance
                    const systemMdStats = mode3Result.systemMdCriteria;
                    let totalTests = 0;
                    let totalPassed = 0;
                    
                    Object.values(systemMdStats).forEach(criteria => {
                        totalTests += criteria.passed + criteria.failed;
                        totalPassed += criteria.passed;
                    });
                    
                    iterationResult.systemMdCompliance = totalTests > 0 ? (totalPassed / totalTests * 100) : 0;
                    
                    // Collect performance metrics
                    iterationResult.performanceMetrics = {
                        averageTime: mode3Result.performance?.averageTime || 0,
                        maxTime: mode3Result.performance?.maxTime || 0,
                        passRate: (mode3Result.summary.passedTests / mode3Result.summary.totalTests * 100)
                    };
                    
                    // Collect issues from failed levels
                    const failedLevels = mode3Result.levels.filter(level => level.status !== 'PASS');
                    failedLevels.forEach(level => {
                        iterationResult.issues.push({
                            type: 'level_failure',
                            description: `Level ${level.level}: ${level.errors.join('; ')}`,
                            severity: 'medium',
                            level: level.level
                        });
                    });
                }
                
                console.log(`[ITERATION-${frameworkState.currentIteration}] Results: Solvability ${iterationResult.solvabilityRate.toFixed(1)}%, SYSTEM.md ${iterationResult.systemMdCompliance.toFixed(1)}%`);
                
            } catch (error) {
                console.error(`[ITERATION-${frameworkState.currentIteration}] Iteration failed:`, error);
                iterationResult.issues.push({
                    type: 'execution_error',
                    description: error.message,
                    severity: 'critical'
                });
            }
            
            updateIterationUI(iterationResult);
            return iterationResult;
        }

        async function analyzeAndSuggestFixes(issues) {
            console.log('[FRAMEWORK] ===== ISSUE ANALYSIS & FIX SUGGESTIONS =====');
            
            const issueCategories = {
                critical: issues.filter(issue => issue.severity === 'critical'),
                high: issues.filter(issue => issue.severity === 'high'),
                medium: issues.filter(issue => issue.severity === 'medium'),
                low: issues.filter(issue => issue.severity === 'low')
            };
            
            console.log(`[FRAMEWORK] Issue breakdown: Critical(${issueCategories.critical.length}), High(${issueCategories.high.length}), Medium(${issueCategories.medium.length}), Low(${issueCategories.low.length})`);
            
            // Analyze patterns in issues
            const errorPatterns = new Map();
            issues.forEach(issue => {
                const pattern = extractErrorPattern(issue.description);
                if (!errorPatterns.has(pattern)) {
                    errorPatterns.set(pattern, []);
                }
                errorPatterns.get(pattern).push(issue);
            });
            
            console.log('[FRAMEWORK] Common error patterns:');
            errorPatterns.forEach((occurrences, pattern) => {
                console.log(`[FRAMEWORK] - ${pattern}: ${occurrences.length} occurrences`);
            });
            
            // Suggest fixes based on patterns
            errorPatterns.forEach((occurrences, pattern) => {
                const suggestion = generateFixSuggestion(pattern, occurrences);
                if (suggestion) {
                    console.log(`[FRAMEWORK] 💡 Fix suggestion for "${pattern}": ${suggestion}`);
                }
            });
        }

        function extractErrorPattern(description) {
            // Extract common error patterns
            if (description.includes('intermediateNodes is not iterable')) return 'intermediateNodes_not_iterable';
            if (description.includes('targetTypes is not iterable')) return 'targetTypes_not_iterable';
            if (description.includes('sourceTypes.map is not a function')) return 'sourceTypes_map_error';
            if (description.includes('Validation Error')) return 'validation_error';
            if (description.includes('Port Mapping')) return 'port_mapping_failure';
            if (description.includes('DAG Topology')) return 'dag_topology_failure';
            if (description.includes('Flow Conservation')) return 'flow_conservation_failure';
            if (description.includes('Port Balance')) return 'port_balance_failure';
            return 'unknown_error';
        }

        function generateFixSuggestion(pattern, occurrences) {
            const suggestions = {
                'intermediateNodes_not_iterable': 'Fix function signature mismatch in validatePortMapping calls',
                'targetTypes_not_iterable': 'Fix parameter passing in calculateIntermediateSteps function',
                'sourceTypes_map_error': 'Ensure sourceTypes is an array before calling map function',
                'validation_error': 'Add comprehensive input validation to all validation functions',
                'port_mapping_failure': 'Improve port compatibility checking algorithm',
                'dag_topology_failure': 'Fix depth assignment and cycle detection logic',
                'flow_conservation_failure': 'Ensure balanced input/output port generation',
                'port_balance_failure': 'Implement stricter port balance validation'
            };
            
            return suggestions[pattern] || 'Manual investigation required';
        }

        function updateUI() {
            document.getElementById('current-iteration').textContent = `Iteration ${frameworkState.currentIteration}`;
            document.getElementById('progress-fill').style.width = `${(frameworkState.currentIteration / frameworkState.maxIterations) * 100}%`;
        }

        function updateIterationUI(result) {
            document.getElementById('solvability-rate').textContent = `Rate: ${result.solvabilityRate.toFixed(1)}%`;
            document.getElementById('solvability-rate').className = `metric ${result.solvabilityRate >= 90 ? 'pass' : 'fail'}`;
            
            document.getElementById('system-md-compliance').textContent = `SYSTEM.md: ${result.systemMdCompliance.toFixed(1)}%`;
            document.getElementById('system-md-compliance').className = `metric ${result.systemMdCompliance >= 95 ? 'pass' : 'warn'}`;
            
            const criticalIssues = result.issues.filter(issue => issue.severity === 'critical').length;
            const validationIssues = result.issues.filter(issue => issue.type === 'validation_error').length;
            
            document.getElementById('critical-errors').textContent = `Critical: ${criticalIssues}`;
            document.getElementById('validation-failures').textContent = `Validation: ${validationIssues}`;
            
            if (result.performanceMetrics) {
                document.getElementById('avg-time').textContent = `Avg: ${result.performanceMetrics.averageTime.toFixed(0)}ms`;
                document.getElementById('success-rate').textContent = `Success: ${result.performanceMetrics.passRate.toFixed(1)}%`;
            }
        }

        function generateFinalReport() {
            console.log('[FRAMEWORK] ========== FINAL COMPREHENSIVE REPORT ==========');
            
            const finalIteration = frameworkState.iterationHistory[frameworkState.iterationHistory.length - 1];
            const success = finalIteration.solvabilityRate >= frameworkState.targetSolvabilityRate &&
                           finalIteration.systemMdCompliance >= frameworkState.targetSystemMdCompliance;
            
            console.log(`[FRAMEWORK] Final Results after ${frameworkState.currentIteration} iterations:`);
            console.log(`[FRAMEWORK] Solvability Rate: ${finalIteration.solvabilityRate.toFixed(1)}% (Target: ${frameworkState.targetSolvabilityRate}%)`);
            console.log(`[FRAMEWORK] SYSTEM.md Compliance: ${finalIteration.systemMdCompliance.toFixed(1)}% (Target: ${frameworkState.targetSystemMdCompliance}%)`);
            console.log(`[FRAMEWORK] Remaining Issues: ${finalIteration.issues.length}`);
            
            if (success) {
                console.log('[FRAMEWORK] 🎉🎉🎉 SUCCESS: 100% SOLVABILITY GUARANTEE ACHIEVED! 🎉🎉🎉');
            } else {
                console.log('[FRAMEWORK] ⚠️ Target not yet achieved. Further iterations needed.');
                
                // Provide specific recommendations
                if (finalIteration.solvabilityRate < frameworkState.targetSolvabilityRate) {
                    console.log('[FRAMEWORK] 🔧 Priority: Fix solvability issues in Flow-First algorithm');
                }
                if (finalIteration.systemMdCompliance < frameworkState.targetSystemMdCompliance) {
                    console.log('[FRAMEWORK] 🔧 Priority: Improve SYSTEM.md criteria compliance');
                }
            }
            
            console.log('[FRAMEWORK] ========== END COMPREHENSIVE REPORT ==========');
        }

        async function analyzeCurrentIssues() {
            console.log('[FRAMEWORK] Analyzing current issues...');
            // Implementation for current issue analysis
        }

        function clearOutput() {
            document.getElementById('output').innerHTML = '';
        }

        // Initialize
        console.log('[FRAMEWORK] Comprehensive Test & Fix Framework loaded');
        console.log('[FRAMEWORK] Ready to systematically achieve 100% solvability guarantee');
    </script>
</body>
</html>
