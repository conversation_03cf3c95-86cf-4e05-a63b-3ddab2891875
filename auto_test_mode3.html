<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Mode 3 Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .status {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
    </style>
</head>
<body>
    <h1>🧪 Auto Mode 3 Test Execution</h1>
    
    <div class="status">
        <p><strong>Status:</strong> <span id="status">Initializing...</span></p>
        <p><strong>Test Progress:</strong> <span id="progress">0/0</span></p>
    </div>

    <div class="test-output" id="output"></div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[AUTO-TEST] Game script loaded, starting tests...');
            setTimeout(runAutoTests, 1000);
        };
        script.onerror = function() {
            console.error('[AUTO-TEST] Failed to load game script');
            document.getElementById('status').innerHTML = '<span class="fail">Failed to load game script</span>';
        };
        document.head.appendChild(script);

        // Mock console to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            const className = type === 'error' ? 'fail' : (message.includes('PASS') ? 'pass' : (message.includes('FAIL') ? 'fail' : ''));
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function runAutoTests() {
            try {
                document.getElementById('status').innerHTML = '<span class="warn">Running diagnostic...</span>';
                
                // Wait for game initialization
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('[AUTO-TEST] Starting automatic Mode 3 testing...');
                
                // Test 1: Diagnostic
                console.log('[AUTO-TEST] === DIAGNOSTIC TEST ===');
                if (window.diagnoseDagAlgorithm) {
                    const diagnosticResult = window.diagnoseDagAlgorithm();
                    console.log('[AUTO-TEST] Diagnostic result:', diagnosticResult ? 'PASS' : 'FAIL');
                } else {
                    console.error('[AUTO-TEST] diagnoseDagAlgorithm function not available');
                }
                
                document.getElementById('status').innerHTML = '<span class="warn">Running comprehensive test...</span>';
                
                // Test 2: Comprehensive Mode 3 Test
                console.log('[AUTO-TEST] === COMPREHENSIVE MODE 3 TEST ===');
                if (window.runMode3Test) {
                    document.getElementById('progress').textContent = '0/10';
                    
                    const testResults = await window.runMode3Test();
                    
                    document.getElementById('progress').textContent = '10/10';
                    
                    // Analyze results
                    const passRate = testResults.summary.passedTests / testResults.summary.totalTests;
                    const success = passRate >= 0.8;
                    
                    console.log('[AUTO-TEST] === TEST SUMMARY ===');
                    console.log(`[AUTO-TEST] Pass Rate: ${(passRate * 100).toFixed(1)}%`);
                    console.log(`[AUTO-TEST] Passed: ${testResults.summary.passedTests}`);
                    console.log(`[AUTO-TEST] Failed: ${testResults.summary.failedTests}`);
                    console.log(`[AUTO-TEST] Errors: ${testResults.summary.errorTests}`);
                    console.log(`[AUTO-TEST] Timeouts: ${testResults.summary.timeoutTests}`);
                    console.log(`[AUTO-TEST] Total Duration: ${testResults.totalDuration.toFixed(2)}ms`);
                    
                    // SYSTEM.md criteria analysis
                    console.log('[AUTO-TEST] === SYSTEM.MD CRITERIA ===');
                    Object.keys(testResults.systemMdCriteria).forEach(criteria => {
                        const stats = testResults.systemMdCriteria[criteria];
                        const rate = stats.passed / (stats.passed + stats.failed) * 100;
                        console.log(`[AUTO-TEST] ${criteria}: ${stats.passed}/${stats.passed + stats.failed} (${rate.toFixed(1)}%)`);
                    });
                    
                    // Performance analysis
                    if (testResults.performance) {
                        console.log('[AUTO-TEST] === PERFORMANCE ANALYSIS ===');
                        console.log(`[AUTO-TEST] Average Time: ${testResults.performance.averageTime.toFixed(2)}ms`);
                        console.log(`[AUTO-TEST] Max Time: ${testResults.performance.maxTime.toFixed(2)}ms`);
                        console.log(`[AUTO-TEST] Min Time: ${testResults.performance.minTime.toFixed(2)}ms`);
                        
                        if (testResults.performance.memoryLeaks.length > 0) {
                            console.log(`[AUTO-TEST] Memory Leaks: ${testResults.performance.memoryLeaks.length}`);
                        }
                        
                        if (testResults.performance.bottlenecks.length > 0) {
                            console.log(`[AUTO-TEST] Bottlenecks: ${testResults.performance.bottlenecks.length}`);
                        }
                    }
                    
                    // Failed levels analysis
                    const failedLevels = testResults.levels.filter(level => level.status !== 'PASS');
                    if (failedLevels.length > 0) {
                        console.log('[AUTO-TEST] === FAILED LEVELS ANALYSIS ===');
                        failedLevels.forEach(level => {
                            console.log(`[AUTO-TEST] Level ${level.level}: ${level.status}`);
                            if (level.errors.length > 0) {
                                console.log(`[AUTO-TEST]   Errors: ${level.errors.join('; ')}`);
                            }
                        });
                    }
                    
                    const statusText = success ? 
                        `<span class="pass">COMPLETED - ${(passRate * 100).toFixed(1)}% pass rate</span>` :
                        `<span class="fail">FAILED - ${testResults.summary.failedTests} failures</span>`;
                    
                    document.getElementById('status').innerHTML = statusText;
                    
                    console.log(`[AUTO-TEST] === FINAL RESULT: ${success ? 'SUCCESS' : 'FAILURE'} ===`);
                    
                } else {
                    console.error('[AUTO-TEST] runMode3Test function not available');
                    document.getElementById('status').innerHTML = '<span class="fail">Test function not available</span>';
                }
                
            } catch (error) {
                console.error('[AUTO-TEST] Auto test execution failed:', error);
                document.getElementById('status').innerHTML = '<span class="fail">Test execution failed</span>';
            }
        }

        // Initialize
        console.log('[AUTO-TEST] Auto test framework initialized');
    </script>
</body>
</html>
