<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>蓝图连接游戏 - 模式3 (强可解性保证)</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .game-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .game-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .game-title {
            font-size: 24px;
            margin: 0 0 10px 0;
            color: #00ff00;
        }
        
        .game-subtitle {
            font-size: 14px;
            margin: 0;
            color: #aaaaaa;
        }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 10px;
        }
        
        .temporary-header {
            text-align: center;
            color: #ffaa00;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-header {
            text-align: center;
            color: #00aaff;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .control-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .control-button:hover {
            background: #45a049;
        }
        
        .control-button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .status-panel {
            background: #2a2a2a;
            border: 2px solid #666;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .status-item {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        
        .status-item.good { border-left-color: #00ff00; }
        .status-item.warning { border-left-color: #ffaa00; }
        .status-item.error { border-left-color: #ff0000; }
        
        .status-label {
            font-size: 12px;
            color: #aaa;
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 16px;
            font-weight: bold;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin: 0 0 10px 0;
            color: #00aaff;
        }
        
        .instructions ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 5px 0;
            color: #ccc;
        }
        
        .math-constraints {
            background: #1a1a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .math-constraints h3 {
            margin: 0 0 10px 0;
            color: #aaccff;
        }
        
        .constraint-formula {
            font-family: 'Times New Roman', serif;
            color: #aaccff;
            margin: 5px 0;
            padding: 5px;
            background: #2a2a3a;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1 class="game-title">🔬 蓝图连接游戏 - 模式3</h1>
            <p class="game-subtitle">强可解性保证的无限构造模式 (SYSTEM.md 数学约束)</p>
        </div>
        
        <div class="math-constraints">
            <h3>📐 SYSTEM.md 数学约束</h3>
            <div class="constraint-formula"><strong>1. 端口类型平衡:</strong> ∀τ ∈ Γ: |Output_τ| = |Input_τ|</div>
            <div class="constraint-formula"><strong>2. DAG拓扑结构:</strong> ∀(u,v) ∈ E: depth(u) < depth(v)</div>
            <div class="constraint-formula"><strong>3. 流守恒:</strong> ∀v ∈ V: Σ(in_ports) = Σ(out_ports)</div>
            <div class="constraint-formula"><strong>4. 端口映射:</strong> ∃Φ: Output_ports → Input_ports (bijective)</div>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="temporary-header">🎯 临时节点池</div>
                <canvas id="temporaryCanvas" width="280" height="580"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="placement-header">🏗️ 蓝图构造区域</div>
                <canvas id="gameCanvas" width="680" height="580"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="control-button" onclick="advanceToNextWave()">🌊 下一波 (N)</button>
            <button class="control-button" onclick="validateCurrentState()">✅ 验证 (V)</button>
            <button class="control-button" onclick="runComprehensiveTest()">🧪 测试 (T)</button>
            <button class="control-button" onclick="resetGame()">🔄 重置</button>
            <button class="control-button" onclick="showHelp()">❓ 帮助</button>
        </div>
        
        <div class="status-panel">
            <h3>📊 游戏状态</h3>
            <div class="status-grid">
                <div class="status-item good" id="wave-status">
                    <div class="status-label">当前波次</div>
                    <div class="status-value" id="wave-value">1</div>
                </div>
                
                <div class="status-item warning" id="solvability-status">
                    <div class="status-label">强可解性保证</div>
                    <div class="status-value" id="solvability-value">检查中...</div>
                </div>
                
                <div class="status-item good" id="nodes-status">
                    <div class="status-label">节点数量</div>
                    <div class="status-value" id="nodes-value">0</div>
                </div>
                
                <div class="status-item good" id="constraints-status">
                    <div class="status-label">约束满足</div>
                    <div class="status-value" id="constraints-value">0/4</div>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 游戏说明</h3>
            <ul>
                <li><strong>目标:</strong> 在模式3中构建满足SYSTEM.md数学约束的蓝图</li>
                <li><strong>拖拽:</strong> 从临时池拖拽节点到构造区域</li>
                <li><strong>连接:</strong> 点击端口创建连接（相同类型和颜色）</li>
                <li><strong>验证:</strong> 系统持续验证强可解性保证</li>
                <li><strong>进阶:</strong> 每波会增加新节点，保持数学约束</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>⌨️ 快捷键</h3>
            <ul>
                <li><strong>N:</strong> 进入下一波</li>
                <li><strong>V:</strong> 验证当前状态</li>
                <li><strong>T:</strong> 运行综合测试</li>
                <li><strong>R:</strong> 重置游戏</li>
            </ul>
        </div>
    </div>

    <!-- 加载脚本 - 按依赖顺序 -->
    <script src="game_core.js"></script>
    <script src="mode3_test_suite.js"></script>
    <script src="game_simplified.js"></script>
    
    <script>
        // 额外的UI控制函数
        function validateCurrentState() {
            const validation = validateCurrentGameState();
            updateStatusDisplay(validation);
            
            const message = validation.allSatisfied ? 
                '✅ 所有约束满足，强可解性保证有效' : 
                '❌ 存在约束违反，需要调整';
            
            showMessage(message);
            console.log('[UI] Validation result:', validation);
        }
        
        function runComprehensiveTest() {
            if (window.runMode3ComprehensiveTest) {
                showMessage('🧪 正在运行综合测试...');
                
                window.runMode3ComprehensiveTest().then(result => {
                    const message = result.overallPassed ? 
                        `✅ 所有测试通过 (${result.summary.passRate})` :
                        `❌ 部分测试失败 (${result.summary.passRate})`;
                    
                    showMessage(message);
                    console.log('[UI] Test result:', result);
                });
            } else {
                showMessage('❌ 测试套件未加载');
            }
        }
        
        function resetGame() {
            gameState.infiniteMode.wave = 1;
            gameState.infiniteMode.difficulty = 1;
            gameState.temporaryNodes = [];
            gameState.placedNodes = [];
            gameState.connections = [];
            
            generateInitialMode3NodePool();
            updateDisplay();
            updateStatusDisplay();
            
            showMessage('🔄 游戏已重置');
        }
        
        function showHelp() {
            const helpText = `
🔬 模式3 - 强可解性保证的无限构造模式

这是一个基于严格数学约束的节点连接游戏。游戏实现了SYSTEM.md中定义的四个核心约束：

1. 端口类型平衡 - 确保每种端口类型的输入输出数量相等
2. DAG拓扑结构 - 保证有向无环图的深度单调性
3. 流守恒 - 验证每个节点的端口配置正确性
4. 端口映射 - 确保存在完美的双射映射

游戏特点：
- 构造性算法保证100%可解性
- 增量修改保持数学约束
- 实时验证强可解性保证
- 支持无限波次扩展

开始游戏后，系统会自动生成满足所有约束的初始节点池。
您可以拖拽节点构建蓝图，系统会持续验证数学约束的满足情况。
            `;
            
            alert(helpText);
        }
        
        function updateStatusDisplay(validation = null) {
            // 更新波次
            document.getElementById('wave-value').textContent = gameState.infiniteMode.wave;
            
            // 更新节点数量
            const totalNodes = gameState.temporaryNodes.length + gameState.placedNodes.length;
            document.getElementById('nodes-value').textContent = totalNodes;
            
            // 更新可解性状态
            if (validation) {
                const solvable = validation.allSatisfied;
                document.getElementById('solvability-value').textContent = solvable ? '✅ 保证' : '❌ 违反';
                
                const solvabilityStatus = document.getElementById('solvability-status');
                solvabilityStatus.className = `status-item ${solvable ? 'good' : 'error'}`;
                
                // 更新约束满足情况
                const satisfiedCount = Object.values(validation).filter(v => v && v.satisfied).length;
                document.getElementById('constraints-value').textContent = `${satisfiedCount}/4`;
                
                const constraintsStatus = document.getElementById('constraints-status');
                constraintsStatus.className = `status-item ${satisfiedCount === 4 ? 'good' : 'warning'}`;
            }
        }
        
        // 重写showMessage函数以显示UI消息
        function showMessage(message) {
            console.log('[MESSAGE]', message);
            
            // 创建临时消息显示
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #2a2a2a;
                color: #00ff00;
                padding: 15px 20px;
                border-radius: 5px;
                border: 1px solid #00ff00;
                font-family: 'Courier New', monospace;
                z-index: 1000;
                max-width: 300px;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            `;
            messageDiv.textContent = message;
            
            document.body.appendChild(messageDiv);
            
            // 3秒后自动移除
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    messageDiv.parentNode.removeChild(messageDiv);
                }
            }, 3000);
        }
        
        // 定期更新状态显示
        setInterval(() => {
            if (gameState.infiniteMode.isActive) {
                const validation = validateCurrentGameState();
                updateStatusDisplay(validation);
            }
        }, 2000);
        
        // 添加键盘快捷键支持
        document.addEventListener('keydown', function(event) {
            switch(event.key.toLowerCase()) {
                case 'r':
                    resetGame();
                    break;
            }
        });
    </script>
</body>
</html>
