<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>层级DAG算法数学约束验证测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-controls {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .test-controls button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .test-controls button:hover {
            background: #1177bb;
        }
        
        .test-controls button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 20px;
            height: 600px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .test-output pre {
            margin: 0;
            white-space: pre-wrap;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #3e3e42;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #0e639c, #1177bb);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-summary {
            background: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .test-summary.show {
            display: block;
        }
        
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .config-panel {
            background: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .config-panel label {
            display: inline-block;
            width: 150px;
            margin: 5px 0;
        }
        
        .config-panel input, .config-panel select {
            background: #3c3c3c;
            color: #d4d4d4;
            border: 1px solid #5a5a5a;
            padding: 5px;
            border-radius: 3px;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 层级DAG算法数学约束验证测试</h1>
        
        <div class="config-panel">
            <h3>测试配置</h3>
            <div>
                <label>测试关卡:</label>
                <select id="testLevels" multiple>
                    <option value="1" selected>关卡 1</option>
                    <option value="2" selected>关卡 2</option>
                    <option value="3" selected>关卡 3</option>
                    <option value="4" selected>关卡 4</option>
                    <option value="5" selected>关卡 5</option>
                </select>
            </div>
            <div>
                <label>每关卡测试次数:</label>
                <input type="number" id="testsPerLevel" value="10" min="1" max="100">
            </div>
            <div>
                <label>超时时间(ms):</label>
                <input type="number" id="maxExecutionTime" value="5000" min="1000" max="30000">
            </div>
            <div>
                <label>详细输出:</label>
                <input type="checkbox" id="verboseOutput" checked>
            </div>
        </div>
        
        <div class="test-controls">
            <button id="runFullTest">🚀 运行完整测试</button>
            <button id="runPerformanceTest">⚡ 性能基准测试</button>
            <button id="runSingleLevel">🎯 单关卡测试</button>
            <button id="clearOutput">🗑️ 清空输出</button>
            <select id="singleLevelSelect">
                <option value="1">关卡 1</option>
                <option value="2">关卡 2</option>
                <option value="3">关卡 3</option>
                <option value="4">关卡 4</option>
                <option value="5">关卡 5</option>
            </select>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        
        <div class="test-output" id="testOutput">
            <pre id="outputContent">准备运行层级DAG算法测试...

📋 测试内容:
✓ 层级结构约束验证 (L_0: 起点, L_max: 终点)
✓ DAG拓扑约束验证 (depth(u) < depth(v))
✓ 端口类型平衡约束验证 (∀τ: |input(τ)| = |output(τ)|)
✓ 端口流守恒约束验证 (起点/终点/中间节点规则)
✓ 连通性约束验证 (每个节点在有效路径上)
✓ 基本结构完整性验证

点击"运行完整测试"开始验证算法是否满足所有数学规范...
</pre>
        </div>
        
        <div class="test-summary" id="testSummary">
            <h3>📊 测试总结</h3>
            <div class="summary-item">
                <span>总测试数:</span>
                <span id="totalTests">0</span>
            </div>
            <div class="summary-item">
                <span>通过测试:</span>
                <span id="passedTests" class="success">0</span>
            </div>
            <div class="summary-item">
                <span>失败测试:</span>
                <span id="failedTests" class="error">0</span>
            </div>
            <div class="summary-item">
                <span>成功率:</span>
                <span id="successRate">0%</span>
            </div>
        </div>
    </div>

    <!-- 加载算法和测试代码 -->
    <script src="game.js"></script>
    <script src="test_layered_dag_algorithm.js"></script>
    
    <script>
        // 测试控制器
        class TestController {
            constructor() {
                this.isRunning = false;
                this.currentProgress = 0;
                this.totalProgress = 0;
                this.setupEventListeners();
            }
            
            setupEventListeners() {
                document.getElementById('runFullTest').addEventListener('click', () => this.runFullTest());
                document.getElementById('runPerformanceTest').addEventListener('click', () => this.runPerformanceTest());
                document.getElementById('runSingleLevel').addEventListener('click', () => this.runSingleLevelTest());
                document.getElementById('clearOutput').addEventListener('click', () => this.clearOutput());
            }
            
            async runFullTest() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateButtons(true);
                this.clearOutput();
                this.updateProgress(0);
                
                try {
                    // 更新测试配置
                    this.updateTestConfig();
                    
                    this.log('🚀 开始层级DAG算法数学约束验证测试\n', 'info');
                    
                    // 运行测试
                    const success = await this.runTestsWithProgress();
                    
                    if (success) {
                        this.log('\n✅ 所有测试通过！算法满足所有数学约束。', 'success');
                    } else {
                        this.log('\n❌ 部分测试失败，请检查算法实现。', 'error');
                    }
                    
                } catch (error) {
                    this.log(`\n💥 测试执行失败: ${error.message}`, 'error');
                } finally {
                    this.isRunning = false;
                    this.updateButtons(false);
                    this.updateProgress(100);
                }
            }
            
            async runPerformanceTest() {
                if (this.isRunning) return;
                
                this.isRunning = true;
                this.updateButtons(true);
                this.clearOutput();
                
                try {
                    this.log('⚡ 开始性能基准测试...\n', 'info');
                    await this.delay(100);
                    
                    if (typeof runPerformanceBenchmark === 'function') {
                        runPerformanceBenchmark();
                    } else {
                        this.log('❌ 性能测试函数未找到', 'error');
                    }
                    
                } catch (error) {
                    this.log(`💥 性能测试失败: ${error.message}`, 'error');
                } finally {
                    this.isRunning = false;
                    this.updateButtons(false);
                }
            }
            
            async runSingleLevelTest() {
                if (this.isRunning) return;
                
                const level = parseInt(document.getElementById('singleLevelSelect').value);
                
                this.isRunning = true;
                this.updateButtons(true);
                this.clearOutput();
                
                try {
                    this.log(`🎯 开始关卡 ${level} 单独测试...\n`, 'info');
                    
                    // 运行单关卡测试
                    await this.runSingleLevelTestImpl(level);
                    
                } catch (error) {
                    this.log(`💥 单关卡测试失败: ${error.message}`, 'error');
                } finally {
                    this.isRunning = false;
                    this.updateButtons(false);
                }
            }
            
            updateTestConfig() {
                const levels = Array.from(document.getElementById('testLevels').selectedOptions).map(opt => parseInt(opt.value));
                const testsPerLevel = parseInt(document.getElementById('testsPerLevel').value);
                const maxExecutionTime = parseInt(document.getElementById('maxExecutionTime').value);
                const verbose = document.getElementById('verboseOutput').checked;
                
                if (typeof TEST_CONFIG !== 'undefined') {
                    TEST_CONFIG.levels = levels;
                    TEST_CONFIG.testsPerLevel = testsPerLevel;
                    TEST_CONFIG.maxExecutionTime = maxExecutionTime;
                    TEST_CONFIG.verbose = verbose;
                }
            }
            
            async runTestsWithProgress() {
                // 重定向console.log到我们的输出
                const originalLog = console.log;
                console.log = (...args) => {
                    this.log(args.join(' '));
                    originalLog.apply(console, args);
                };
                
                try {
                    if (typeof runLayeredDAGAlgorithmTests === 'function') {
                        return runLayeredDAGAlgorithmTests();
                    } else {
                        this.log('❌ 测试函数未找到，请确保已正确加载test_layered_dag_algorithm.js', 'error');
                        return false;
                    }
                } finally {
                    console.log = originalLog;
                }
            }
            
            async runSingleLevelTestImpl(level) {
                // 实现单关卡测试逻辑
                this.log(`测试关卡 ${level} 的所有数学约束...`, 'info');
                
                try {
                    const difficulty = {
                        level: level,
                        nodes: level + 1,
                        chains: level,
                        types: Math.min(level + 1, 3),
                        colors: Math.min(level + 1, 3),
                        duplicateNodes: level > 3,
                        maxPortsPerNode: Math.min(level + 1, 4),
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4)),
                        multipleSolutions: level > 2
                    };
                    
                    const scenario = generateDeterministicSolvableScenario(difficulty);
                    
                    // 运行所有验证
                    const tests = [
                        { name: '层级结构', fn: validateLayerStructureConstraints },
                        { name: 'DAG拓扑', fn: validateDAGTopologyConstraints },
                        { name: '端口平衡', fn: validatePortTypeBalanceConstraints },
                        { name: '流守恒', fn: validatePortFlowConservationConstraints },
                        { name: '连通性', fn: validateConnectivityConstraints }
                    ];
                    
                    let allPassed = true;
                    for (const test of tests) {
                        const result = test.fn(scenario);
                        const status = result.isValid ? '✅' : '❌';
                        this.log(`${status} ${test.name}约束: ${result.isValid ? '通过' : '失败'}`, result.isValid ? 'success' : 'error');
                        
                        if (!result.isValid) {
                            allPassed = false;
                            this.log(`   错误: ${result.errors.join('; ')}`, 'error');
                        }
                    }
                    
                    this.log(`\n关卡 ${level} 测试${allPassed ? '全部通过' : '存在失败'}`, allPassed ? 'success' : 'error');
                    
                } catch (error) {
                    this.log(`关卡 ${level} 测试失败: ${error.message}`, 'error');
                }
            }
            
            log(message, type = 'info') {
                const output = document.getElementById('outputContent');
                const timestamp = new Date().toLocaleTimeString();
                const colorClass = type === 'success' ? 'success' : 
                                 type === 'error' ? 'error' : 
                                 type === 'warning' ? 'warning' : 'info';
                
                output.innerHTML += `<span class="${colorClass}">[${timestamp}] ${message}</span>\n`;
                
                // 自动滚动到底部
                const container = document.getElementById('testOutput');
                container.scrollTop = container.scrollHeight;
            }
            
            clearOutput() {
                document.getElementById('outputContent').innerHTML = '';
                document.getElementById('testSummary').classList.remove('show');
            }
            
            updateButtons(disabled) {
                const buttons = document.querySelectorAll('.test-controls button');
                buttons.forEach(btn => btn.disabled = disabled);
            }
            
            updateProgress(percent) {
                document.getElementById('progressFill').style.width = `${percent}%`;
            }
            
            delay(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
        }
        
        // 初始化测试控制器
        const testController = new TestController();
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('层级DAG算法测试页面已加载');
        });
    </script>
</body>
</html>
