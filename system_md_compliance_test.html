<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SYSTEM.md Compliance Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .test-container {
            max-width: 1800px;
            margin: 0 auto;
        }
        .constraint-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .constraint-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .constraint-satisfied { border-left-color: #00ff00; }
        .constraint-violated { border-left-color: #ff0000; }
        .constraint-partial { border-left-color: #ffaa00; }
        .test-output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 11px;
        }
        .math-formula {
            background: #1a1a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: 'Times New Roman', serif;
            color: #aaccff;
            font-size: 14px;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover { background: #45a049; }
        button:disabled { background: #666; cursor: not-allowed; }
        .log-success { color: #00ff00; font-weight: bold; }
        .log-error { color: #ff0000; font-weight: bold; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
        .log-math { color: #aaccff; }
        .test-summary {
            background: #2a2a2a;
            border: 1px solid #444;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔬 SYSTEM.md Mathematical Compliance Test</h1>
        <p><strong>Objective:</strong> Verify that the Flow-First algorithm satisfies all SYSTEM.md mathematical constraints</p>
        
        <div class="math-formula">
            <strong>SYSTEM.md Mathematical Constraints:</strong><br>
            <strong>1. Port Type Balance:</strong> ∀τ ∈ Γ: |Output_τ| = |Input_τ|<br>
            <strong>2. DAG Topology:</strong> ∀(u,v) ∈ E: depth(u) < depth(v)<br>
            <strong>3. Flow Conservation:</strong> ∀v ∈ V: Σ(in_ports) = Σ(out_ports)<br>
            <strong>4. Port Mapping:</strong> ∃Φ: Output_ports → Input_ports (bijective)
        </div>

        <div class="constraint-grid">
            <div class="constraint-panel constraint-partial" id="constraint-1">
                <h3>📊 Constraint 1: Port Type Balance</h3>
                <div id="balance-status">Status: Not Tested</div>
                <div id="balance-details"></div>
            </div>
            
            <div class="constraint-panel constraint-partial" id="constraint-2">
                <h3>🔗 Constraint 2: DAG Topology</h3>
                <div id="topology-status">Status: Not Tested</div>
                <div id="topology-details"></div>
            </div>
            
            <div class="constraint-panel constraint-partial" id="constraint-3">
                <h3>🌊 Constraint 3: Flow Conservation</h3>
                <div id="conservation-status">Status: Not Tested</div>
                <div id="conservation-details"></div>
            </div>
            
            <div class="constraint-panel constraint-partial" id="constraint-4">
                <h3>🎯 Constraint 4: Port Mapping</h3>
                <div id="mapping-status">Status: Not Tested</div>
                <div id="mapping-details"></div>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runComprehensiveComplianceTest()">🧪 Run Comprehensive Compliance Test</button>
            <button onclick="testSingleConstraint(1)">📊 Test Port Balance</button>
            <button onclick="testSingleConstraint(2)">🔗 Test DAG Topology</button>
            <button onclick="testSingleConstraint(3)">🌊 Test Flow Conservation</button>
            <button onclick="testSingleConstraint(4)">🎯 Test Port Mapping</button>
            <button onclick="clearOutput()">🧹 Clear Output</button>
        </div>

        <div class="test-summary" id="test-summary" style="display: none;">
            <h2>📋 Test Summary</h2>
            <div id="summary-content"></div>
        </div>

        <div class="test-output" id="test-output"></div>
    </div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            logOutput('SYSTEM.md Compliance Test Framework loaded', 'success');
            initializeComplianceTest();
        };
        script.onerror = function() {
            logOutput('Failed to load game script', 'error');
        };
        document.head.appendChild(script);

        // Test state
        let testResults = {
            portTypeBalance: null,
            dagTopology: null,
            flowConservation: null,
            portMapping: null,
            overallCompliance: false
        };

        // Enhanced logging system
        const outputElement = document.getElementById('test-output');
        
        function logOutput(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            outputElement.innerHTML += formattedMessage;
            outputElement.scrollTop = outputElement.scrollHeight;
        }

        function initializeComplianceTest() {
            logOutput('========== SYSTEM.md MATHEMATICAL COMPLIANCE TEST ==========', 'success');
            logOutput('Testing Flow-First algorithm against strict mathematical constraints', 'info');
            logOutput('All constraints must be satisfied for 100% solvability guarantee', 'info');
        }

        async function runComprehensiveComplianceTest() {
            logOutput('========== COMPREHENSIVE COMPLIANCE TEST STARTED ==========', 'success');
            
            try {
                // Test multiple scenarios with different complexities
                const testScenarios = [
                    { wave: 1, difficulty: 1, name: 'Simple Scenario' },
                    { wave: 3, difficulty: 2, name: 'Medium Scenario' },
                    { wave: 5, difficulty: 3, name: 'Complex Scenario' },
                    { wave: 7, difficulty: 4, name: 'Advanced Scenario' },
                    { wave: 10, difficulty: 5, name: 'Maximum Scenario' }
                ];
                
                let allScenariosPassed = true;
                const scenarioResults = [];
                
                for (const scenario of testScenarios) {
                    logOutput(`Testing ${scenario.name} (Wave ${scenario.wave}, Difficulty ${scenario.difficulty})`, 'info');
                    
                    const scenarioResult = await testScenario(scenario);
                    scenarioResults.push(scenarioResult);
                    
                    if (!scenarioResult.allConstraintsSatisfied) {
                        allScenariosPassed = false;
                        logOutput(`❌ ${scenario.name} FAILED - Constraints violated`, 'error');
                    } else {
                        logOutput(`✅ ${scenario.name} PASSED - All constraints satisfied`, 'success');
                    }
                }
                
                // Generate comprehensive report
                generateComprehensiveReport(scenarioResults, allScenariosPassed);
                
            } catch (error) {
                logOutput(`Comprehensive test failed: ${error.message}`, 'error');
            }
        }

        async function testScenario(scenario) {
            try {
                // Generate node pool using Flow-First algorithm
                const requirements = generateSystemMdCompliantRequirements(scenario.wave, scenario.difficulty);
                const nodePool = generateNodesForRequirements(requirements, scenario.wave, scenario.difficulty);
                
                // Verify all SYSTEM.md constraints
                const verification = verifySystemMdConstraints(nodePool);
                
                // Update UI for this scenario
                updateConstraintPanels(verification);
                
                logOutput(`Generated ${nodePool.length} nodes for ${scenario.name}`, 'info');
                logOutput(`Constraint satisfaction: ${verification.allSatisfied ? 'PASS' : 'FAIL'}`, 
                         verification.allSatisfied ? 'success' : 'error');
                
                // Detailed constraint analysis
                logConstraintDetails(verification);
                
                return {
                    scenario: scenario.name,
                    nodeCount: nodePool.length,
                    allConstraintsSatisfied: verification.allSatisfied,
                    constraints: verification
                };
                
            } catch (error) {
                logOutput(`Scenario ${scenario.name} failed: ${error.message}`, 'error');
                return {
                    scenario: scenario.name,
                    nodeCount: 0,
                    allConstraintsSatisfied: false,
                    error: error.message
                };
            }
        }

        function logConstraintDetails(verification) {
            logOutput('=== DETAILED CONSTRAINT ANALYSIS ===', 'math');
            
            // Port Type Balance
            logOutput(`Port Type Balance: ${verification.portTypeBalance.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                     verification.portTypeBalance.satisfied ? 'success' : 'error');
            if (!verification.portTypeBalance.satisfied) {
                verification.portTypeBalance.imbalances.forEach(imbalance => {
                    logOutput(`  - Type ${imbalance.type}: ${imbalance.output} outputs, ${imbalance.input} inputs (diff: ${imbalance.difference})`, 'warn');
                });
            }
            
            // DAG Topology
            logOutput(`DAG Topology: ${verification.dagTopology.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                     verification.dagTopology.satisfied ? 'success' : 'error');
            if (!verification.dagTopology.satisfied) {
                verification.dagTopology.violations.forEach(violation => {
                    logOutput(`  - ${violation}`, 'warn');
                });
            }
            
            // Flow Conservation
            logOutput(`Flow Conservation: ${verification.flowConservation.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                     verification.flowConservation.satisfied ? 'success' : 'error');
            if (!verification.flowConservation.satisfied) {
                verification.flowConservation.violations.forEach(violation => {
                    logOutput(`  - ${violation}`, 'warn');
                });
            }
            
            // Port Mapping
            logOutput(`Port Mapping: ${verification.portMapping.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                     verification.portMapping.satisfied ? 'success' : 'error');
            if (!verification.portMapping.satisfied) {
                verification.portMapping.issues.forEach(issue => {
                    logOutput(`  - ${issue}`, 'warn');
                });
            }
        }

        function updateConstraintPanels(verification) {
            // Update constraint 1: Port Type Balance
            const panel1 = document.getElementById('constraint-1');
            panel1.className = `constraint-panel ${verification.portTypeBalance.satisfied ? 'constraint-satisfied' : 'constraint-violated'}`;
            document.getElementById('balance-status').textContent = 
                `Status: ${verification.portTypeBalance.satisfied ? 'SATISFIED ✅' : 'VIOLATED ❌'}`;
            document.getElementById('balance-details').innerHTML = 
                `Imbalances: ${verification.portTypeBalance.imbalances.length}`;
            
            // Update constraint 2: DAG Topology
            const panel2 = document.getElementById('constraint-2');
            panel2.className = `constraint-panel ${verification.dagTopology.satisfied ? 'constraint-satisfied' : 'constraint-violated'}`;
            document.getElementById('topology-status').textContent = 
                `Status: ${verification.dagTopology.satisfied ? 'SATISFIED ✅' : 'VIOLATED ❌'}`;
            document.getElementById('topology-details').innerHTML = 
                `Max Depth: ${verification.dagTopology.maxDepth}, Violations: ${verification.dagTopology.violations.length}`;
            
            // Update constraint 3: Flow Conservation
            const panel3 = document.getElementById('constraint-3');
            panel3.className = `constraint-panel ${verification.flowConservation.satisfied ? 'constraint-satisfied' : 'constraint-violated'}`;
            document.getElementById('conservation-status').textContent = 
                `Status: ${verification.flowConservation.satisfied ? 'SATISFIED ✅' : 'VIOLATED ❌'}`;
            document.getElementById('conservation-details').innerHTML = 
                `Violations: ${verification.flowConservation.violations.length}`;
            
            // Update constraint 4: Port Mapping
            const panel4 = document.getElementById('constraint-4');
            panel4.className = `constraint-panel ${verification.portMapping.satisfied ? 'constraint-satisfied' : 'constraint-violated'}`;
            document.getElementById('mapping-status').textContent = 
                `Status: ${verification.portMapping.satisfied ? 'SATISFIED ✅' : 'VIOLATED ❌'}`;
            document.getElementById('mapping-details').innerHTML = 
                `Outputs: ${verification.portMapping.outputCount}, Inputs: ${verification.portMapping.inputCount}`;
        }

        function generateComprehensiveReport(scenarioResults, allPassed) {
            logOutput('========== COMPREHENSIVE COMPLIANCE REPORT ==========', 'success');
            
            const passedScenarios = scenarioResults.filter(r => r.allConstraintsSatisfied).length;
            const totalScenarios = scenarioResults.length;
            
            logOutput(`Overall Result: ${allPassed ? 'PASS' : 'FAIL'}`, allPassed ? 'success' : 'error');
            logOutput(`Scenarios Passed: ${passedScenarios}/${totalScenarios}`, 'info');
            
            if (allPassed) {
                logOutput('🎉 SYSTEM.md MATHEMATICAL COMPLIANCE ACHIEVED! 🎉', 'success');
                logOutput('✅ All constraints satisfied across all test scenarios', 'success');
                logOutput('✅ 100% solvability guarantee mathematically proven', 'success');
                logOutput('✅ Flow-First algorithm is SYSTEM.md compliant', 'success');
            } else {
                logOutput('⚠️ SYSTEM.md compliance not fully achieved', 'warn');
                logOutput('Some constraints require additional fixes', 'warn');
            }
            
            // Show summary panel
            const summaryPanel = document.getElementById('test-summary');
            const summaryContent = document.getElementById('summary-content');
            
            summaryContent.innerHTML = `
                <h3>${allPassed ? '✅ COMPLIANCE ACHIEVED' : '❌ COMPLIANCE INCOMPLETE'}</h3>
                <p><strong>Scenarios Passed:</strong> ${passedScenarios}/${totalScenarios}</p>
                <p><strong>Mathematical Guarantee:</strong> ${allPassed ? '100% Solvability' : 'Partial Solvability'}</p>
                <p><strong>SYSTEM.md Status:</strong> ${allPassed ? 'Fully Compliant' : 'Requires Fixes'}</p>
            `;
            
            summaryPanel.style.display = 'block';
        }

        function testSingleConstraint(constraintNumber) {
            logOutput(`Testing individual constraint ${constraintNumber}...`, 'info');
            
            try {
                const requirements = generateSystemMdCompliantRequirements(3, 2);
                const nodePool = generateNodesForRequirements(requirements, 3, 2);
                const verification = verifySystemMdConstraints(nodePool);
                
                switch (constraintNumber) {
                    case 1:
                        logOutput(`Port Type Balance: ${verification.portTypeBalance.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                                 verification.portTypeBalance.satisfied ? 'success' : 'error');
                        break;
                    case 2:
                        logOutput(`DAG Topology: ${verification.dagTopology.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                                 verification.dagTopology.satisfied ? 'success' : 'error');
                        break;
                    case 3:
                        logOutput(`Flow Conservation: ${verification.flowConservation.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                                 verification.flowConservation.satisfied ? 'success' : 'error');
                        break;
                    case 4:
                        logOutput(`Port Mapping: ${verification.portMapping.satisfied ? 'SATISFIED' : 'VIOLATED'}`, 
                                 verification.portMapping.satisfied ? 'success' : 'error');
                        break;
                }
                
                updateConstraintPanels(verification);
                
            } catch (error) {
                logOutput(`Single constraint test failed: ${error.message}`, 'error');
            }
        }

        function clearOutput() {
            document.getElementById('test-output').innerHTML = '';
            document.getElementById('test-summary').style.display = 'none';
        }

        // Initialize
        logOutput('SYSTEM.md Mathematical Compliance Test Framework initialized', 'success');
    </script>
</body>
</html>
