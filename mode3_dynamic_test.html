<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Dynamic Topology Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .dynamic-info {
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .info-item {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #00aaff;
        }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #ffaa00;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .area-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
            border: 1px solid #555;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .btn.warning {
            background: #ff9800;
        }
        
        .btn.warning:hover {
            background: #f57c00;
        }
        
        .modification-panel {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .mod-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .validation-status {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .status-good {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .status-error {
            color: #f44336;
            font-weight: bold;
        }
        
        .status-warning {
            color: #ff9800;
            font-weight: bold;
        }
        
        .topology-analysis {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .analysis-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .analysis-item {
            background: #2a2a3a;
            padding: 10px;
            border-radius: 5px;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Mode 3 Dynamic Topology Test</h1>
            <p>动态拓扑生成 - 支持增删改、按端口类型防环、非固定深度</p>
        </div>
        
        <div class="dynamic-info">
            <h3>📊 Dynamic Topology Status</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div>Current Wave</div>
                    <div class="info-value" id="current-wave">1</div>
                </div>
                <div class="info-item">
                    <div>Modifications</div>
                    <div class="info-value" id="modification-count">0</div>
                </div>
                <div class="info-item">
                    <div>Temp Nodes</div>
                    <div class="info-value" id="temp-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Placed Nodes</div>
                    <div class="info-value" id="placed-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Connections</div>
                    <div class="info-value" id="connections">0</div>
                </div>
                <div class="info-item">
                    <div>Validation Status</div>
                    <div class="info-value status-good" id="validation-status">Valid</div>
                </div>
            </div>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="area-header" style="color: #ffaa00;">🔄 Dynamic Node Pool</div>
                <canvas id="temporaryCanvas" width="280" height="400"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="area-header" style="color: #00aaff;">🏗️ Topology Construction Area</div>
                <canvas id="gameCanvas" width="680" height="400"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="nextDynamicWave()">🌊 Next Wave (N)</button>
            <button class="btn" onclick="regenerateFromModifiedState()">🔄 Regenerate (R)</button>
            <button class="btn warning" onclick="showModificationMenu()">⚙️ Modify (M)</button>
            <button class="btn" onclick="analyzeTopology()">🔍 Analyze</button>
            <button class="btn danger" onclick="resetDynamicMode()">🔄 Reset</button>
        </div>
        
        <div class="modification-panel">
            <h3>🛠️ Dynamic Modifications</h3>
            <div class="mod-controls">
                <button class="btn" onclick="addRandomPort()">➕ Add Random Port</button>
                <button class="btn" onclick="removeRandomPort()">➖ Remove Random Port</button>
                <button class="btn" onclick="modifyRandomPort()">🔧 Modify Random Port</button>
                <button class="btn danger" onclick="deleteRandomNode()">🗑️ Delete Random Node</button>
                <button class="btn" onclick="addRandomNode()">➕ Add Random Node</button>
                <button class="btn warning" onclick="scrambleTopology()">🎲 Scramble Topology</button>
            </div>
        </div>
        
        <div class="validation-status">
            <h3>✅ Validation & Constraints</h3>
            <div id="validation-details">
                <div>Port Type Balance: <span class="status-good">PASS</span></div>
                <div>Acyclic by Port Type: <span class="status-good">PASS</span></div>
                <div>Dynamic Depth Consistency: <span class="status-good">PASS</span></div>
                <div>Topology Connectivity: <span class="status-good">PASS</span></div>
            </div>
        </div>
        
        <div class="topology-analysis">
            <h3>🔍 Topology Analysis</h3>
            <div class="analysis-grid">
                <div class="analysis-item">
                    <h4>Port Type Distribution</h4>
                    <div id="port-type-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Depth Distribution</h4>
                    <div id="depth-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Connectivity Patterns</h4>
                    <div id="connectivity-analysis">Loading...</div>
                </div>
                <div class="analysis-item">
                    <h4>Expansion Opportunities</h4>
                    <div id="expansion-analysis">Loading...</div>
                </div>
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 Dynamic Topology Instructions</h3>
            <ul>
                <li><strong>Dynamic Generation:</strong> Nodes are generated based on existing topology, not from scratch</li>
                <li><strong>Non-Fixed Depth:</strong> Node depths adjust dynamically as topology changes</li>
                <li><strong>Port-Type Acyclic:</strong> Each port type maintains its own acyclic constraint</li>
                <li><strong>Incremental Modifications:</strong> Add/remove/modify nodes and ports dynamically</li>
                <li><strong>Continuous Validation:</strong> Real-time validation of mathematical constraints</li>
                <li><strong>Randomness Preservation:</strong> Maintains randomness while respecting constraints</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>⌨️ Keyboard Shortcuts</h3>
            <ul>
                <li><strong>N:</strong> Next wave (generate new nodes)</li>
                <li><strong>R:</strong> Regenerate from current state</li>
                <li><strong>M:</strong> Show modification menu</li>
                <li><strong>A:</strong> Analyze current topology</li>
                <li><strong>1-6:</strong> Quick modifications</li>
            </ul>
        </div>
        
        <div>
            <h3>📋 Dynamic Topology Log</h3>
            <div class="log" id="dynamic-log"></div>
        </div>
    </div>

    <!-- Load scripts in dependency order -->
    <script src="dynamic_topology_engine.js"></script>
    <script src="mode3_dynamic.js"></script>
    
    <script>
        // Logging system
        function logDynamic(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('dynamic-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[DYNAMIC] ${message}`);
        }
        
        // UI update functions
        function updateDynamicStatus() {
            if (typeof mode3DynamicState !== 'undefined') {
                document.getElementById('current-wave').textContent = mode3DynamicState.currentWave;
                document.getElementById('modification-count').textContent = mode3DynamicState.modificationHistory.length;
                document.getElementById('temp-nodes').textContent = mode3DynamicState.temporaryNodes.length;
                document.getElementById('placed-nodes').textContent = mode3DynamicState.placedNodes.length;
                document.getElementById('connections').textContent = mode3DynamicState.connections.length;
            }
        }
        
        // Modification functions
        function addRandomPort() {
            const allNodes = [...mode3DynamicState.temporaryNodes, ...mode3DynamicState.placedNodes];
            if (allNodes.length === 0) return;
            
            const randomNode = allNodes[Math.floor(Math.random() * allNodes.length)];
            const portSpec = {
                type: ['square', 'circle', 'triangle', 'diamond'][Math.floor(Math.random() * 4)],
                color: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'][Math.floor(Math.random() * 4)],
                side: Math.random() < 0.5 ? 'input' : 'output'
            };
            
            performNodeModification('add_port', randomNode.id, { portSpec });
            logDynamic(`Added ${portSpec.side} port (${portSpec.type}, ${portSpec.color}) to node ${randomNode.id}`, 'success');
        }
        
        function removeRandomPort() {
            const allNodes = [...mode3DynamicState.temporaryNodes, ...mode3DynamicState.placedNodes];
            const nodesWithPorts = allNodes.filter(node => 
                (node.inputPorts && node.inputPorts.length > 0) || 
                (node.outputPorts && node.outputPorts.length > 0)
            );
            
            if (nodesWithPorts.length === 0) return;
            
            const randomNode = nodesWithPorts[Math.floor(Math.random() * nodesWithPorts.length)];
            const allPorts = [...(randomNode.inputPorts || []), ...(randomNode.outputPorts || [])];
            
            if (allPorts.length > 0) {
                const randomPort = allPorts[Math.floor(Math.random() * allPorts.length)];
                performNodeModification('remove_port', randomNode.id, { portId: randomPort.id });
                logDynamic(`Removed port ${randomPort.id} from node ${randomNode.id}`, 'warn');
            }
        }
        
        function modifyRandomPort() {
            const allNodes = [...mode3DynamicState.temporaryNodes, ...mode3DynamicState.placedNodes];
            const nodesWithPorts = allNodes.filter(node => 
                (node.inputPorts && node.inputPorts.length > 0) || 
                (node.outputPorts && node.outputPorts.length > 0)
            );
            
            if (nodesWithPorts.length === 0) return;
            
            const randomNode = nodesWithPorts[Math.floor(Math.random() * nodesWithPorts.length)];
            const allPorts = [...(randomNode.inputPorts || []), ...(randomNode.outputPorts || [])];
            
            if (allPorts.length > 0) {
                const randomPort = allPorts[Math.floor(Math.random() * allPorts.length)];
                const newSpec = {
                    type: ['square', 'circle', 'triangle', 'diamond'][Math.floor(Math.random() * 4)],
                    color: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'][Math.floor(Math.random() * 4)]
                };
                
                performNodeModification('modify_port', randomNode.id, { portId: randomPort.id, newSpec });
                logDynamic(`Modified port ${randomPort.id} in node ${randomNode.id}`, 'info');
            }
        }
        
        function deleteRandomNode() {
            const allNodes = [...mode3DynamicState.temporaryNodes, ...mode3DynamicState.placedNodes];
            const deletableNodes = allNodes.filter(node => node.type === 'intermediate');
            
            if (deletableNodes.length === 0) {
                logDynamic('No deletable nodes available', 'warn');
                return;
            }
            
            const randomNode = deletableNodes[Math.floor(Math.random() * deletableNodes.length)];
            performNodeModification('delete_node', randomNode.id, {});
            logDynamic(`Deleted node ${randomNode.id}`, 'error');
        }
        
        function addRandomNode() {
            // This would trigger the dynamic topology engine to add a new node
            logDynamic('Adding random node via topology expansion', 'info');
            nextDynamicWave();
        }
        
        function scrambleTopology() {
            logDynamic('Scrambling topology with multiple random modifications', 'warn');
            
            // Perform multiple random modifications
            for (let i = 0; i < 3; i++) {
                const operations = [addRandomPort, removeRandomPort, modifyRandomPort];
                const randomOp = operations[Math.floor(Math.random() * operations.length)];
                setTimeout(randomOp, i * 500);
            }
        }
        
        function analyzeTopology() {
            logDynamic('Analyzing current topology structure', 'info');
            
            const allNodes = [...mode3DynamicState.temporaryNodes, ...mode3DynamicState.placedNodes];
            
            // Port type analysis
            const portTypes = new Map();
            allNodes.forEach(node => {
                [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
                    const key = `${port.type}-${port.color}`;
                    portTypes.set(key, (portTypes.get(key) || 0) + 1);
                });
            });
            
            document.getElementById('port-type-analysis').innerHTML = 
                Array.from(portTypes.entries()).map(([type, count]) => `${type}: ${count}`).join('<br>');
            
            // Depth analysis
            const depths = new Map();
            allNodes.forEach(node => {
                const depth = node.depth || 0;
                depths.set(depth, (depths.get(depth) || 0) + 1);
            });
            
            document.getElementById('depth-analysis').innerHTML = 
                Array.from(depths.entries()).sort((a, b) => a[0] - b[0]).map(([depth, count]) => `Depth ${depth}: ${count} nodes`).join('<br>');
            
            // Connectivity analysis
            const connections = mode3DynamicState.connections.length;
            const maxPossibleConnections = allNodes.reduce((sum, node) => 
                sum + Math.min((node.inputPorts || []).length, (node.outputPorts || []).length), 0);
            
            document.getElementById('connectivity-analysis').innerHTML = 
                `Connections: ${connections}<br>Max Possible: ${maxPossibleConnections}<br>Utilization: ${((connections / Math.max(maxPossibleConnections, 1)) * 100).toFixed(1)}%`;
            
            // Expansion opportunities
            const isolatedNodes = allNodes.filter(node => {
                const hasConnections = mode3DynamicState.connections.some(conn => 
                    conn.fromNode === node.id || conn.toNode === node.id);
                return !hasConnections;
            });
            
            document.getElementById('expansion-analysis').innerHTML = 
                `Isolated Nodes: ${isolatedNodes.length}<br>Expansion Potential: ${isolatedNodes.length > 0 ? 'High' : 'Low'}`;
        }
        
        function resetDynamicMode() {
            if (confirm('Reset dynamic topology mode? This will clear all modifications.')) {
                location.reload();
            }
        }
        
        // Auto-update status
        setInterval(updateDynamicStatus, 1000);
        
        // Auto-analyze topology
        setInterval(analyzeTopology, 5000);
        
        // Initial setup
        setTimeout(() => {
            updateDynamicStatus();
            logDynamic('Dynamic topology test framework loaded', 'success');
            
            if (typeof mode3DynamicState !== 'undefined') {
                logDynamic('Mode 3 dynamic state initialized', 'success');
            } else {
                logDynamic('Mode 3 dynamic state not found', 'error');
            }
        }, 1000);
        
        // Additional keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            switch(event.key) {
                case 'a':
                case 'A':
                    analyzeTopology();
                    break;
                case '1':
                    addRandomPort();
                    break;
                case '2':
                    removeRandomPort();
                    break;
                case '3':
                    modifyRandomPort();
                    break;
                case '4':
                    deleteRandomNode();
                    break;
                case '5':
                    addRandomNode();
                    break;
                case '6':
                    scrambleTopology();
                    break;
            }
        });
    </script>
</body>
</html>
