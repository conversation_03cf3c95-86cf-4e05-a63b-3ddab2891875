<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Conflict-Free Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .conflict-free-info {
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .info-item {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
            color: #00aaff;
        }
        
        .status-good { color: #4CAF50; }
        .status-error { color: #f44336; }
        .status-warning { color: #ff9800; }
        
        .game-areas {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .temporary-area {
            flex: 0 0 300px;
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 10px;
        }
        
        .placement-area {
            flex: 1;
            background: #2a2a2a;
            border: 2px solid #00aaff;
            border-radius: 10px;
            padding: 10px;
        }
        
        .area-header {
            text-align: center;
            font-size: 16px;
            margin-bottom: 10px;
        }
        
        canvas {
            background: #333;
            border-radius: 5px;
            cursor: crosshair;
            border: 1px solid #555;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .btn.warning {
            background: #ff9800;
        }
        
        .btn.warning:hover {
            background: #f57c00;
        }
        
        .conflict-resolution-panel {
            background: #2a2a2a;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .resolution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .resolution-item {
            background: #1a3a1a;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #4CAF50;
        }
        
        .algorithm-explanation {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .algorithm-step {
            background: #2a2a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #00aaff;
        }
        
        .validation-results {
            background: #1a3a1a;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .validation-item {
            background: #2a4a2a;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .instructions {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 150px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>✅ Mode 3 Conflict-Free Test</h1>
            <p>冲突解决算法 - 虚拟DAG分离、智能节点分配、零冲突保证</p>
        </div>
        
        <div class="conflict-free-info">
            <h3>📊 Conflict-Free Status</h3>
            <div class="info-grid">
                <div class="info-item">
                    <div>Current Wave</div>
                    <div class="info-value" id="current-wave">1</div>
                </div>
                <div class="info-item">
                    <div>Conflict Status</div>
                    <div class="info-value status-good" id="conflict-free-status">All Conflict-Free</div>
                </div>
                <div class="info-item">
                    <div>Resolutions Applied</div>
                    <div class="info-value" id="resolution-count">0</div>
                </div>
                <div class="info-item">
                    <div>Temp Nodes</div>
                    <div class="info-value" id="temp-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Placed Nodes</div>
                    <div class="info-value" id="placed-nodes">0</div>
                </div>
                <div class="info-item">
                    <div>Valid DAGs</div>
                    <div class="info-value status-good" id="valid-dags">All Valid</div>
                </div>
            </div>
        </div>
        
        <div class="game-areas">
            <div class="temporary-area">
                <div class="area-header" style="color: #4CAF50;">✅ Conflict-Free Node Pool</div>
                <canvas id="temporaryCanvas" width="280" height="400"></canvas>
            </div>
            
            <div class="placement-area">
                <div class="area-header" style="color: #00aaff;">🏗️ Validated Construction Area</div>
                <canvas id="gameCanvas" width="680" height="400"></canvas>
            </div>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="nextConflictFreeWave()">🌊 Next Wave (N)</button>
            <button class="btn" onclick="validateConflictFreeState()">✅ Validate (V)</button>
            <button class="btn" onclick="regenerateConflictFreeNodes()">🔄 Regenerate (R)</button>
            <button class="btn" onclick="analyzeConflictResolution()">📊 Analyze (A)</button>
            <button class="btn warning" onclick="testConflictScenarios()">⚠️ Test Conflicts</button>
            <button class="btn danger" onclick="resetConflictFreeMode()">🔄 Reset</button>
        </div>
        
        <div class="conflict-resolution-panel">
            <h3>🔧 Conflict Resolution Strategies</h3>
            <div class="resolution-grid">
                <div class="resolution-item">
                    <h4>Node Separation</h4>
                    <div id="separation-strategy">
                        Conflicting port types are allocated to separate nodes to avoid self-connection risks.
                    </div>
                </div>
                <div class="resolution-item">
                    <h4>Role Specialization</h4>
                    <div id="specialization-strategy">
                        Nodes are specialized for specific roles (input-only or output-only) to prevent conflicts.
                    </div>
                </div>
                <div class="resolution-item">
                    <h4>Depth Adjustment</h4>
                    <div id="depth-strategy">
                        Node depths are adjusted to maintain DAG properties while resolving conflicts.
                    </div>
                </div>
            </div>
        </div>
        
        <div class="algorithm-explanation">
            <h3>🧠 Algorithm Deep Dive</h3>
            <div class="algorithm-step">
                <h4>Step 1: Virtual DAG Generation</h4>
                <p>For each port type, generate an independent virtual DAG structure with ideal node roles and connections.</p>
            </div>
            <div class="algorithm-step">
                <h4>Step 2: Conflict Analysis</h4>
                <p>Analyze potential conflicts between port types when they need to share the same physical node.</p>
            </div>
            <div class="algorithm-step">
                <h4>Step 3: Separation Strategy</h4>
                <p>Apply node separation for conflicting port types to ensure no self-connection risks.</p>
            </div>
            <div class="algorithm-step">
                <h4>Step 4: Merging Strategy</h4>
                <p>Merge compatible port types into shared nodes to optimize node count while maintaining safety.</p>
            </div>
            <div class="algorithm-step">
                <h4>Step 5: Final Validation</h4>
                <p>Validate that no node has the same port type for both input and output, ensuring DAG properties.</p>
            </div>
        </div>
        
        <div class="validation-results">
            <h3>✅ Validation Results</h3>
            <div id="validation-details">
                Loading validation results...
            </div>
        </div>
        
        <div class="instructions">
            <h3>🎮 Conflict-Free Algorithm Features</h3>
            <ul>
                <li><strong>Virtual DAG Separation:</strong> Each port type gets its own ideal DAG structure first</li>
                <li><strong>Conflict Detection:</strong> Automatic detection of port types that would conflict on same node</li>
                <li><strong>Smart Allocation:</strong> Intelligent assignment of port types to physical nodes</li>
                <li><strong>Zero Tolerance:</strong> Absolutely no self-connection configurations allowed</li>
                <li><strong>DAG Preservation:</strong> Each port type maintains perfect DAG properties</li>
                <li><strong>Visual Validation:</strong> Green checkmarks indicate conflict-free nodes</li>
            </ul>
        </div>
        
        <div class="instructions">
            <h3>🔬 Technical Innovations</h3>
            <ul>
                <li><strong>Virtual-Physical Mapping:</strong> Two-stage design separates logical requirements from physical constraints</li>
                <li><strong>Conflict Matrix:</strong> Pre-computed compatibility matrix for all port type pairs</li>
                <li><strong>Resolution Strategies:</strong> Multiple fallback strategies for different conflict scenarios</li>
                <li><strong>Incremental Validation:</strong> Real-time validation during node allocation process</li>
                <li><strong>Rollback Capability:</strong> Automatic rollback when conflicts cannot be resolved</li>
            </ul>
        </div>
        
        <div>
            <h3>📋 Conflict-Free Log</h3>
            <div class="log" id="conflict-free-log"></div>
        </div>
    </div>

    <!-- Load scripts in dependency order -->
    <script src="port_dag_conflict_resolver.js"></script>
    <script src="mode3_conflict_free.js"></script>
    
    <script>
        // Logging system
        function logConflictFree(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('conflict-free-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[CONFLICT-FREE] ${message}`);
        }
        
        // UI update functions
        function updateConflictFreeStatus() {
            if (typeof mode3ConflictFreeState !== 'undefined') {
                document.getElementById('current-wave').textContent = mode3ConflictFreeState.currentWave;
                document.getElementById('resolution-count').textContent = mode3ConflictFreeState.conflictResolutions.length;
                document.getElementById('temp-nodes').textContent = mode3ConflictFreeState.temporaryNodes.length;
                document.getElementById('placed-nodes').textContent = mode3ConflictFreeState.placedNodes.length;
            }
        }
        
        function updateValidationResults() {
            if (typeof mode3ConflictFreeState !== 'undefined' && mode3ConflictFreeState.portTypeValidations) {
                const validationItems = [];
                
                mode3ConflictFreeState.portTypeValidations.forEach((result, portType) => {
                    const statusClass = result.isDAG ? 'status-good' : 'status-error';
                    const selfLoopInfo = result.selfLoopNodes.size > 0 ? ` (${result.selfLoopNodes.size} self-loops)` : '';
                    
                    validationItems.push(`<div class="validation-item">
                        <span class="${statusClass}">${portType}</span>: 
                        ${result.nodeCount} nodes, ${result.edgeCount} edges
                        ${selfLoopInfo}
                        ${result.isDAG ? ' ✅' : ' ❌'}
                    </div>`);
                });
                
                document.getElementById('validation-details').innerHTML = 
                    validationItems.length > 0 ? validationItems.join('') : 'No validations available';
            }
        }
        
        // Test functions
        function testConflictScenarios() {
            logConflictFree('Testing conflict scenarios', 'info');
            
            // Test scenario: Try to create a node with conflicting port types
            const conflictNode = {
                id: 'test_conflict_node',
                type: 'intermediate',
                depth: 1,
                inputPorts: [
                    { id: 'conflict_in', type: 'square', color: 'red', side: 'input', portTypeKey: 'square-red' }
                ],
                outputPorts: [
                    { id: 'conflict_out', type: 'square', color: 'red', side: 'output', portTypeKey: 'square-red' }
                ],
                x: 150,
                y: 200,
                area: 'temporary',
                conflictFree: false
            };
            
            logConflictFree('Created test node with intentional conflict (square-red input and output)', 'warn');
            
            // The conflict resolver should detect and prevent this
            const validation = validateNodeConflictFree(conflictNode);
            
            if (!validation.isConflictFree) {
                logConflictFree(`Conflict detected as expected: ${validation.conflicts[0].description}`, 'success');
            } else {
                logConflictFree('ERROR: Conflict not detected!', 'error');
            }
        }
        
        function resetConflictFreeMode() {
            if (confirm('Reset conflict-free mode? This will clear all nodes and restart.')) {
                location.reload();
            }
        }
        
        // Auto-update functions
        setInterval(updateConflictFreeStatus, 1000);
        setInterval(updateValidationResults, 2000);
        
        // Initial setup
        setTimeout(() => {
            updateConflictFreeStatus();
            updateValidationResults();
            logConflictFree('Conflict-free test framework loaded', 'success');
            
            if (typeof mode3ConflictFreeState !== 'undefined') {
                logConflictFree('Mode 3 conflict-free state initialized', 'success');
                
                // Test the conflict resolver
                if (mode3ConflictFreeState.conflictResolver) {
                    logConflictFree('PortDAGConflictResolver loaded and ready', 'success');
                } else {
                    logConflictFree('PortDAGConflictResolver not found', 'error');
                }
            } else {
                logConflictFree('Mode 3 conflict-free state not found', 'error');
            }
        }, 1000);
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            switch(event.key.toLowerCase()) {
                case 't':
                    testConflictScenarios();
                    break;
            }
        });
        
        // Demonstrate the algorithm
        setTimeout(() => {
            logConflictFree('=== ALGORITHM DEMONSTRATION ===', 'info');
            logConflictFree('1. Virtual DAGs created for each port type', 'info');
            logConflictFree('2. Conflict analysis identifies potential self-loops', 'info');
            logConflictFree('3. Separation strategy applied for conflicting types', 'info');
            logConflictFree('4. Compatible types merged to optimize node count', 'info');
            logConflictFree('5. Final validation ensures zero conflicts', 'success');
        }, 2000);
    </script>
</body>
</html>
