// ========== Mode 3 Minimal Working Implementation ==========

// Global game state
const gameState = {
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    
    // Canvas references
    temporaryCanvas: null,
    temporaryCtx: null,
    gameCanvas: null,
    gameCtx: null,
    
    // Interaction state
    draggingNode: null,
    selectedPort: null,
    creatingConnection: null,
    
    // Port types
    portTypes: ['square', 'circle', 'triangle', 'diamond'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107']
};

// ========== Initialization ==========

function initializeGame() {
    console.log('[MODE3] Initializing minimal Mode 3 game');
    
    // Get canvas elements
    gameState.temporaryCanvas = document.getElementById('temporaryCanvas');
    gameState.gameCanvas = document.getElementById('gameCanvas');
    
    if (!gameState.temporaryCanvas || !gameState.gameCanvas) {
        console.error('[MODE3] Canvas elements not found');
        return false;
    }
    
    gameState.temporaryCtx = gameState.temporaryCanvas.getContext('2d');
    gameState.gameCtx = gameState.gameCanvas.getContext('2d');
    
    // Setup event listeners
    setupEventListeners();
    
    // Generate initial nodes
    generateInitialNodes();
    
    // Start rendering
    updateDisplay();
    
    console.log('[MODE3] Game initialized successfully');
    return true;
}

function setupEventListeners() {
    // Mouse events for both canvases
    gameState.temporaryCanvas.addEventListener('mousedown', handleMouseDown);
    gameState.temporaryCanvas.addEventListener('mousemove', handleMouseMove);
    gameState.temporaryCanvas.addEventListener('mouseup', handleMouseUp);
    
    gameState.gameCanvas.addEventListener('mousedown', handleMouseDown);
    gameState.gameCanvas.addEventListener('mousemove', handleMouseMove);
    gameState.gameCanvas.addEventListener('mouseup', handleMouseUp);
    
    console.log('[MODE3] Event listeners setup');
}

// ========== Node Generation ==========

function generateInitialNodes() {
    console.log('[MODE3] Generating initial test nodes');
    
    // Clear existing nodes
    gameState.temporaryNodes = [];
    gameState.placedNodes = [];
    gameState.connections = [];
    
    // Create simple test nodes
    const testNodes = [
        {
            id: 'start1',
            type: 'start',
            x: 50,
            y: 100,
            inputPorts: [],
            outputPorts: [
                { id: 'start1_out1', type: 'square', color: '#ff5252', x: 0, y: 0 }
            ]
        },
        {
            id: 'middle1',
            type: 'intermediate',
            x: 50,
            y: 200,
            inputPorts: [
                { id: 'middle1_in1', type: 'square', color: '#ff5252', x: 0, y: 0 }
            ],
            outputPorts: [
                { id: 'middle1_out1', type: 'circle', color: '#2196F3', x: 0, y: 0 }
            ]
        },
        {
            id: 'end1',
            type: 'end',
            x: 50,
            y: 300,
            inputPorts: [
                { id: 'end1_in1', type: 'circle', color: '#2196F3', x: 0, y: 0 }
            ],
            outputPorts: []
        }
    ];
    
    gameState.temporaryNodes = testNodes;
    console.log('[MODE3] Generated', testNodes.length, 'test nodes');
}

// ========== Rendering System ==========

function updateDisplay() {
    clearCanvases();
    drawNodes();
    drawConnections();
}

function clearCanvases() {
    if (gameState.temporaryCtx) {
        gameState.temporaryCtx.clearRect(0, 0, gameState.temporaryCanvas.width, gameState.temporaryCanvas.height);
    }
    if (gameState.gameCtx) {
        gameState.gameCtx.clearRect(0, 0, gameState.gameCanvas.width, gameState.gameCanvas.height);
    }
}

function drawNodes() {
    // Draw temporary nodes
    gameState.temporaryNodes.forEach(node => {
        drawNode(node, gameState.temporaryCtx);
    });
    
    // Draw placed nodes
    gameState.placedNodes.forEach(node => {
        drawNode(node, gameState.gameCtx);
    });
}

function drawNode(node, ctx) {
    const nodeSize = 60;
    const portSize = 12;
    
    // Draw node body
    ctx.fillStyle = getNodeColor(node.type);
    ctx.fillRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // Draw node border
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.strokeRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // Draw input ports (left side)
    if (node.inputPorts && node.inputPorts.length > 0) {
        const spacing = nodeSize / (node.inputPorts.length + 1);
        node.inputPorts.forEach((port, index) => {
            const portX = node.x - nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            // Update port position
            port.x = portX;
            port.y = portY;
            
            drawPort(ctx, portX, portY, port, portSize);
        });
    }
    
    // Draw output ports (right side)
    if (node.outputPorts && node.outputPorts.length > 0) {
        const spacing = nodeSize / (node.outputPorts.length + 1);
        node.outputPorts.forEach((port, index) => {
            const portX = node.x + nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            // Update port position
            port.x = portX;
            port.y = portY;
            
            drawPort(ctx, portX, portY, port, portSize);
        });
    }
    
    // Draw node label
    ctx.fillStyle = 'white';
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.id, node.x, node.y + 4);
}

function drawPort(ctx, x, y, port, size) {
    ctx.fillStyle = port.color;
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 1;
    
    switch (port.type) {
        case 'square':
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.strokeRect(x - size/2, y - size/2, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x - size/2, y + size/2);
            ctx.lineTo(x + size/2, y + size/2);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x + size/2, y);
            ctx.lineTo(x, y + size/2);
            ctx.lineTo(x - size/2, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
}

function getNodeColor(type) {
    switch (type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        case 'intermediate': return '#2196F3';
        default: return '#666';
    }
}

function drawConnections() {
    gameState.connections.forEach(connection => {
        const fromNode = findNodeById(connection.fromNode);
        const toNode = findNodeById(connection.toNode);
        
        if (fromNode && toNode) {
            const fromPort = findPortInNode(fromNode, connection.fromPort);
            const toPort = findPortInNode(toNode, connection.toPort);
            
            if (fromPort && toPort) {
                drawConnectionLine(gameState.gameCtx, fromPort, toPort);
            }
        }
    });
}

function drawConnectionLine(ctx, fromPort, toPort) {
    ctx.strokeStyle = fromPort.color;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(fromPort.x, fromPort.y);
    ctx.lineTo(toPort.x, toPort.y);
    ctx.stroke();
}

// ========== Event Handling ==========

function handleMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const isTemporaryArea = event.target === gameState.temporaryCanvas;
    
    // Check for port click first
    const clickedPort = findPortAtPosition(x, y, isTemporaryArea);
    if (clickedPort) {
        handlePortClick(clickedPort);
        return;
    }
    
    // Check for node click
    const clickedNode = findNodeAtPosition(x, y, isTemporaryArea);
    if (clickedNode) {
        startNodeDrag(clickedNode, x, y);
    }
}

function handleMouseMove(event) {
    if (gameState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        gameState.draggingNode.node.x = x;
        gameState.draggingNode.node.y = y;
        updateDisplay();
    }
}

function handleMouseUp(event) {
    if (gameState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        finishNodeDrag(x, y, event.target === gameState.gameCanvas);
        gameState.draggingNode = null;
        updateDisplay();
    }
}

// ========== Utility Functions ==========

function findNodeAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? gameState.temporaryNodes : gameState.placedNodes;
    const nodeSize = 60;
    
    for (const node of nodes) {
        if (x >= node.x - nodeSize/2 && x <= node.x + nodeSize/2 &&
            y >= node.y - nodeSize/2 && y <= node.y + nodeSize/2) {
            return node;
        }
    }
    return null;
}

function findPortAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? gameState.temporaryNodes : gameState.placedNodes;
    const portSize = 12;
    
    for (const node of nodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.x !== undefined && port.y !== undefined) {
                if (x >= port.x - portSize/2 && x <= port.x + portSize/2 &&
                    y >= port.y - portSize/2 && y <= port.y + portSize/2) {
                    return port;
                }
            }
        }
    }
    return null;
}

function findNodeById(nodeId) {
    return [...gameState.temporaryNodes, ...gameState.placedNodes].find(node => node.id === nodeId);
}

function findPortInNode(node, portId) {
    const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
    return allPorts.find(port => port.id === portId);
}

function startNodeDrag(node, x, y) {
    gameState.draggingNode = {
        node: node,
        offsetX: x - node.x,
        offsetY: y - node.y
    };
}

function finishNodeDrag(x, y, isGameArea) {
    const node = gameState.draggingNode.node;
    
    if (isGameArea) {
        // Move to game area
        if (gameState.temporaryNodes.includes(node)) {
            gameState.temporaryNodes = gameState.temporaryNodes.filter(n => n !== node);
            gameState.placedNodes.push(node);
        }
    } else {
        // Move to temporary area
        if (gameState.placedNodes.includes(node)) {
            gameState.placedNodes = gameState.placedNodes.filter(n => n !== node);
            gameState.temporaryNodes.push(node);
        }
    }
}

function handlePortClick(port) {
    if (!gameState.creatingConnection) {
        // Start creating connection
        gameState.creatingConnection = { fromPort: port };
        console.log('[MODE3] Started connection from port', port.id);
    } else {
        // Complete connection
        const fromPort = gameState.creatingConnection.fromPort;
        if (canConnect(fromPort, port)) {
            createConnection(fromPort, port);
        }
        gameState.creatingConnection = null;
    }
}

function canConnect(fromPort, toPort) {
    return fromPort.type === toPort.type && fromPort.color === toPort.color;
}

function createConnection(fromPort, toPort) {
    const fromNode = findNodeByPort(fromPort);
    const toNode = findNodeByPort(toPort);
    
    if (fromNode && toNode) {
        const connection = {
            id: `conn_${Date.now()}`,
            fromNode: fromNode.id,
            fromPort: fromPort.id,
            toNode: toNode.id,
            toPort: toPort.id
        };
        
        gameState.connections.push(connection);
        console.log('[MODE3] Created connection', connection.id);
        updateDisplay();
    }
}

function findNodeByPort(port) {
    const allNodes = [...gameState.temporaryNodes, ...gameState.placedNodes];
    for (const node of allNodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        if (allPorts.includes(port)) {
            return node;
        }
    }
    return null;
}

// ========== Global Functions for Testing ==========

window.updateDisplay = updateDisplay;
window.findPortAtPosition = findPortAtPosition;
window.handlePortClick = handlePortClick;
window.createConnection = createConnection;
window.validateConnection = canConnect;

// ========== Auto-Initialize ==========

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MODE3] DOM loaded, initializing...');
    setTimeout(() => {
        if (initializeGame()) {
            console.log('[MODE3] Minimal Mode 3 ready!');
        }
    }, 100);
});
