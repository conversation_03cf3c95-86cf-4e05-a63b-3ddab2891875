// Debug Mode 3 node generation carefully to find unsolvable scenarios
console.log('🔍 深度调试Mode 3节点生成...');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// 模拟游戏状态，追踪多个波次
const mockGameState = {
    nodes: [],
    placedNodes: [],
    temporaryNodes: [],
    connections: [],
    portTypes: ['square', 'diamond', 'triangle', 'circle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
    infiniteMode: {
        currentWave: 1,
        adaptiveDifficulty: 1
    }
};

// 模拟结构修改（可能导致问题的部分）
function simulateStructuralModifications(wave) {
    console.log(`\n🔧 模拟波次 ${wave} 的结构修改...`);
    
    const modifications = [];
    
    // 获取现有节点
    const allNodes = [...mockGameState.placedNodes, ...mockGameState.nodes];
    const placedNodes = allNodes.filter(n => n.type === 'normal');
    
    // 1. 可能添加新节点
    if (Math.random() < 0.4) {
        const newNode = new TestNode('normal');
        newNode.id = `wave_${wave}_mod_${Date.now()}`;
        newNode.label = `W${wave}-Mod`;
        
        // 这里可能是问题所在 - 随机端口类型
        const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
        const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
        
        newNode.addInputPort(portType, portColor);
        newNode.addOutputPort(portType, portColor);
        
        mockGameState.placedNodes.push(newNode);
        modifications.push(`添加节点: ${newNode.label} (${portType}-${portColor})`);
    }
    
    // 2. 修改现有节点端口
    if (placedNodes.length > 0 && Math.random() < 0.5) {
        const nodeToModify = placedNodes[Math.floor(Math.random() * placedNodes.length)];
        const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
        const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
        
        // 随机添加端口 - 这可能破坏平衡
        if (Math.random() < 0.5) {
            nodeToModify.addInputPort(portType, portColor);
            modifications.push(`为${nodeToModify.label}添加输入端口: ${portType}-${portColor}`);
        } else {
            nodeToModify.addOutputPort(portType, portColor);
            modifications.push(`为${nodeToModify.label}添加输出端口: ${portType}-${portColor}`);
        }
    }
    
    // 3. 可能添加新的起点/终点
    if (wave > 1 && Math.random() < 0.3) {
        if (Math.random() < 0.5) {
            const newStart = new TestNode('start');
            newStart.id = `start_wave_${wave}`;
            newStart.label = `Start-W${wave}`;
            
            const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
            const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
            newStart.addOutputPort(portType, portColor);
            
            mockGameState.placedNodes.push(newStart);
            modifications.push(`添加新起点: ${newStart.label} 输出${portType}-${portColor}`);
        }
        
        if (Math.random() < 0.5) {
            const newEnd = new TestNode('end');
            newEnd.id = `end_wave_${wave}`;
            newEnd.label = `End-W${wave}`;
            
            const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
            const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
            newEnd.addInputPort(portType, portColor);
            
            mockGameState.placedNodes.push(newEnd);
            modifications.push(`添加新终点: ${newEnd.label} 输入${portType}-${portColor}`);
        }
    }
    
    if (modifications.length > 0) {
        console.log(`执行修改: ${modifications.join(', ')}`);
    } else {
        console.log('本波次无结构修改');
    }
    
    return modifications;
}

// 分析系统状态
function analyzeSystemDetailed() {
    const allNodes = [...mockGameState.placedNodes, ...mockGameState.nodes, ...mockGameState.temporaryNodes];
    
    const analysis = {
        totalNodes: allNodes.length,
        startNodes: allNodes.filter(n => n.type === 'start'),
        endNodes: allNodes.filter(n => n.type === 'end'),
        normalNodes: allNodes.filter(n => n.type === 'normal'),
        temporaryNodes: mockGameState.temporaryNodes,
        portAnalysis: new Map()
    };
    
    // 详细端口分析
    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const key = `${port.type}-${port.color}`;
            if (!analysis.portAnalysis.has(key)) {
                analysis.portAnalysis.set(key, { 
                    input: 0, output: 0, 
                    inputSources: [], outputSources: [],
                    nodes: new Set()
                });
            }
            const portInfo = analysis.portAnalysis.get(key);
            portInfo.nodes.add(`${node.type}:${node.label}`);
            
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                portInfo.input++;
                portInfo.inputSources.push(`${node.label}`);
            } else {
                portInfo.output++;
                portInfo.outputSources.push(`${node.label}`);
            }
        });
    });
    
    return analysis;
}

// 检查可解性
function checkSolvabilityDetailed() {
    const analysis = analyzeSystemDetailed();
    const issues = [];
    const warnings = [];
    
    // 1. 基本结构检查
    if (analysis.startNodes.length === 0) {
        issues.push('❌ 缺少起点节点');
    }
    if (analysis.endNodes.length === 0) {
        issues.push('❌ 缺少终点节点');
    }
    
    // 2. 端口平衡检查
    let totalImbalance = 0;
    for (const [portType, info] of analysis.portAnalysis.entries()) {
        const imbalance = info.output - info.input;
        if (imbalance !== 0) {
            totalImbalance += Math.abs(imbalance);
            issues.push(`❌ 端口不平衡 ${portType}: ${info.output}输出 vs ${info.input}输入 (差值: ${imbalance})`);
            
            // 详细分析不平衡的来源
            if (imbalance > 0) {
                issues.push(`  ↳ 多余输出来自: ${info.outputSources.join(', ')}`);
            } else {
                issues.push(`  ↳ 多余输入来自: ${info.inputSources.join(', ')}`);
            }
        }
    }
    
    // 3. 孤立端口类型检查
    for (const [portType, info] of analysis.portAnalysis.entries()) {
        if (info.input === 0 && info.output > 0) {
            warnings.push(`⚠️ 孤立输出端口类型 ${portType}: 无对应输入`);
        }
        if (info.output === 0 && info.input > 0) {
            warnings.push(`⚠️ 孤立输入端口类型 ${portType}: 无对应输出`);
        }
    }
    
    // 4. 连通性基础检查
    const hasValidFlow = analysis.startNodes.length > 0 && analysis.endNodes.length > 0;
    if (!hasValidFlow) {
        issues.push('❌ 无法形成有效数据流路径');
    }
    
    return {
        isSolvable: issues.length === 0,
        issues,
        warnings,
        totalImbalance,
        analysis
    };
}

// 生成临时节点（当前实现）
function generateTemporaryNodesCurrentLogic(wave) {
    console.log(`\n🎯 为波次 ${wave} 生成临时节点...`);
    
    mockGameState.temporaryNodes = [];
    
    const analysis = analyzeSystemDetailed();
    
    // 检查是否需要桥接节点
    const requiredBridges = new Set();
    analysis.startNodes.forEach(node => {
        node.outputPorts.forEach(port => {
            requiredBridges.add(`${port.type}-${port.color}`);
        });
    });
    
    console.log(`需要桥接的端口类型: ${Array.from(requiredBridges).join(', ')}`);
    
    // 生成桥接节点
    let bridgeIndex = 0;
    for (const portType of requiredBridges) {
        const [type, color] = portType.split('-');
        
        const bridgeNode = new TestNode('normal');
        bridgeNode.id = `temp_bridge_${wave}_${bridgeIndex}`;
        bridgeNode.label = `W${wave}-${bridgeIndex + 1}`;
        
        bridgeNode.addInputPort(type, color);
        bridgeNode.addOutputPort(type, color);
        
        mockGameState.temporaryNodes.push(bridgeNode);
        bridgeIndex++;
    }
    
    // 额外复杂度（可能导致问题）
    if (wave > 1) {
        const extraNode = new TestNode('normal');
        extraNode.id = `temp_extra_${wave}`;
        extraNode.label = `W${wave}-Extra`;
        
        // 随机端口类型 - 这可能是问题所在
        const randomType = mockGameState.portTypes[wave % mockGameState.portTypes.length];
        const randomColor = mockGameState.portColors[wave % mockGameState.portColors.length];
        
        extraNode.addInputPort(randomType, randomColor);
        extraNode.addOutputPort(randomType, randomColor);
        
        mockGameState.temporaryNodes.push(extraNode);
        console.log(`⚠️ 添加额外复杂度节点: ${extraNode.label} (${randomType}-${randomColor})`);
    }
    
    console.log(`生成 ${mockGameState.temporaryNodes.length} 个临时节点`);
}

// 模拟完整的Mode 3波次序列
function simulateMode3WaveSequence() {
    console.log('🌊 模拟Mode 3多波次序列...\n');
    
    // 初始化：Wave 1
    console.log('=== 波次 1 初始化 ===');
    mockGameState.placedNodes = [];
    mockGameState.nodes = [];
    mockGameState.temporaryNodes = [];
    
    // 创建基础起点终点
    const startNode = new TestNode('start');
    startNode.id = 'initial_start';
    startNode.addOutputPort('square', '#ff5252');
    
    const endNode = new TestNode('end');
    endNode.id = 'initial_end';
    endNode.addInputPort('square', '#ff5252');
    
    mockGameState.placedNodes.push(startNode, endNode);
    
    // 测试多个波次
    for (let wave = 1; wave <= 3; wave++) {
        console.log(`\n=== 波次 ${wave} ===`);
        mockGameState.infiniteMode.currentWave = wave;
        
        // 1. 生成临时节点
        generateTemporaryNodesCurrentLogic(wave);
        
        // 2. 检查可解性
        const solvability = checkSolvabilityDetailed();
        
        console.log(`\n📊 波次 ${wave} 分析:`);
        console.log(`总节点: ${solvability.analysis.totalNodes} (${solvability.analysis.startNodes.length}起点, ${solvability.analysis.endNodes.length}终点, ${solvability.analysis.normalNodes.length}中间, ${solvability.analysis.temporaryNodes.length}临时)`);
        console.log(`可解性: ${solvability.isSolvable ? '✅ 可解' : '❌ 不可解'}`);
        console.log(`总不平衡度: ${solvability.totalImbalance}`);
        
        if (!solvability.isSolvable) {
            console.log('\n❌ 发现不可解场景:');
            solvability.issues.forEach(issue => console.log(`  ${issue}`));
        }
        
        if (solvability.warnings.length > 0) {
            console.log('\n⚠️ 警告:');
            solvability.warnings.forEach(warning => console.log(`  ${warning}`));
        }
        
        // 详细端口分析
        console.log('\n🔍 详细端口分析:');
        for (const [portType, info] of solvability.analysis.portAnalysis.entries()) {
            const balance = info.output === info.input ? '✅' : '❌';
            console.log(`  ${portType}: ${info.output}输出/${info.input}输入 ${balance}`);
            if (info.output !== info.input) {
                console.log(`    输出节点: [${info.outputSources.join(', ')}]`);
                console.log(`    输入节点: [${info.inputSources.join(', ')}]`);
            }
        }
        
        // 如果当前波次不可解，停止模拟
        if (!solvability.isSolvable) {
            console.log(`\n🚨 在波次 ${wave} 发现不可解情况，停止模拟`);
            return { problematicWave: wave, solvability };
        }
        
        // 3. 模拟玩家完成波次，执行结构修改
        if (wave < 3) {
            console.log(`\n🔄 波次 ${wave} 完成，执行结构修改...`);
            simulateStructuralModifications(wave + 1);
        }
    }
    
    return { problematicWave: null, allWavesSolvable: true };
}

// 运行完整模拟
const result = simulateMode3WaveSequence();

console.log('\n' + '='.repeat(60));
console.log('📋 模拟结果总结');
console.log('='.repeat(60));

if (result.problematicWave) {
    console.log(`❌ 发现问题波次: Wave ${result.problematicWave}`);
    console.log('这可能就是您提到的 "w 1-2" 不在可解域的问题！');
    
    console.log('\n🔧 问题分析:');
    console.log('1. 结构修改可能引入随机端口类型');
    console.log('2. 临时节点生成算法在复杂场景下可能失效');
    console.log('3. 多波次累积效应导致端口类型激增');
    console.log('4. 缺乏全局一致性检查');
    
} else {
    console.log('✅ 所有波次都可解（在此次随机模拟中）');
    console.log('⚠️ 但问题可能是概率性的，需要多次测试');
}

console.log('\n✅ 深度调试完成');