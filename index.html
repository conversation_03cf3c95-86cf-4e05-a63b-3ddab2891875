<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#1a1a1a">
    <title>Blueprint - Node Connection Game</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="game-container">
        <div class="header">
            <div class="title">Blueprint - Node Connection Game</div>
            <div class="score-info">
                <div class="level-info">关卡: <span id="level-counter">1</span></div>
                <div class="score">分数: <span id="score-counter">0</span></div>
                <div class="mode-info">模式: <span id="mode-display">谜题</span></div>
            </div>
            <div class="toolbar">
                <div class="controls">
                    <button id="validate-btn">验证蓝图 <kbd>Enter</kbd></button>
                    <button id="play-flow-btn">播放流动 <kbd>Space</kbd></button>
                    <button id="clear-btn">清空 <kbd>C</kbd></button>
                    <button id="regenerate-btn">重新生成 <kbd>R</kbd></button>
                    
                    <!-- 游戏模式选择 -->
                    <div class="mode-selector">
                        <button id="puzzle-mode-btn" class="mode-btn active">🧩 谜题</button>
                        <button id="tetris-mode-btn" class="mode-btn">🎮 俄罗斯方块</button>
                        <button id="infinite-mode-btn" class="mode-btn">🌊 无限构筑</button>
                    </div>
                    
                    <button id="hint-btn">提示 <kbd>H</kbd></button>

                    <!-- 开发者测试按钮 -->
                    <button id="dev-test-btn" style="display: none;">🧪 测试推进 <kbd>T</kbd></button>

                    <!-- 逐级生成测试按钮 -->
                    <button id="level-test-btn" style="display: none; margin-left: 10px;">🔬 逐级测试 <kbd>L</kbd></button>

                    <!-- 波次推进模式控制 -->
                    <div id="progression-control" style="display: none; margin-left: 10px;">
                        <label for="progression-mode">推进模式:</label>
                        <select id="progression-mode">
                            <option value="MANUAL">手动模式</option>
                            <option value="AUTO">自动模式</option>
                            <option value="DEVELOPER">开发者模式</option>
                        </select>
                    </div>
                </div>
                <div class="crossing-controls">
                    <button id="crossing-toggle-btn" class="crossing-btn">🚫 交叉限制 <kbd>X</kbd></button>
                    <button id="crosspoint-toggle-btn" class="crossing-btn">👁️ 显示交叉点 <kbd>V</kbd></button>
                </div>
                
                <!-- 多节点控制面板 -->
                <div class="multi-node-controls">
                    <button id="multi-start-toggle-btn" class="multi-node-btn">🔄 多起点 <kbd>U</kbd></button>
                    <button id="multi-end-toggle-btn" class="multi-node-btn">🎯 多终点 <kbd>I</kbd></button>
                    <button id="debug-test-btn" class="multi-node-btn" style="background: #ff6b6b;">🔧 Debug Test</button>
                </div>
            </div>
        </div>
        
        <div class="game-areas">
            <!-- Node临时区：存放临时生成的node -->
            <div class="temporary-area">
                <div class="area-header">
                    <h3>Node临时区</h3>
                    <span class="area-info">存放临时生成的节点</span>
                    <div class="tetris-info" id="tetris-info" style="display: none;">
                        <div class="time-info">剩余: <span id="time-remaining">300</span>s</div>
                        <div class="capacity-info">容量: <span id="temp-capacity">0/6</span></div>
                        <div class="queue-info">队列: <span id="queue-count">0</span></div>
                        <div class="next-node" id="next-node-preview">下一个: 无</div>
                    </div>
                    <div class="infinite-info" id="infinite-info" style="display: none;">
                        <div class="wave-info">波次: <span id="current-wave">1</span>/<span id="max-waves">10</span></div>
                        <div class="progress-info">进度: <span id="completed-waves">0</span> 完成</div>
                        <div class="difficulty-info">难度: <span id="adaptive-difficulty">1</span></div>
                        <div class="validation-status">状态: <span id="validation-status">准备就绪</span></div>
                        <div class="wave-progress">进度: <span id="wave-progress">0/10</span></div>
                    </div>
                </div>
                <div class="temp-nodes-container">
                    <canvas id="temp-canvas" width="300" height="600"></canvas>
                </div>
            </div>
            
            <!-- Node摆放区：存在起点和终点node，供玩家摆放新node和连接node -->
            <div class="blueprint-area">
                <div class="area-header">
                    <h3>Node摆放区</h3>
                    <span class="area-info">连接节点构建蓝图</span>
                </div>
                <div class="blueprint-container">
                    <canvas id="game-canvas" width="700" height="600"></canvas>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <div class="message" id="message">拖拽节点到摆放区域，连接相同类型的端口！</div>
            <div class="keyboard-help">
                <details>
                    <summary>🎮 快捷键</summary>
                    <div class="help-grid">
                        <span><kbd>A</kbd> 分析建议</span>
                        <span><kbd>S</kbd> 检查可解性</span>
                        <span><kbd>G</kbd> 生成建议节点</span>
                        <span><kbd>H</kbd> 显示解决方案</span>
                        <span><kbd>D</kbd> 调试信息</span>
                        <span><kbd>Del</kbd> 删除节点</span>
                        <span><kbd>X</kbd> 交叉限制</span>
                        <span><kbd>V</kbd> 显示交叉点</span>
                    </div>
                </details>
            </div>
            <button id="mute-button" aria-label="Toggle sound">🔊</button>
        </div>
    </div>

    <!-- Sound effects (hidden) -->
    <audio id="sound-place" preload="auto">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-interface-click-1126.mp3" type="audio/mpeg">
    </audio>
    <audio id="sound-connect" preload="auto">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-positive-interface-beep-221.mp3" type="audio/mpeg">
    </audio>
    <audio id="sound-complete" preload="auto">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-achievement-bell-600.mp3" type="audio/mpeg">
    </audio>
    <audio id="sound-error" preload="auto">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-alert-quick-hint-267.mp3" type="audio/mpeg">
    </audio>
    <audio id="sound-day" preload="auto">
        <source src="https://assets.mixkit.co/sfx/preview/mixkit-software-interface-start-2574.mp3" type="audio/mpeg">
    </audio>

    <script src="game.js?v=layered-dag-algorithm-2025"></script>
</body>
</html> 