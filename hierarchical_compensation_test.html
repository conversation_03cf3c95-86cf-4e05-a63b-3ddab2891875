<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hierarchical Compensation Algorithm Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .algorithm-info {
            background: #2a2a2a;
            border: 2px solid #9C27B0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .level-visualization {
            background: #1a1a3a;
            border: 2px solid #2196F3;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .level-row {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #2a2a3a;
            border-radius: 5px;
        }
        
        .level-label {
            width: 100px;
            font-weight: bold;
            color: #00aaff;
        }
        
        .level-nodes {
            flex: 1;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .node-visual {
            background: #333;
            border: 2px solid #555;
            border-radius: 8px;
            padding: 8px;
            min-width: 80px;
            text-align: center;
            font-size: 10px;
        }
        
        .node-visual.source {
            border-color: #4CAF50;
            background: #1a3a1a;
        }
        
        .node-visual.sink {
            border-color: #f44336;
            background: #3a1a1a;
        }
        
        .node-visual.intermediate {
            border-color: #2196F3;
            background: #1a1a3a;
        }
        
        .node-visual.compensation {
            border-color: #ff9800;
            background: #3a2a1a;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #9C27B0;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #7B1FA2;
        }
        
        .btn.test {
            background: #2196F3;
        }
        
        .btn.test:hover {
            background: #1976D2;
        }
        
        .btn.success {
            background: #4CAF50;
        }
        
        .btn.success:hover {
            background: #45a049;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .test-results {
            background: #1a3a1a;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .result-item {
            background: #2a4a2a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        
        .result-item.failed {
            background: #4a2a2a;
            border-left-color: #f44336;
        }
        
        .wave-progression {
            background: #1a1a3a;
            border: 1px solid #444;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
        }
        
        .wave-item {
            background: #2a2a3a;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏗️ Hierarchical Compensation Algorithm Test</h1>
            <p>分层补偿系统 - 类型隔离验证 - 全局拓扑序维护</p>
        </div>
        
        <div class="algorithm-info">
            <h3>🧠 Algorithm Features</h3>
            <ul>
                <li><strong>分层补偿系统:</strong> 输出端口仅添加到层级0（起点），输入端口仅添加到最高层级（终点）</li>
                <li><strong>类型隔离验证:</strong> 每种端口类型独立验证DAG，共享全局节点层级约束</li>
                <li><strong>全局拓扑序:</strong> 维护兼容的全局拓扑序，避免类型间拓扑冲突</li>
                <li><strong>动态难度调整:</strong> 根据波次动态调整端口类型数量和修改强度</li>
                <li><strong>智能节点分配:</strong> 避免端口集中，支持均匀分布和随机性</li>
            </ul>
        </div>
        
        <div class="controls">
            <button class="btn test" onclick="runHierarchicalTest()">🧪 Run Hierarchical Test</button>
            <button class="btn" onclick="generateTestScenario()">🎲 Generate Scenario</button>
            <button class="btn success" onclick="applyCompensation()">⚖️ Apply Compensation</button>
            <button class="btn test" onclick="validateTypeIsolation()">🔍 Validate Type Isolation</button>
            <button class="btn" onclick="testWaveProgression()">📈 Test Wave Progression</button>
            <button class="btn danger" onclick="clearTest()">🗑️ Clear</button>
        </div>
        
        <div class="level-visualization">
            <h3>📊 Level-Based Node Distribution</h3>
            <div id="level-display">
                <div class="level-row">
                    <div class="level-label">No Data</div>
                    <div class="level-nodes">Click "Generate Scenario" to start</div>
                </div>
            </div>
        </div>
        
        <div class="test-results">
            <h3>✅ Test Results</h3>
            <div id="test-results-content">
                Ready to run hierarchical compensation tests.
            </div>
        </div>
        
        <div class="wave-progression">
            <h3>📈 Wave Progression Analysis</h3>
            <div id="wave-progression-content">
                No wave progression data yet.
            </div>
        </div>
        
        <div>
            <h3>📋 Hierarchical Compensation Log</h3>
            <div class="log" id="hierarchical-log"></div>
        </div>
    </div>

    <!-- Load the hierarchical compensation algorithm -->
    <script src="compensation_algorithm.js"></script>
    
    <script>
        let hierarchicalAlgorithm = null;
        let currentTestNodes = [];
        let currentWave = 1;
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            hierarchicalAlgorithm = new HierarchicalCompensationAlgorithm();
            logHierarchical('Hierarchical Compensation Algorithm Test initialized', 'success');
        });
        
        // Logging system
        function logHierarchical(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('hierarchical-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[HIERARCHICAL-TEST] ${message}`);
        }
        
        // Generate test scenario with intentional imbalances
        function generateTestScenario() {
            logHierarchical('Generating test scenario with hierarchical structure', 'info');
            
            currentTestNodes = [
                // Level 0 - Sources
                {
                    id: 'source_1',
                    type: 'start',
                    level: 0,
                    depth: 0,
                    inputPorts: [],
                    outputPorts: [
                        { id: 'src1_out1', type: 'square', color: 'red', side: 'output', nodeId: 'source_1', portTypeKey: 'square-red' },
                        { id: 'src1_out2', type: 'circle', color: 'blue', side: 'output', nodeId: 'source_1', portTypeKey: 'circle-blue' }
                    ]
                },
                
                // Level 1 - Intermediate
                {
                    id: 'intermediate_1',
                    type: 'intermediate',
                    level: 1,
                    depth: 100,
                    inputPorts: [
                        { id: 'int1_in1', type: 'square', color: 'red', side: 'input', nodeId: 'intermediate_1', portTypeKey: 'square-red' }
                    ],
                    outputPorts: [
                        { id: 'int1_out1', type: 'triangle', color: 'green', side: 'output', nodeId: 'intermediate_1', portTypeKey: 'triangle-green' },
                        { id: 'int1_out2', type: 'triangle', color: 'green', side: 'output', nodeId: 'intermediate_1', portTypeKey: 'triangle-green' }
                    ]
                },
                
                // Level 2 - Sink (with imbalance)
                {
                    id: 'sink_1',
                    type: 'end',
                    level: 2,
                    depth: 200,
                    inputPorts: [
                        { id: 'sink1_in1', type: 'triangle', color: 'green', side: 'input', nodeId: 'sink_1', portTypeKey: 'triangle-green' }
                    ],
                    outputPorts: []
                }
            ];
            
            logHierarchical(`Generated test scenario with ${currentTestNodes.length} nodes across 3 levels`, 'success');
            updateLevelVisualization();
            analyzeCurrentState();
        }
        
        // Apply hierarchical compensation
        function applyCompensation() {
            if (currentTestNodes.length === 0) {
                logHierarchical('No test nodes available. Generate a scenario first.', 'warn');
                return;
            }
            
            logHierarchical('Applying hierarchical compensation algorithm', 'info');
            
            // Apply compensation
            const compensationNodes = hierarchicalAlgorithm.compensate(currentTestNodes);
            
            // Add compensation nodes to current test
            compensationNodes.forEach(node => {
                node.area = 'temporary';
            });
            
            currentTestNodes.push(...compensationNodes);
            
            logHierarchical(`Added ${compensationNodes.length} compensation nodes`, 'success');
            updateLevelVisualization();
            analyzeCurrentState();
        }
        
        // Validate type isolation
        function validateTypeIsolation() {
            if (currentTestNodes.length === 0) {
                logHierarchical('No test nodes available. Generate a scenario first.', 'warn');
                return;
            }
            
            logHierarchical('Validating type-isolated DAGs', 'info');
            
            const isValid = hierarchicalAlgorithm.validateTypeIsolatedDAGs(currentTestNodes);
            
            if (isValid) {
                logHierarchical('✅ All port types form valid isolated DAGs', 'success');
                updateTestResults('Type Isolation', true, 'All port types maintain DAG properties independently');
            } else {
                logHierarchical('❌ Type isolation validation failed', 'error');
                updateTestResults('Type Isolation', false, 'Some port types violate DAG constraints');
            }
        }
        
        // Test wave progression
        function testWaveProgression() {
            logHierarchical('Testing wave progression with dynamic difficulty', 'info');
            
            const waveResults = [];
            
            for (let wave = 1; wave <= 10; wave++) {
                hierarchicalAlgorithm.adjustDifficultyForWave(wave);
                
                const config = hierarchicalAlgorithm.difficultyConfig;
                waveResults.push({
                    wave: wave,
                    maxPortTypes: config.maxPortTypes,
                    maxModifications: config.maxModifications,
                    maxSourceNodes: config.maxSourceNodes,
                    maxSinkNodes: config.maxSinkNodes
                });
                
                logHierarchical(`Wave ${wave}: ${config.maxPortTypes} port types, ${config.maxModifications} modifications`, 'info');
            }
            
            updateWaveProgression(waveResults);
        }
        
        // Run comprehensive hierarchical test
        function runHierarchicalTest() {
            logHierarchical('=== RUNNING COMPREHENSIVE HIERARCHICAL TEST ===', 'info');
            
            const testResults = [];
            
            // Test 1: Generate scenario
            generateTestScenario();
            testResults.push({ name: 'Scenario Generation', passed: true, message: 'Test scenario generated successfully' });
            
            // Test 2: Apply compensation
            const initialImbalance = hierarchicalAlgorithm.calculatePortImbalance(currentTestNodes);
            let hasInitialImbalance = false;
            initialImbalance.forEach(deficit => {
                if (deficit !== 0) hasInitialImbalance = true;
            });
            
            if (hasInitialImbalance) {
                applyCompensation();
                
                // Verify balance after compensation
                const finalImbalance = hierarchicalAlgorithm.calculatePortImbalance(currentTestNodes);
                let isBalanced = true;
                finalImbalance.forEach(deficit => {
                    if (deficit !== 0) isBalanced = false;
                });
                
                testResults.push({ 
                    name: 'Port Balance Compensation', 
                    passed: isBalanced, 
                    message: isBalanced ? 'All port types balanced after compensation' : 'Port imbalances remain after compensation'
                });
            } else {
                testResults.push({ name: 'Port Balance Compensation', passed: true, message: 'No compensation needed - already balanced' });
            }
            
            // Test 3: Type isolation validation
            const typeIsolationValid = hierarchicalAlgorithm.validateTypeIsolatedDAGs(currentTestNodes);
            testResults.push({ 
                name: 'Type Isolation', 
                passed: typeIsolationValid, 
                message: typeIsolationValid ? 'All port types form valid DAGs' : 'Type isolation validation failed'
            });
            
            // Test 4: Global topology validation
            const topologyValid = hierarchicalAlgorithm.validateGlobalTopologicalOrder(currentTestNodes);
            testResults.push({ 
                name: 'Global Topology', 
                passed: topologyValid, 
                message: topologyValid ? 'Global topological order is valid' : 'Global topology validation failed'
            });
            
            // Test 5: Strong solvability
            const strongSolvability = hierarchicalAlgorithm.validateStrongSolvability(currentTestNodes, []);
            testResults.push({ 
                name: 'Strong Solvability', 
                passed: strongSolvability, 
                message: strongSolvability ? 'System is strongly solvable' : 'Strong solvability validation failed'
            });
            
            // Display results
            displayTestResults(testResults);
            
            const passedCount = testResults.filter(r => r.passed).length;
            const totalTests = testResults.length;
            
            logHierarchical(`=== TEST SUMMARY: ${passedCount}/${totalTests} TESTS PASSED ===`, 
                           passedCount === totalTests ? 'success' : 'error');
        }
        
        // Update level visualization
        function updateLevelVisualization() {
            const levelGroups = new Map();
            
            currentTestNodes.forEach(node => {
                const level = hierarchicalAlgorithm.getNodeLevel(node);
                if (!levelGroups.has(level)) {
                    levelGroups.set(level, []);
                }
                levelGroups.get(level).push(node);
            });
            
            const displayElement = document.getElementById('level-display');
            const levelRows = [];
            
            // Sort levels
            const sortedLevels = Array.from(levelGroups.keys()).sort((a, b) => a - b);
            
            sortedLevels.forEach(level => {
                const nodes = levelGroups.get(level);
                const nodeVisuals = nodes.map(node => {
                    let nodeClass = 'node-visual';
                    
                    if (hierarchicalAlgorithm.isSourceNode(node)) {
                        nodeClass += ' source';
                    } else if (hierarchicalAlgorithm.isSinkNode(node)) {
                        nodeClass += ' sink';
                    } else {
                        nodeClass += ' intermediate';
                    }
                    
                    if (node.createdBy === 'hierarchical_compensation') {
                        nodeClass += ' compensation';
                    }
                    
                    const portCount = hierarchicalAlgorithm.getNodePortCount(node);
                    
                    return `<div class="${nodeClass}">
                        <div>${node.id}</div>
                        <div>${portCount} ports</div>
                    </div>`;
                }).join('');
                
                levelRows.push(`
                    <div class="level-row">
                        <div class="level-label">Level ${level}</div>
                        <div class="level-nodes">${nodeVisuals}</div>
                    </div>
                `);
            });
            
            displayElement.innerHTML = levelRows.join('');
        }
        
        // Analyze current state
        function analyzeCurrentState() {
            const analysis = hierarchicalAlgorithm.analyzeNodePool(currentTestNodes);
            const imbalance = hierarchicalAlgorithm.calculatePortImbalance(currentTestNodes);
            
            logHierarchical(`Analysis: ${analysis.totalNodes} nodes across ${analysis.levelRange.max - analysis.levelRange.min + 1} levels`, 'info');
            
            imbalance.forEach((deficit, portType) => {
                if (deficit !== 0) {
                    logHierarchical(`Imbalance: ${portType} has deficit ${deficit}`, 'warn');
                } else {
                    logHierarchical(`Balanced: ${portType} is perfectly balanced`, 'success');
                }
            });
        }
        
        // Update test results display
        function updateTestResults(testName, passed, message) {
            const resultsElement = document.getElementById('test-results-content');
            const resultClass = passed ? 'result-item' : 'result-item failed';
            const icon = passed ? '✅' : '❌';
            
            const newResult = `<div class="${resultClass}">${icon} <strong>${testName}:</strong> ${message}</div>`;
            resultsElement.innerHTML += newResult;
        }
        
        // Display comprehensive test results
        function displayTestResults(results) {
            const resultsElement = document.getElementById('test-results-content');
            
            const resultHtml = results.map(result => {
                const className = result.passed ? 'result-item' : 'result-item failed';
                const icon = result.passed ? '✅' : '❌';
                return `<div class="${className}">${icon} <strong>${result.name}:</strong> ${result.message}</div>`;
            }).join('');
            
            resultsElement.innerHTML = resultHtml;
        }
        
        // Update wave progression display
        function updateWaveProgression(waveResults) {
            const progressionElement = document.getElementById('wave-progression-content');
            
            const progressionHtml = waveResults.map(result => {
                return `<div class="wave-item">
                    <strong>Wave ${result.wave}:</strong> 
                    ${result.maxPortTypes} port types, 
                    ${result.maxModifications} modifications, 
                    ${result.maxSourceNodes}/${result.maxSinkNodes} source/sink nodes
                </div>`;
            }).join('');
            
            progressionElement.innerHTML = progressionHtml;
        }
        
        // Clear test data
        function clearTest() {
            currentTestNodes = [];
            currentWave = 1;
            
            document.getElementById('level-display').innerHTML = 
                '<div class="level-row"><div class="level-label">No Data</div><div class="level-nodes">Click "Generate Scenario" to start</div></div>';
            document.getElementById('test-results-content').innerHTML = 'Ready to run hierarchical compensation tests.';
            document.getElementById('wave-progression-content').innerHTML = 'No wave progression data yet.';
            
            logHierarchical('Test data cleared', 'info');
        }
    </script>
</body>
</html>
