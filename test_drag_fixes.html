<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Drag and Drop Fixes</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-controls {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            line-height: 1.3;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
        
        .game-area {
            display: flex;
            margin-top: 20px;
            border: 2px solid #3e3e42;
            border-radius: 8px;
            overflow: hidden;
            background: #2d2d30;
        }
        
        .temp-area {
            width: 300px;
            height: 600px;
            border-right: 2px solid #3e3e42;
            background: #1e1e1e;
            position: relative;
        }
        
        .play-area {
            width: 700px;
            height: 600px;
            background: #1e1e1e;
            position: relative;
        }
        
        .test-node {
            position: absolute;
            width: 100px;
            height: 60px;
            background: #0e639c;
            border: 2px solid #1177bb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            cursor: move;
            user-select: none;
        }
        
        .test-node:hover {
            background: #1177bb;
        }
        
        .test-node.dragging {
            opacity: 0.8;
            z-index: 1000;
        }
        
        .debug-info {
            background: #2d2d30;
            padding: 10px;
            margin-top: 10px;
            border-radius: 4px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖱️ Test Drag and Drop Fixes</h1>
        
        <div class="test-controls">
            <button onclick="runAllTests()">🚀 Run All Tests</button>
            <button onclick="testLevel6()">🎯 Test Level 6</button>
            <button onclick="testPositioning()">📍 Test Positioning</button>
            <button onclick="testCrossAreaDrag()">🔄 Test Cross-Area Drag</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
        </div>
        
        <div class="test-output" id="testOutput">
            <div class="info">Ready to test drag and drop fixes...</div>
        </div>
        
        <div class="game-area">
            <div class="temp-area" id="tempArea">
                <div style="padding: 10px; color: #9cdcfe; font-size: 12px;">Temp Area</div>
            </div>
            <div class="play-area" id="playArea">
                <div style="padding: 10px; color: #9cdcfe; font-size: 12px;">Play Area</div>
            </div>
        </div>
        
        <div class="debug-info" id="debugInfo">
            Debug info will appear here...
        </div>
    </div>

    <script src="game.js?v=layered-dag-algorithm-2025"></script>
    
    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0
        };
        
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const div = document.createElement('div');
            div.className = type;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
            testResults = { total: 0, passed: 0, failed: 0 };
        }
        
        function recordTest(testName, passed, details = '') {
            testResults.total++;
            if (passed) {
                testResults.passed++;
                log('✅ ' + testName + ': PASSED' + (details ? ' - ' + details : ''), 'success');
            } else {
                testResults.failed++;
                log('❌ ' + testName + ': FAILED' + (details ? ' - ' + details : ''), 'error');
            }
        }
        
        function showSummary() {
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            log('\n📊 Test Summary: ' + testResults.passed + '/' + testResults.total + ' passed (' + successRate + '%)', 
                successRate >= 90 ? 'success' : successRate >= 70 ? 'warning' : 'error');
        }
        
        async function runAllTests() {
            clearOutput();
            log('🚀 Running all drag and drop tests...', 'info');
            
            await testLevel6();
            await delay(500);
            await testPositioning();
            await delay(500);
            await testCrossAreaDrag();
            
            showSummary();
        }
        
        async function testLevel6() {
            log('\n🎯 Testing Level 6 functionality...', 'info');
            
            try {
                // Initialize game for level 6
                if (typeof initializeGame === 'function') {
                    initializeGame();
                }
                
                gameState.level = 6;
                gameState.mode = 'puzzle';
                
                // Clear existing state
                gameState.temporaryNodes = [];
                gameState.placedNodes = [];
                gameState.connections = [];
                
                // Generate level 6
                if (typeof generatePuzzleLevel === 'function') {
                    generatePuzzleLevel();
                    
                    recordTest('Level 6 generation', true, 'Generated ' + gameState.temporaryNodes.length + ' temp nodes');
                    
                    // Test temp node display
                    const hasVisibleTempNodes = gameState.temporaryNodes.length > 0;
                    recordTest('Temp nodes created', hasVisibleTempNodes, gameState.temporaryNodes.length + ' nodes');
                    
                    // Test node positioning
                    let allNodesPositioned = true;
                    gameState.temporaryNodes.forEach((node, index) => {
                        if (node.x < 0 || node.y < 0) {
                            allNodesPositioned = false;
                        }
                    });
                    recordTest('Temp nodes positioned', allNodesPositioned);
                    
                } else {
                    recordTest('Level 6 generation', false, 'generatePuzzleLevel function not found');
                }
                
            } catch (error) {
                recordTest('Level 6 test', false, error.message);
            }
        }
        
        async function testPositioning() {
            log('\n📍 Testing drag positioning accuracy...', 'info');
            
            try {
                // Create test node
                const testNode = new Node('normal');
                testNode.label = 'Test';
                testNode.moveTo(50, 100);
                testNode.addInputPort('square', '#ff5252');
                testNode.addOutputPort('circle', '#2196F3');
                
                gameState.temporaryNodes = [testNode];
                
                // Test drag start
                const startX = 75; // Middle of node
                const startY = 130;
                
                if (typeof startNodeDrag === 'function') {
                    startNodeDrag(testNode, startX, startY, 'temp');
                    
                    const hasOffset = gameState.draggingNode && 
                                     typeof gameState.draggingNode.offsetX === 'number' &&
                                     typeof gameState.draggingNode.offsetY === 'number';
                    recordTest('Drag offset calculation', hasOffset);
                    
                    // Test drag update
                    const newX = 150;
                    const newY = 200;
                    
                    if (typeof updateNodeDrag === 'function') {
                        updateNodeDrag(newX, newY, 'temp');
                        
                        // Check if node moved
                        const nodeMoved = testNode.x !== 50 || testNode.y !== 100;
                        recordTest('Node position update', nodeMoved);
                        
                        // Test drag finish
                        if (typeof finishNodeDrag === 'function') {
                            finishNodeDrag(newX, newY, 'temp');
                            
                            // Check final position accuracy
                            const expectedX = newX - gameState.draggingNode.offsetX;
                            const expectedY = newY - gameState.draggingNode.offsetY;
                            const positionError = Math.sqrt(
                                Math.pow(testNode.x - expectedX, 2) + 
                                Math.pow(testNode.y - expectedY, 2)
                            );
                            
                            recordTest('Position accuracy', positionError < 10, 'Error: ' + positionError.toFixed(2) + 'px');
                            
                            // Clean up
                            gameState.draggingNode = null;
                            testNode.isDragging = false;
                        } else {
                            recordTest('Drag finish function', false, 'finishNodeDrag not found');
                        }
                    } else {
                        recordTest('Drag update function', false, 'updateNodeDrag not found');
                    }
                } else {
                    recordTest('Drag start function', false, 'startNodeDrag not found');
                }
                
            } catch (error) {
                recordTest('Positioning test', false, error.message);
            }
        }
        
        async function testCrossAreaDrag() {
            log('\n🔄 Testing cross-area drag functionality...', 'info');
            
            try {
                // Create test nodes in both areas
                const tempNode = new Node('normal');
                tempNode.label = 'Temp';
                tempNode.moveTo(50, 50);
                tempNode.addInputPort('square', '#ff5252');
                tempNode.addOutputPort('circle', '#2196F3');
                
                const placedNode = new Node('normal');
                placedNode.label = 'Placed';
                placedNode.moveTo(100, 100);
                placedNode.addInputPort('triangle', '#4CAF50');
                placedNode.addOutputPort('diamond', '#FFC107');
                
                gameState.temporaryNodes = [tempNode];
                gameState.placedNodes = [placedNode];
                
                // Test temp to placement drag
                if (typeof startNodeDrag === 'function' && typeof finishNodeDrag === 'function') {
                    startNodeDrag(tempNode, 75, 80, 'temp');
                    finishNodeDrag(400, 200, 'game'); // Drag to placement area
                    
                    const movedToPlacement = gameState.placedNodes.includes(tempNode) && 
                                           !gameState.temporaryNodes.includes(tempNode);
                    recordTest('Temp to placement drag', movedToPlacement);
                    
                    // Test placement to temp drag
                    startNodeDrag(tempNode, 400, 200, 'game');
                    finishNodeDrag(100, 150, 'temp'); // Drag back to temp area
                    
                    const movedBackToTemp = gameState.temporaryNodes.includes(tempNode) && 
                                          !gameState.placedNodes.includes(tempNode);
                    recordTest('Placement to temp drag', movedBackToTemp);
                    
                } else {
                    recordTest('Cross-area drag functions', false, 'Required functions not found');
                }
                
            } catch (error) {
                recordTest('Cross-area drag test', false, error.message);
            }
        }
        
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // Initialize when page loads
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Drag and drop fixes test page loaded', 'info');
                log('Click buttons above to test specific functionality', 'info');
            }, 500);
        });
        
        // Add visual test nodes
        function createVisualTestNode(x, y, area, label) {
            const node = document.createElement('div');
            node.className = 'test-node';
            node.textContent = label;
            node.style.left = x + 'px';
            node.style.top = y + 'px';
            
            let isDragging = false;
            let startX, startY, offsetX, offsetY;
            
            node.addEventListener('mousedown', (e) => {
                isDragging = true;
                node.classList.add('dragging');
                
                const rect = node.getBoundingClientRect();
                const areaRect = document.getElementById(area + 'Area').getBoundingClientRect();
                
                offsetX = e.clientX - rect.left;
                offsetY = e.clientY - rect.top;
                
                document.getElementById('debugInfo').textContent = 
                    'Started drag: offset(' + offsetX + ', ' + offsetY + ')';
            });
            
            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;
                
                const areaRect = document.getElementById(area + 'Area').getBoundingClientRect();
                const newX = e.clientX - areaRect.left - offsetX;
                const newY = e.clientY - areaRect.top - offsetY;
                
                node.style.left = Math.max(0, Math.min(newX, areaRect.width - node.offsetWidth)) + 'px';
                node.style.top = Math.max(0, Math.min(newY, areaRect.height - node.offsetHeight)) + 'px';
                
                document.getElementById('debugInfo').textContent = 
                    'Dragging: pos(' + newX.toFixed(0) + ', ' + newY.toFixed(0) + ') offset(' + offsetX + ', ' + offsetY + ')';
            });
            
            document.addEventListener('mouseup', () => {
                if (isDragging) {
                    isDragging = false;
                    node.classList.remove('dragging');
                    
                    document.getElementById('debugInfo').textContent = 
                        'Drag finished at: ' + node.style.left + ', ' + node.style.top;
                }
            });
            
            document.getElementById(area + 'Area').appendChild(node);
        }
        
        // Create visual test nodes
        setTimeout(() => {
            createVisualTestNode(50, 50, 'temp', 'Temp 1');
            createVisualTestNode(50, 120, 'temp', 'Temp 2');
            createVisualTestNode(100, 100, 'play', 'Play 1');
            createVisualTestNode(200, 150, 'play', 'Play 2');
        }, 1000);
    </script>
</body>
</html>
