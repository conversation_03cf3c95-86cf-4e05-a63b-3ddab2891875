// Test Mode 3 Level 1 issue specifically
console.log('🐛 Testing Mode 3 Level 1 solvability issue...');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }
}

const mockGameState = {
    nodes: [],
    temporaryNodes: [],
    portTypes: ['square', 'diamond', 'triangle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50']
};

function createMode3BasicScenario() {
    mockGameState.nodes = [];
    mockGameState.temporaryNodes = [];

    // 起点
    const startNode = new TestNode('start');
    startNode.addOutputPort('square', '#ff5252');

    // 终点
    const endNode = new TestNode('end');
    endNode.addInputPort('square', '#ff5252');

    mockGameState.nodes = [startNode, endNode];
    console.log('✅ 创建基础场景: 起点输出square-#ff5252, 终点输入square-#ff5252');
    
    return { startNode, endNode };
}

function analyzePortBalance() {
    const allNodes = [...mockGameState.nodes, ...mockGameState.temporaryNodes];
    const portStats = new Map();
    
    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const key = `${port.type}-${port.color}`;
            if (!portStats.has(key)) {
                portStats.set(key, { input: 0, output: 0 });
            }
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                portStats.get(key).input++;
            } else {
                portStats.get(key).output++;
            }
        });
    });

    console.log('\n📊 端口统计:');
    for (const [type, counts] of portStats.entries()) {
        const balance = counts.output === counts.input ? '✅' : '❌';
        console.log(`  ${type}: ${counts.output}输出, ${counts.input}输入 ${balance}`);
    }

    const imbalances = [];
    for (const [type, counts] of portStats.entries()) {
        if (counts.input !== counts.output) {
            imbalances.push(`${type}: ${counts.output}out vs ${counts.input}in`);
        }
    }

    return {
        isBalanced: imbalances.length === 0,
        imbalances,
        portStats
    };
}

// 当前有问题的实现（从game.js推测）
function generateTemporaryNodesBuggy() {
    console.log('\n🔧 测试当前有问题的实现...');
    
    mockGameState.temporaryNodes = [];
    
    // 这可能是问题所在 - 随机生成端口类型
    const tempNode = new TestNode('normal');
    tempNode.addInputPort('square', '#ff5252');
    tempNode.addOutputPort('diamond', '#2196F3'); // 类型不匹配！
    
    mockGameState.temporaryNodes.push(tempNode);
    console.log('❌ 生成临时节点: 输入square-#ff5252, 输出diamond-#2196F3');
}

// 修复版本
function generateTemporaryNodesFixed() {
    console.log('\n🔧 测试修复版本...');
    
    mockGameState.temporaryNodes = [];
    
    // 分析现有端口需求
    const allNodes = [...mockGameState.nodes];
    const requiredTypes = new Set();
    
    // 收集所有需要处理的端口类型
    allNodes.forEach(node => {
        if (node.type === 'start') {
            node.outputPorts.forEach(port => {
                requiredTypes.add(`${port.type}-${port.color}`);
            });
        }
    });
    
    console.log(`🎯 需要处理的端口类型: ${Array.from(requiredTypes).join(', ')}`);
    
    // 为每种类型创建匹配的桥接节点
    let nodeIndex = 0;
    for (const portTypeKey of requiredTypes) {
        const [type, color] = portTypeKey.split('-');
        
        const bridgeNode = new TestNode('normal');
        bridgeNode.addInputPort(type, color);
        bridgeNode.addOutputPort(type, color);
        
        mockGameState.temporaryNodes.push(bridgeNode);
        console.log(`✅ 生成桥接节点 ${nodeIndex + 1}: 输入${type}-${color}, 输出${type}-${color}`);
        nodeIndex++;
    }
}

// 运行测试
function runTest() {
    console.log('🧪 开始Mode 3 Level 1 问题诊断...\n');

    // 测试有问题的版本
    console.log('=== 测试有问题的实现 ===');
    createMode3BasicScenario();
    generateTemporaryNodesBuggy();
    const buggyResult = analyzePortBalance();
    console.log(`结果: ${buggyResult.isBalanced ? '✅ 可解' : '❌ 不可解'}`);
    if (!buggyResult.isBalanced) {
        console.log(`不平衡项: ${buggyResult.imbalances.join(', ')}`);
    }

    // 测试修复版本
    console.log('\n=== 测试修复版本 ===');
    createMode3BasicScenario();
    generateTemporaryNodesFixed();
    const fixedResult = analyzePortBalance();
    console.log(`结果: ${fixedResult.isBalanced ? '✅ 可解' : '❌ 不可解'}`);
    if (!fixedResult.isBalanced) {
        console.log(`不平衡项: ${fixedResult.imbalances.join(', ')}`);
    }

    console.log('\n📝 诊断结论:');
    if (!buggyResult.isBalanced && fixedResult.isBalanced) {
        console.log('✅ 问题确认: 端口类型不匹配导致不可解');
        console.log('✅ 修复方案: 确保临时节点端口类型与起点输出/终点输入匹配');
        return true;
    } else {
        console.log('⚠️ 需要进一步调试');
        return false;
    }
}

const testResult = runTest();

if (testResult) {
    console.log('\n🔧 推荐的修复代码:');
    console.log(`
function generateConstraintSatisfyingTemporaryNodes(constraints) {
    const temporaryNodes = [];
    
    // 分析起点输出端口类型
    const startNodes = gameState.nodes.filter(n => n.type === 'start');
    const requiredTypes = new Set();
    
    startNodes.forEach(node => {
        node.outputPorts.forEach(port => {
            requiredTypes.add(\`\${port.type}-\${port.color}\`);
        });
    });
    
    // 为每种端口类型创建匹配的桥接节点
    let nodeIndex = 0;
    for (const portTypeKey of requiredTypes) {
        const [type, color] = portTypeKey.split('-');
        
        const bridgeNode = new Node('normal');
        bridgeNode.id = \`bridge_\${Date.now()}_\${nodeIndex}\`;
        bridgeNode.label = \`Bridge-\${nodeIndex + 1}\`;
        
        // 确保端口类型完全匹配
        bridgeNode.addInputPort(type, color);
        bridgeNode.addOutputPort(type, color);
        
        temporaryNodes.push(bridgeNode);
        nodeIndex++;
    }
    
    return temporaryNodes;
}
    `);
}

console.log('\n✅ 测试完成');