<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Algorithm Diagnostic</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .diagnostic-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 500px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Algorithm Diagnostic</h1>
        
        <button onclick="runDiagnostic()">🔍 Run Diagnostic</button>
        <button onclick="testGameIntegration()">🎮 Test Game Integration</button>
        <button onclick="clearOutput()">🗑️ Clear Output</button>
        
        <div class="diagnostic-output" id="diagnosticOutput">
            <div class="info">Ready to run algorithm diagnostic...</div>
        </div>
    </div>

    <script src="game.js?v=layered-dag-algorithm-2025"></script>
    
    <script>
        let errorLog = [];
        
        // Capture all console errors
        const originalError = console.error;
        console.error = function(...args) {
            errorLog.push({
                type: 'error',
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            originalError.apply(console, args);
        };
        
        const originalWarn = console.warn;
        console.warn = function(...args) {
            errorLog.push({
                type: 'warning',
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            originalWarn.apply(console, args);
        };
        
        function log(message, type = 'info') {
            const output = document.getElementById('diagnosticOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const div = document.createElement('div');
            div.className = type;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('diagnosticOutput').innerHTML = '';
            errorLog = [];
        }
        
        function runDiagnostic() {
            clearOutput();
            log('🔍 Running comprehensive diagnostic...', 'info');
            
            // Test 1: Check if all required functions exist
            log('=== Function Availability ===', 'info');
            const requiredFunctions = [
                'generateDeterministicSolvableScenario',
                'calculateLayeredScenarioSpec',
                'generateLayeredPortPlan',
                'buildLayeredScenarioFromPortPlan',
                'validateLayeredScenarioCompletely',
                'calculateMaxDepth',
                'calculatePortPairs'
            ];
            
            requiredFunctions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log('✅ ' + funcName + ': Available', 'success');
                } else {
                    log('❌ ' + funcName + ': Missing', 'error');
                }
            });
            
            // Test 2: Test basic algorithm execution
            log('\n=== Algorithm Execution Test ===', 'info');
            try {
                const testDifficulty = {
                    level: 2,
                    availableTypes: ['square', 'circle'],
                    availableColors: ['#ff5252', '#2196F3']
                };
                
                log('Testing with difficulty: ' + JSON.stringify(testDifficulty), 'info');
                
                const startTime = performance.now();
                const scenario = generateDeterministicSolvableScenario(testDifficulty);
                const endTime = performance.now();
                
                if (scenario) {
                    log('✅ Algorithm executed successfully (' + (endTime - startTime).toFixed(2) + 'ms)', 'success');
                    
                    // Detailed analysis
                    log('Scenario details:', 'info');
                    log('  Start node: ' + (scenario.startNode ? 'YES' : 'NO'), 'info');
                    log('  End node: ' + (scenario.endNode ? 'YES' : 'NO'), 'info');
                    log('  Intermediate nodes: ' + (scenario.nodes ? scenario.nodes.length : 0), 'info');
                    log('  Connections: ' + (scenario.guaranteedConnections ? scenario.guaranteedConnections.length : 0), 'info');
                    
                    if (scenario.startNode) {
                        log('  Start outputs: ' + scenario.startNode.outputPorts.length, 'info');
                        log('  Start inputs: ' + scenario.startNode.inputPorts.length, 'info');
                        log('  Start depth: ' + scenario.startNode.depth, 'info');
                    }
                    
                    if (scenario.endNode) {
                        log('  End inputs: ' + scenario.endNode.inputPorts.length, 'info');
                        log('  End outputs: ' + scenario.endNode.outputPorts.length, 'info');
                        log('  End depth: ' + scenario.endNode.depth, 'info');
                    }
                    
                } else {
                    log('❌ Algorithm returned null', 'error');
                }
                
            } catch (error) {
                log('❌ Algorithm execution failed: ' + error.message, 'error');
                log('Stack: ' + error.stack, 'error');
            }
            
            // Test 3: Check for any captured errors
            log('\n=== Error Log Analysis ===', 'info');
            if (errorLog.length === 0) {
                log('✅ No errors or warnings captured', 'success');
            } else {
                log('⚠️ Found ' + errorLog.length + ' issues:', 'warning');
                errorLog.forEach((entry, index) => {
                    log((index + 1) + '. [' + entry.type.toUpperCase() + '] ' + entry.message, entry.type === 'error' ? 'error' : 'warning');
                });
            }
            
            // Test 4: Memory and performance check
            log('\n=== Performance Check ===', 'info');
            try {
                const iterations = 10;
                const times = [];
                
                for (let i = 0; i < iterations; i++) {
                    const start = performance.now();
                    generateDeterministicSolvableScenario({
                        level: 3,
                        availableTypes: ['square', 'circle'],
                        availableColors: ['#ff5252', '#2196F3']
                    });
                    const end = performance.now();
                    times.push(end - start);
                }
                
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                const maxTime = Math.max(...times);
                const minTime = Math.min(...times);
                
                log('Performance over ' + iterations + ' iterations:', 'info');
                log('  Average: ' + avgTime.toFixed(2) + 'ms', 'info');
                log('  Min: ' + minTime.toFixed(2) + 'ms', 'info');
                log('  Max: ' + maxTime.toFixed(2) + 'ms', 'info');
                
                if (avgTime < 100) {
                    log('✅ Performance is good', 'success');
                } else if (avgTime < 500) {
                    log('⚠️ Performance is acceptable', 'warning');
                } else {
                    log('❌ Performance is poor', 'error');
                }
                
            } catch (error) {
                log('❌ Performance test failed: ' + error.message, 'error');
            }
            
            log('\n🎉 Diagnostic completed', 'success');
        }
        
        function testGameIntegration() {
            clearOutput();
            log('🎮 Testing game integration...', 'info');
            
            try {
                // Test if game state exists
                if (typeof gameState !== 'undefined') {
                    log('✅ gameState is available', 'success');
                } else {
                    log('⚠️ gameState is not defined (may be normal)', 'warning');
                }
                
                // Test puzzle level generation
                if (typeof generatePuzzleLevel === 'function') {
                    log('✅ generatePuzzleLevel function available', 'success');
                    
                    try {
                        // This might fail if gameState is not initialized, which is expected
                        log('Testing puzzle level generation...', 'info');
                        // We won't actually call it as it requires game state
                        log('⚠️ Skipping actual call (requires game state)', 'warning');
                    } catch (error) {
                        log('⚠️ Puzzle generation test skipped: ' + error.message, 'warning');
                    }
                } else {
                    log('❌ generatePuzzleLevel function not found', 'error');
                }
                
                // Test validation functions
                if (typeof validateTransformationChainExecution === 'function') {
                    log('✅ validateTransformationChainExecution function available', 'success');
                    
                    // Test with safe inputs
                    const testChains = [
                        null,
                        undefined,
                        {},
                        { source: { type: 'square', color: '#ff5252' }, target: { type: 'circle', color: '#2196F3' } }
                    ];
                    
                    testChains.forEach((chain, index) => {
                        try {
                            const result = validateTransformationChainExecution(chain);
                            log('Chain test ' + (index + 1) + ': ' + (result ? 'VALID' : 'INVALID'), 'info');
                        } catch (error) {
                            log('Chain test ' + (index + 1) + ': ERROR - ' + error.message, 'error');
                        }
                    });
                } else {
                    log('❌ validateTransformationChainExecution function not found', 'error');
                }
                
                log('🎮 Game integration test completed', 'success');
                
            } catch (error) {
                log('❌ Game integration test failed: ' + error.message, 'error');
            }
        }
        
        // Auto-run diagnostic on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, ready to run diagnostic', 'info');
            }, 500);
        });
        
        // Catch global errors
        window.addEventListener('error', (event) => {
            errorLog.push({
                type: 'error',
                message: 'Global error: ' + event.message + ' at line ' + event.lineno,
                timestamp: new Date().toISOString()
            });
            log('GLOBAL ERROR: ' + event.message + ' at line ' + event.lineno, 'error');
        });
    </script>
</body>
</html>
