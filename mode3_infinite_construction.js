// ========== Mode 3 无限构筑模式 ==========

// 游戏状态
const mode3InfiniteState = {
    // 节点和连接
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    
    // Canvas引用
    temporaryCanvas: null,
    temporaryCtx: null,
    gameCanvas: null,
    gameCtx: null,
    
    // 交互状态
    draggingNode: null,
    selectedPort: null,
    
    // Mode 3 无限构筑状态
    currentRound: 1,
    compensationAlgorithm: null,
    gamePhase: 'building', // 'building', 'validating', 'modifying'
    
    // 游戏历史
    roundHistory: [],
    modificationHistory: [],
    
    // 配置
    roundConfig: {
        targetNewNodes: 2,
        maxCompensationNodes: 5,
        modificationProbability: 0.7
    }
};

// ========== 初始化 ==========

function initializeMode3Infinite() {
    console.log('[MODE3-INFINITE] Initializing infinite construction mode');
    
    // 获取Canvas元素
    mode3InfiniteState.temporaryCanvas = document.getElementById('temporaryCanvas');
    mode3InfiniteState.gameCanvas = document.getElementById('gameCanvas');
    
    if (!mode3InfiniteState.temporaryCanvas || !mode3InfiniteState.gameCanvas) {
        console.error('[MODE3-INFINITE] Canvas elements not found');
        return false;
    }
    
    mode3InfiniteState.temporaryCtx = mode3InfiniteState.temporaryCanvas.getContext('2d');
    mode3InfiniteState.gameCtx = mode3InfiniteState.gameCanvas.getContext('2d');
    
    // 初始化分层补偿算法
    mode3InfiniteState.compensationAlgorithm = new HierarchicalCompensationAlgorithm();
    
    // 设置事件监听器
    setupMode3InfiniteEventListeners();
    
    // 生成初始轮次
    generateInitialRound();

    // 启动端口平衡监控
    startPortBalanceMonitoring();

    updateMode3InfiniteDisplay();

    console.log('[MODE3-INFINITE] Infinite construction mode initialized');
    return true;
}

function setupMode3InfiniteEventListeners() {
    [mode3InfiniteState.temporaryCanvas, mode3InfiniteState.gameCanvas].forEach(canvas => {
        canvas.addEventListener('mousedown', handleMode3InfiniteMouseDown);
        canvas.addEventListener('mousemove', handleMode3InfiniteMouseMove);
        canvas.addEventListener('mouseup', handleMode3InfiniteMouseUp);
    });
    
    document.addEventListener('keydown', handleMode3InfiniteKeyDown);
}

// ========== 游戏循环实现 ==========

function generateInitialRound() {
    console.log('[MODE3-INFINITE] Generating initial round');
    
    // 创建初始节点池
    const initialNodes = [
        createInfiniteNode('start_1', 'start', 0, [], [
            { type: 'square', color: 'red' }
        ]),
        createInfiniteNode('mid_1', 'intermediate', 500, [
            { type: 'square', color: 'red' }
        ], [
            { type: 'circle', color: 'blue' }
        ]),
        createInfiniteNode('end_1', 'end', 1000, [
            { type: 'circle', color: 'blue' }
        ], [])
    ];
    
    // 分配到游戏区域
    initialNodes.forEach((node, index) => {
        node.x = 200 + index * 150;
        node.y = 200;
        node.area = 'placed';
    });
    
    mode3InfiniteState.placedNodes = initialNodes;
    mode3InfiniteState.temporaryNodes = [];
    
    // 记录初始状态
    recordRoundState('initial', initialNodes, []);
    
    // 生成第一轮新节点
    generateNextRoundNodes();
}

function createInfiniteNode(id, type, depth, inputPortSpecs, outputPortSpecs) {
    const node = {
        id: id || `inf_node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: type,
        depth: depth,
        inputPorts: [],
        outputPorts: [],
        createdAt: Date.now(),
        round: mode3InfiniteState.currentRound
    };
    
    // 创建输入端口
    inputPortSpecs.forEach((spec, index) => {
        node.inputPorts.push({
            id: `${node.id}_in_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'input',
            nodeId: node.id,
            portTypeKey: `${spec.type}-${spec.color}`,
            x: 0,
            y: 0
        });
    });
    
    // 创建输出端口
    outputPortSpecs.forEach((spec, index) => {
        node.outputPorts.push({
            id: `${node.id}_out_${index}`,
            type: spec.type,
            color: spec.color,
            side: 'output',
            nodeId: node.id,
            portTypeKey: `${spec.type}-${spec.color}`,
            x: 0,
            y: 0
        });
    });
    
    return node;
}

// ========== 游戏循环核心逻辑 ==========

function gameLoop() {
    console.log('[MODE3-INFINITE] Starting game loop for round', mode3InfiniteState.currentRound);
    
    switch (mode3InfiniteState.gamePhase) {
        case 'building':
            // 玩家构建阶段 - 等待玩家操作
            break;
            
        case 'validating':
            // 验证阶段
            validateCurrentSolution();
            break;
            
        case 'modifying':
            // 修改阶段
            applyRandomModifications();
            break;
    }
}

function validateCurrentSolution() {
    console.log('[MODE3-INFINITE] Validating current solution');

    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    const connections = mode3InfiniteState.connections;

    // 首先验证端口平衡（SYSTEM.md关键约束）
    const portBalanceValidation = validatePortBalance(allNodes);

    // 然后验证连接的完整性和正确性
    const connectionValidation = validateSolution(allNodes, connections);

    const isValid = portBalanceValidation.isValid && connectionValidation.isValid;

    if (isValid) {
        console.log('[MODE3-INFINITE] Solution validated successfully');
        mode3InfiniteState.gamePhase = 'modifying';

        // 显示成功消息
        showValidationResult(true, 'Round completed successfully! All SYSTEM.md constraints satisfied.');

        // 延迟进入修改阶段
        setTimeout(() => {
            applyRandomModifications();
        }, 2000);

    } else {
        console.log('[MODE3-INFINITE] Solution validation failed');
        mode3InfiniteState.gamePhase = 'building';

        // 合并错误消息
        const errors = [];
        if (!portBalanceValidation.isValid) {
            errors.push(...portBalanceValidation.errors);
        }
        if (!connectionValidation.isValid) {
            errors.push(...connectionValidation.errors);
        }

        // 显示错误消息
        showValidationResult(false, errors.join(', '));
    }
}

// 新增：端口平衡验证函数
function validatePortBalance(nodes) {
    console.log('[MODE3-INFINITE] Validating port balance across all nodes');

    const validation = {
        isValid: true,
        errors: []
    };

    if (!mode3InfiniteState.compensationAlgorithm) {
        validation.isValid = false;
        validation.errors.push('Compensation algorithm not available');
        return validation;
    }

    // 使用补偿算法计算端口平衡
    const portBalance = mode3InfiniteState.compensationAlgorithm.calculatePortBalance(nodes);

    // 检查每种端口类型的平衡
    portBalance.forEach((stat, portType) => {
        if (stat.delta !== 0) {
            validation.isValid = false;
            const deficitType = stat.delta > 0 ? 'excess outputs' : 'deficit outputs';
            validation.errors.push(`${portType}: ${deficitType} (Δ${stat.delta})`);

            console.warn(`[MODE3-INFINITE] Port balance violation: ${portType} has delta=${stat.delta}`);
        } else {
            console.log(`[MODE3-INFINITE] Port balance OK: ${portType} is balanced`);
        }
    });

    if (validation.isValid) {
        console.log('[MODE3-INFINITE] ✅ All port types are perfectly balanced');
    } else {
        console.error('[MODE3-INFINITE] ❌ Port balance violations detected:', validation.errors);
    }

    return validation;
}

function validateSolution(nodes, connections) {
    const validation = {
        isValid: true,
        errors: []
    };
    
    // 验证1: 所有端口都已连接
    const connectedPorts = new Set();
    connections.forEach(conn => {
        connectedPorts.add(conn.fromPort);
        connectedPorts.add(conn.toPort);
    });
    
    let unconnectedPorts = 0;
    nodes.forEach(node => {
        [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
            if (!connectedPorts.has(port.id)) {
                unconnectedPorts++;
            }
        });
    });
    
    if (unconnectedPorts > 0) {
        validation.isValid = false;
        validation.errors.push(`${unconnectedPorts} ports are not connected`);
    }
    
    // 验证2: 连接类型匹配
    connections.forEach(conn => {
        const fromPort = findPortById(conn.fromPort);
        const toPort = findPortById(conn.toPort);
        
        if (fromPort && toPort) {
            const fromType = `${fromPort.type}-${fromPort.color}`;
            const toType = `${toPort.type}-${toPort.color}`;
            
            if (fromType !== toType) {
                validation.isValid = false;
                validation.errors.push(`Type mismatch: ${fromType} -> ${toType}`);
            }
        }
    });
    
    // 验证3: 拓扑结构（简化检查）
    const hasStartNodes = nodes.some(n => n.type === 'start');
    const hasEndNodes = nodes.some(n => n.type === 'end');
    
    if (!hasStartNodes) {
        validation.isValid = false;
        validation.errors.push('No start nodes found');
    }
    
    if (!hasEndNodes) {
        validation.isValid = false;
        validation.errors.push('No end nodes found');
    }
    
    return validation;
}

function applyRandomModifications() {
    console.log('[MODE3-INFINITE] Applying random modifications');
    
    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    
    // 生成随机修改操作
    const modifications = generateRandomModifications(allNodes);
    
    console.log('[MODE3-INFINITE] Generated modifications:', modifications);
    
    // 使用补偿算法生成新的节点池 - 传递完整游戏状态
    const gameStateForAlgorithm = {
        temporaryNodes: mode3InfiniteState.temporaryNodes,
        placedNodes: mode3InfiniteState.placedNodes,
        connections: mode3InfiniteState.connections
    };

    // 传递波次信息给分层补偿算法
    const enhancedRoundConfig = {
        ...mode3InfiniteState.roundConfig,
        wave: mode3InfiniteState.currentRound
    };

    const newNodes = mode3InfiniteState.compensationAlgorithm.generateNextRound(
        allNodes,
        modifications,
        enhancedRoundConfig,
        gameStateForAlgorithm
    );
    
    // 分离新节点到临时区域和游戏区域
    const { temporaryNodes, placedNodes } = separateNodesByArea(newNodes);
    
    mode3InfiniteState.temporaryNodes = temporaryNodes;
    mode3InfiniteState.placedNodes = placedNodes;
    
    // 清除无效连接
    cleanupInvalidConnections();
    
    // 记录轮次状态
    recordRoundState('modified', newNodes, modifications);
    
    // 进入下一轮
    mode3InfiniteState.currentRound++;
    mode3InfiniteState.gamePhase = 'building';
    
    updateMode3InfiniteDisplay();
    
    console.log('[MODE3-INFINITE] Round', mode3InfiniteState.currentRound, 'ready');
}

function generateRandomModifications(nodes) {
    console.log('[MODE3-INFINITE] Generating gentle modifications to preserve game continuity');

    const modifications = [];
    const currentWave = mode3InfiniteState.currentRound;

    // 温和的修改策略：早期波次只做端口修改，后期才考虑节点修改
    let opCount;
    let modificationWeights;

    if (currentWave <= 3) {
        // 前3波：只修改端口，不删除节点
        opCount = 1;
        modificationWeights = [
            { type: 'add_port', weight: 0.7 },
            { type: 'del_port', weight: 0.3 }
        ];
    } else if (currentWave <= 10) {
        // 4-10波：主要端口修改，少量节点添加
        opCount = Math.min(2, 1 + Math.floor(currentWave / 5));
        modificationWeights = [
            { type: 'add_node', weight: 0.2 },
            { type: 'add_port', weight: 0.5 },
            { type: 'del_port', weight: 0.3 }
        ];
    } else {
        // 10波后：允许更复杂的修改，但仍然保守
        opCount = Math.min(3, 2 + Math.floor(currentWave / 10));
        modificationWeights = [
            { type: 'add_node', weight: 0.3 },
            { type: 'del_node', weight: 0.1 }, // 很低的删除概率
            { type: 'add_port', weight: 0.4 },
            { type: 'del_port', weight: 0.2 }
        ];
    }

    for (let i = 0; i < opCount; i++) {
        const modType = weightedChoice(modificationWeights);
        const modification = createIntelligentModification(modType, nodes, currentWave);

        if (modification) {
            modifications.push(modification);
        }
    }

    console.log(`[MODE3-INFINITE] Generated ${modifications.length} gentle modifications for wave ${currentWave}`);
    return modifications;
}

function weightedChoice(choices) {
    const totalWeight = choices.reduce((sum, choice) => sum + choice.weight, 0);
    let random = Math.random() * totalWeight;

    for (const choice of choices) {
        random -= choice.weight;
        if (random <= 0) {
            return choice.type;
        }
    }

    return choices[0].type; // 回退
}

function createIntelligentModification(modType, nodes, wave) {
    switch (modType) {
        case 'add_node':
            return createAddNodeModification(wave);

        case 'del_node':
            return createDelNodeModification(nodes);

        case 'add_port':
            return createAddPortModification(nodes, wave);

        case 'del_port':
            return createDelPortModification(nodes);

        default:
            return null;
    }
}

function createAddNodeModification(wave) {
    // 智能确定节点类型
    let nodeType;
    if (wave <= 5) {
        nodeType = ['start', 'intermediate', 'end'][Math.floor(Math.random() * 3)];
    } else {
        // 后期更倾向于中间节点，增加复杂性
        const typeWeights = [
            { type: 'start', weight: 0.2 },
            { type: 'intermediate', weight: 0.6 },
            { type: 'end', weight: 0.2 }
        ];
        nodeType = weightedChoice(typeWeights);
    }

    return {
        type: 'add_node',
        nodeType: nodeType,
        inputCount: nodeType === 'start' ? 0 : Math.floor(Math.random() * 3) + 1,
        outputCount: nodeType === 'end' ? 0 : Math.floor(Math.random() * 3) + 1
    };
}

function createDelNodeModification(nodes) {
    // 非常保守的节点删除策略
    const intermediateNodes = nodes.filter(n =>
        n.type === 'intermediate' &&
        !mode3InfiniteState.compensationAlgorithm.isSourceNode(n) &&
        !mode3InfiniteState.compensationAlgorithm.isSinkNode(n)
    );

    // 只有在中间节点数量很多时才考虑删除
    if (intermediateNodes.length > 3) {
        // 选择端口最少的中间节点删除，减少对平衡的影响
        const sortedByPorts = intermediateNodes.sort((a, b) => {
            const aPorts = (a.inputPorts?.length || 0) + (a.outputPorts?.length || 0);
            const bPorts = (b.inputPorts?.length || 0) + (b.outputPorts?.length || 0);
            return aPorts - bPorts;
        });

        const targetNode = sortedByPorts[0]; // 选择端口最少的
        console.log(`[MODE3-INFINITE] Considering deletion of node ${targetNode.id} with minimal ports`);

        return {
            type: 'del_node',
            nodeId: targetNode.id
        };
    }

    console.log('[MODE3-INFINITE] Skipping node deletion - not enough intermediate nodes');
    return null;
}

function createAddPortModification(nodes, wave) {
    if (nodes.length === 0) return null;

    const targetNode = nodes[Math.floor(Math.random() * nodes.length)];

    // 检查节点是否还能添加端口
    const currentPortCount = (targetNode.inputPorts?.length || 0) + (targetNode.outputPorts?.length || 0);
    if (currentPortCount >= 4) return null; // 最大端口限制

    // 智能选择端口类型（基于波次难度）
    const availablePortTypes = getAvailablePortTypesForWave(wave);
    const portType = availablePortTypes[Math.floor(Math.random() * availablePortTypes.length)];

    // 智能选择端口方向
    let side;
    if (mode3InfiniteState.compensationAlgorithm.isSourceNode(targetNode)) {
        side = 'output'; // 起点只能添加输出端口
    } else if (mode3InfiniteState.compensationAlgorithm.isSinkNode(targetNode)) {
        side = 'input'; // 终点只能添加输入端口
    } else {
        side = Math.random() < 0.5 ? 'input' : 'output';
    }

    return {
        type: 'add_port',
        nodeId: targetNode.id,
        portSpec: {
            type: portType.shape,
            color: portType.color,
            side: side
        }
    };
}

function createDelPortModification(nodes) {
    // 找到有多余端口的节点
    const nodesWithExtraPorts = nodes.filter(n => {
        const totalPorts = (n.inputPorts?.length || 0) + (n.outputPorts?.length || 0);
        return totalPorts > 1; // 至少保留一个端口
    });

    if (nodesWithExtraPorts.length === 0) return null;

    const targetNode = nodesWithExtraPorts[Math.floor(Math.random() * nodesWithExtraPorts.length)];
    const allPorts = [...(targetNode.inputPorts || []), ...(targetNode.outputPorts || [])];

    if (allPorts.length > 1) {
        const targetPort = allPorts[Math.floor(Math.random() * allPorts.length)];
        return {
            type: 'del_port',
            nodeId: targetNode.id,
            portId: targetPort.id
        };
    }

    return null;
}

function getAvailablePortTypesForWave(wave) {
    const allPortTypes = [
        { shape: 'square', color: 'red' },
        { shape: 'circle', color: 'blue' },
        { shape: 'triangle', color: 'green' },
        { shape: 'diamond', color: 'yellow' }
    ];

    // 根据波次逐渐增加端口类型
    if (wave <= 5) {
        return allPortTypes.slice(0, 2); // 前5波只用2种类型
    } else if (wave <= 10) {
        return allPortTypes.slice(0, 3); // 6-10波用3种类型
    } else {
        return allPortTypes; // 11波后用全部类型
    }
}

function separateNodesByArea(nodes) {
    const temporaryNodes = [];
    const placedNodes = [];

    nodes.forEach(node => {
        // 检查是否是新生成的节点（补偿、紧急修复、桥接）
        if (node.createdBy === 'compensation' ||
            node.createdBy === 'emergency_fix' ||
            node.createdBy === 'layer_bridge') {
            // 新生成的节点放入临时区域
            node.area = 'temporary';
            node.x = 50;
            node.y = 50 + temporaryNodes.length * 80;
            temporaryNodes.push(node);
        } else {
            // 现有节点保持在游戏区域
            node.area = 'placed';
            placedNodes.push(node);
        }
    });

    console.log(`[MODE3-INFINITE] Separated nodes: ${temporaryNodes.length} temporary, ${placedNodes.length} placed`);
    return { temporaryNodes, placedNodes };
}

function cleanupInvalidConnections() {
    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    const validPortIds = new Set();
    
    allNodes.forEach(node => {
        [...(node.inputPorts || []), ...(node.outputPorts || [])].forEach(port => {
            validPortIds.add(port.id);
        });
    });
    
    const validConnections = mode3InfiniteState.connections.filter(conn => 
        validPortIds.has(conn.fromPort) && validPortIds.has(conn.toPort)
    );
    
    const removedCount = mode3InfiniteState.connections.length - validConnections.length;
    if (removedCount > 0) {
        console.log('[MODE3-INFINITE] Removed', removedCount, 'invalid connections');
        showMessage(`${removedCount} invalid connections were automatically removed`);
    }
    
    mode3InfiniteState.connections = validConnections;
}

function generateNextRoundNodes() {
    console.log('[MODE3-INFINITE] Generating next round nodes');
    
    // 使用补偿算法生成新节点 - 传递完整游戏状态
    const existingNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    const gameStateForAlgorithm = {
        temporaryNodes: mode3InfiniteState.temporaryNodes,
        placedNodes: mode3InfiniteState.placedNodes,
        connections: mode3InfiniteState.connections
    };

    // 传递波次信息给分层补偿算法
    const enhancedRoundConfig = {
        ...mode3InfiniteState.roundConfig,
        wave: mode3InfiniteState.currentRound
    };

    const newNodes = mode3InfiniteState.compensationAlgorithm.generateNextRound(
        existingNodes,
        [], // 没有修改，只是添加新节点
        enhancedRoundConfig,
        gameStateForAlgorithm
    );
    
    // 只取新生成的节点
    const addedNodes = newNodes.filter(node => 
        !existingNodes.some(existing => existing.id === node.id)
    );
    
    // 定位到临时区域
    addedNodes.forEach((node, index) => {
        node.x = 50;
        node.y = 50 + index * 80;
        node.area = 'temporary';
    });
    
    mode3InfiniteState.temporaryNodes.push(...addedNodes);
    
    console.log('[MODE3-INFINITE] Added', addedNodes.length, 'new nodes to temporary area');
}

// ========== 辅助函数 ==========

function recordRoundState(phase, nodes, modifications) {
    const record = {
        round: mode3InfiniteState.currentRound,
        phase: phase,
        timestamp: Date.now(),
        nodeCount: nodes.length,
        modifications: modifications,
        connections: mode3InfiniteState.connections.length
    };
    
    mode3InfiniteState.roundHistory.push(record);
    
    if (modifications.length > 0) {
        mode3InfiniteState.modificationHistory.push({
            round: mode3InfiniteState.currentRound,
            modifications: modifications,
            timestamp: Date.now()
        });
    }
}

function findPortById(portId) {
    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    for (const node of allNodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        const port = allPorts.find(p => p.id === portId);
        if (port) return port;
    }
    return null;
}

function showValidationResult(isValid, message) {
    const statusElement = document.getElementById('validation-status');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = isValid ? 'status-good' : 'status-error';
    }
    
    // 也可以显示临时消息
    showMessage(message);
}

function showMessage(message) {
    console.log('[MODE3-INFINITE] Message:', message);
    
    // 创建临时消息显示
    const messageDiv = document.createElement('div');
    messageDiv.textContent = message;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #333;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 1000;
        font-family: 'Courier New', monospace;
    `;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        document.body.removeChild(messageDiv);
    }, 3000);
}

// ========== 渲染系统 ==========

function updateMode3InfiniteDisplay() {
    clearMode3InfiniteCanvases();
    drawMode3InfiniteNodes();
    drawMode3InfiniteConnections();
    drawMode3InfiniteUI();
}

function clearMode3InfiniteCanvases() {
    if (mode3InfiniteState.temporaryCtx) {
        mode3InfiniteState.temporaryCtx.clearRect(0, 0, mode3InfiniteState.temporaryCanvas.width, mode3InfiniteState.temporaryCanvas.height);
    }
    if (mode3InfiniteState.gameCtx) {
        mode3InfiniteState.gameCtx.clearRect(0, 0, mode3InfiniteState.gameCanvas.width, mode3InfiniteState.gameCanvas.height);
    }
}

function drawMode3InfiniteNodes() {
    // 绘制临时节点
    mode3InfiniteState.temporaryNodes.forEach(node => {
        drawMode3InfiniteNode(node, mode3InfiniteState.temporaryCtx);
    });
    
    // 绘制放置节点
    mode3InfiniteState.placedNodes.forEach(node => {
        drawMode3InfiniteNode(node, mode3InfiniteState.gameCtx);
    });
}

function drawMode3InfiniteNode(node, ctx) {
    const nodeSize = 60;
    const portSize = 12;
    
    // 根据创建者着色
    let nodeColor = getMode3InfiniteNodeColor(node.type);
    if (node.createdBy === 'compensation') {
        nodeColor = '#4CAF50'; // 绿色表示补偿节点
    } else if (node.createdBy === 'emergency_fix') {
        nodeColor = '#ff9800'; // 橙色表示紧急修复节点
    }
    
    // 绘制节点主体
    ctx.fillStyle = nodeColor;
    ctx.fillRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制节点边框
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    ctx.strokeRect(node.x - nodeSize/2, node.y - nodeSize/2, nodeSize, nodeSize);
    
    // 绘制端口
    drawMode3InfiniteNodePorts(node, ctx, nodeSize, portSize);
    
    // 绘制节点标签
    ctx.fillStyle = 'white';
    ctx.font = '10px Arial';
    ctx.textAlign = 'center';
    ctx.fillText(node.id.substring(0, 8), node.x, node.y + 3);
    
    // 绘制轮次和深度信息
    ctx.fillStyle = 'rgba(255,255,255,0.7)';
    ctx.font = '8px Arial';
    ctx.fillText(`R:${node.round || 1}`, node.x - 20, node.y - 25);
    ctx.fillText(`D:${node.depth || 0}`, node.x + 20, node.y - 25);
}

function drawMode3InfiniteNodePorts(node, ctx, nodeSize, portSize) {
    // 输入端口（左侧）
    if (node.inputPorts && node.inputPorts.length > 0) {
        const spacing = nodeSize / (node.inputPorts.length + 1);
        node.inputPorts.forEach((port, index) => {
            const portX = node.x - nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3InfinitePort(ctx, portX, portY, port, portSize);
        });
    }
    
    // 输出端口（右侧）
    if (node.outputPorts && node.outputPorts.length > 0) {
        const spacing = nodeSize / (node.outputPorts.length + 1);
        node.outputPorts.forEach((port, index) => {
            const portX = node.x + nodeSize/2;
            const portY = node.y - nodeSize/2 + spacing * (index + 1);
            
            port.x = portX;
            port.y = portY;
            
            drawMode3InfinitePort(ctx, portX, portY, port, portSize);
        });
    }
}

function drawMode3InfinitePort(ctx, x, y, port, size) {
    ctx.fillStyle = port.color;
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 1;
    
    switch (port.type) {
        case 'square':
            ctx.fillRect(x - size/2, y - size/2, size, size);
            ctx.strokeRect(x - size/2, y - size/2, size, size);
            break;
        case 'circle':
            ctx.beginPath();
            ctx.arc(x, y, size/2, 0, 2 * Math.PI);
            ctx.fill();
            ctx.stroke();
            break;
        case 'triangle':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x - size/2, y + size/2);
            ctx.lineTo(x + size/2, y + size/2);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
        case 'diamond':
            ctx.beginPath();
            ctx.moveTo(x, y - size/2);
            ctx.lineTo(x + size/2, y);
            ctx.lineTo(x, y + size/2);
            ctx.lineTo(x - size/2, y);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            break;
    }
}

function getMode3InfiniteNodeColor(type) {
    switch (type) {
        case 'start': return '#4CAF50';
        case 'end': return '#f44336';
        case 'intermediate': return '#2196F3';
        default: return '#666';
    }
}

function drawMode3InfiniteConnections() {
    mode3InfiniteState.connections.forEach(connection => {
        const fromPort = findPortById(connection.fromPort);
        const toPort = findPortById(connection.toPort);
        
        if (fromPort && toPort) {
            drawMode3InfiniteConnectionLine(mode3InfiniteState.gameCtx, fromPort, toPort);
        }
    });
}

function drawMode3InfiniteConnectionLine(ctx, fromPort, toPort) {
    ctx.strokeStyle = fromPort.color;
    ctx.lineWidth = 3;
    ctx.beginPath();
    ctx.moveTo(fromPort.x, fromPort.y);
    ctx.lineTo(toPort.x, toPort.y);
    ctx.stroke();
}

function drawMode3InfiniteUI() {
    const ctx = mode3InfiniteState.gameCtx;
    
    // 绘制状态信息
    ctx.fillStyle = 'white';
    ctx.font = '14px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`Round: ${mode3InfiniteState.currentRound}`, 10, 25);
    ctx.fillText(`Phase: ${mode3InfiniteState.gamePhase}`, 10, 45);
    ctx.fillText(`Infinite Construction Mode`, 10, 65);
}

// ========== 事件处理 ==========

function handleMode3InfiniteMouseDown(event) {
    const rect = event.target.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    const isTemporaryArea = event.target === mode3InfiniteState.temporaryCanvas;
    
    // 检查端口点击
    const clickedPort = findInfinitePortAtPosition(x, y, isTemporaryArea);
    if (clickedPort) {
        handleMode3InfinitePortClick(clickedPort);
        return;
    }
    
    // 检查节点点击
    const clickedNode = findInfiniteNodeAtPosition(x, y, isTemporaryArea);
    if (clickedNode) {
        startMode3InfiniteNodeDrag(clickedNode, x, y);
    }
}

function handleMode3InfiniteMouseMove(event) {
    if (mode3InfiniteState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        mode3InfiniteState.draggingNode.node.x = x;
        mode3InfiniteState.draggingNode.node.y = y;
        updateMode3InfiniteDisplay();
    }
}

function handleMode3InfiniteMouseUp(event) {
    if (mode3InfiniteState.draggingNode) {
        const rect = event.target.getBoundingClientRect();
        const x = event.clientX - rect.left;
        const y = event.clientY - rect.top;
        
        finishMode3InfiniteNodeDrag(x, y, event.target === mode3InfiniteState.gameCanvas);
        mode3InfiniteState.draggingNode = null;
        updateMode3InfiniteDisplay();
    }
}

function handleMode3InfiniteKeyDown(event) {
    switch (event.key) {
        case 'v':
        case 'V':
            if (mode3InfiniteState.gamePhase === 'building') {
                mode3InfiniteState.gamePhase = 'validating';
                validateCurrentSolution();
            }
            break;
        case 'n':
        case 'N':
            generateNextRoundNodes();
            updateMode3InfiniteDisplay();
            break;
        case 'r':
        case 'R':
            resetInfiniteMode();
            break;
    }
}

// ========== 辅助函数 ==========

function findInfiniteNodeAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3InfiniteState.temporaryNodes : mode3InfiniteState.placedNodes;
    const nodeSize = 60;
    
    for (const node of nodes) {
        if (x >= node.x - nodeSize/2 && x <= node.x + nodeSize/2 &&
            y >= node.y - nodeSize/2 && y <= node.y + nodeSize/2) {
            return node;
        }
    }
    return null;
}

function findInfinitePortAtPosition(x, y, isTemporaryArea) {
    const nodes = isTemporaryArea ? mode3InfiniteState.temporaryNodes : mode3InfiniteState.placedNodes;
    const portSize = 12;
    
    for (const node of nodes) {
        const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
        for (const port of allPorts) {
            if (port.x !== undefined && port.y !== undefined) {
                if (x >= port.x - portSize/2 && x <= port.x + portSize/2 &&
                    y >= port.y - portSize/2 && y <= port.y + portSize/2) {
                    return port;
                }
            }
        }
    }
    return null;
}

function startMode3InfiniteNodeDrag(node, x, y) {
    mode3InfiniteState.draggingNode = {
        node: node,
        offsetX: x - node.x,
        offsetY: y - node.y
    };
}

function finishMode3InfiniteNodeDrag(x, y, isGameArea) {
    const node = mode3InfiniteState.draggingNode.node;
    
    if (isGameArea) {
        // 移动到游戏区域
        if (mode3InfiniteState.temporaryNodes.includes(node)) {
            mode3InfiniteState.temporaryNodes = mode3InfiniteState.temporaryNodes.filter(n => n !== node);
            mode3InfiniteState.placedNodes.push(node);
            node.area = 'placed';
        }
    } else {
        // 移动回临时区域
        if (mode3InfiniteState.placedNodes.includes(node)) {
            mode3InfiniteState.placedNodes = mode3InfiniteState.placedNodes.filter(n => n !== node);
            mode3InfiniteState.temporaryNodes.push(node);
            node.area = 'temporary';
        }
    }
}

function handleMode3InfinitePortClick(port) {
    if (!mode3InfiniteState.selectedPort) {
        mode3InfiniteState.selectedPort = port;
        console.log('[MODE3-INFINITE] Selected port', port.id);
    } else {
        if (canConnectInfinitePorts(mode3InfiniteState.selectedPort, port)) {
            createInfiniteConnection(mode3InfiniteState.selectedPort, port);
        }
        mode3InfiniteState.selectedPort = null;
    }
}

function canConnectInfinitePorts(fromPort, toPort) {
    // 检查端口类型匹配
    const fromType = fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`;
    const toType = toPort.portTypeKey || `${toPort.type}-${toPort.color}`;
    
    return fromType === toType && 
           fromPort.side !== toPort.side && 
           fromPort.nodeId !== toPort.nodeId;
}

function createInfiniteConnection(fromPort, toPort) {
    const connection = {
        id: `conn_${Date.now()}`,
        fromNode: fromPort.nodeId,
        fromPort: fromPort.id,
        toNode: toPort.nodeId,
        toPort: toPort.id,
        portType: fromPort.portTypeKey || `${fromPort.type}-${fromPort.color}`,
        round: mode3InfiniteState.currentRound
    };
    
    mode3InfiniteState.connections.push(connection);
    console.log('[MODE3-INFINITE] Created connection', connection.id);
    updateMode3InfiniteDisplay();
}

function resetInfiniteMode() {
    if (confirm('Reset infinite construction mode? This will start over from round 1.')) {
        mode3InfiniteState.currentRound = 1;
        mode3InfiniteState.gamePhase = 'building';
        mode3InfiniteState.temporaryNodes = [];
        mode3InfiniteState.placedNodes = [];
        mode3InfiniteState.connections = [];
        mode3InfiniteState.roundHistory = [];
        mode3InfiniteState.modificationHistory = [];
        
        generateInitialRound();
        updateMode3InfiniteDisplay();
    }
}

// ========== 端口平衡监控 ==========

function startPortBalanceMonitoring() {
    console.log('[MODE3-INFINITE] Starting port balance monitoring');

    // 每2秒检查一次端口平衡
    setInterval(() => {
        monitorPortBalance();
    }, 2000);

    // 立即执行一次检查
    monitorPortBalance();
}

function monitorPortBalance() {
    if (!mode3InfiniteState.compensationAlgorithm) return;

    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    const portBalance = mode3InfiniteState.compensationAlgorithm.calculatePortBalance(allNodes);

    // 更新UI显示
    updatePortBalanceDisplay(portBalance);

    // 检查是否有不平衡
    let hasImbalance = false;
    portBalance.forEach((stat, portType) => {
        if (stat.delta !== 0) {
            hasImbalance = true;
            console.warn(`[MODE3-INFINITE] Port imbalance detected: ${portType} delta=${stat.delta}`);
        }
    });

    // 如果有不平衡，触发自动修复
    if (hasImbalance && mode3InfiniteState.gamePhase === 'building') {
        console.log('[MODE3-INFINITE] Triggering automatic port balance correction');
        correctPortBalance();
    }
}

function updatePortBalanceDisplay(portBalance) {
    // 更新HTML中的端口平衡显示
    const balanceElement = document.getElementById('port-balance-status');
    if (balanceElement) {
        const balanceItems = [];
        let allBalanced = true;

        portBalance.forEach((stat, portType) => {
            const isBalanced = stat.delta === 0;
            if (!isBalanced) allBalanced = false;

            const statusClass = isBalanced ? 'status-good' : 'status-error';
            balanceItems.push(`<span class="${statusClass}">${portType}: Δ${stat.delta}</span>`);
        });

        balanceElement.innerHTML = balanceItems.join(' | ');
        balanceElement.className = allBalanced ? 'status-good' : 'status-error';
    }
}

function correctPortBalance() {
    console.log('[MODE3-INFINITE] Applying automatic port balance correction');

    const allNodes = [...mode3InfiniteState.temporaryNodes, ...mode3InfiniteState.placedNodes];
    const gameStateForAlgorithm = {
        temporaryNodes: mode3InfiniteState.temporaryNodes,
        placedNodes: mode3InfiniteState.placedNodes,
        connections: mode3InfiniteState.connections
    };

    // 使用紧急修复机制
    const correctedNodes = mode3InfiniteState.compensationAlgorithm.emergencyFixWithGameState(
        [], // 不添加新节点，只修复平衡
        gameStateForAlgorithm
    );

    // 将修复节点添加到临时区域
    correctedNodes.forEach((node, index) => {
        node.x = 50;
        node.y = 50 + (mode3InfiniteState.temporaryNodes.length + index) * 80;
        node.area = 'temporary';
    });

    mode3InfiniteState.temporaryNodes.push(...correctedNodes);

    console.log(`[MODE3-INFINITE] Added ${correctedNodes.length} correction nodes to temporary area`);
    updateMode3InfiniteDisplay();
}

// ========== 全局函数 ==========

window.initializeMode3Infinite = initializeMode3Infinite;
window.updateMode3InfiniteDisplay = updateMode3InfiniteDisplay;
window.validateCurrentSolution = validateCurrentSolution;
window.generateNextRoundNodes = generateNextRoundNodes;
window.resetInfiniteMode = resetInfiniteMode;
window.monitorPortBalance = monitorPortBalance;
window.correctPortBalance = correctPortBalance;

// ========== 自动初始化 ==========

document.addEventListener('DOMContentLoaded', function() {
    console.log('[MODE3-INFINITE] DOM loaded, initializing infinite construction mode...');
    setTimeout(() => {
        if (typeof CompensationAlgorithm !== 'undefined') {
            if (initializeMode3Infinite()) {
                console.log('[MODE3-INFINITE] Infinite construction mode ready!');
            }
        } else {
            console.error('[MODE3-INFINITE] CompensationAlgorithm not loaded');
        }
    }, 100);
});
