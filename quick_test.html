<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Algorithm Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Quick Algorithm Test</h1>
            <p>快速验证分层补偿算法的核心功能</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="testAlgorithmLoading()">🔧 Test Loading</button>
            <button class="btn" onclick="testBasicMethods()">📋 Test Methods</button>
            <button class="btn" onclick="testPortBalance()">⚖️ Test Balance</button>
            <button class="btn" onclick="testCompensation()">🔄 Test Compensation</button>
            <button class="btn" onclick="clearLog()">🗑️ Clear</button>
        </div>
        
        <div>
            <h3>📋 Test Log</h3>
            <div class="log" id="test-log"></div>
        </div>
    </div>

    <!-- Load the hierarchical compensation algorithm -->
    <script src="compensation_algorithm.js"></script>
    
    <script>
        let algorithm = null;
        
        // Logging system
        function logTest(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('test-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[QUICK-TEST] ${message}`);
        }
        
        // Test algorithm loading
        function testAlgorithmLoading() {
            logTest('=== Testing Algorithm Loading ===', 'info');
            
            try {
                // Test 1: Check if class exists
                if (typeof HierarchicalCompensationAlgorithm === 'undefined') {
                    logTest('❌ HierarchicalCompensationAlgorithm class not found', 'error');
                    return;
                }
                logTest('✅ HierarchicalCompensationAlgorithm class found', 'success');
                
                // Test 2: Create instance
                algorithm = new HierarchicalCompensationAlgorithm();
                if (!algorithm) {
                    logTest('❌ Failed to create algorithm instance', 'error');
                    return;
                }
                logTest('✅ Algorithm instance created successfully', 'success');
                
                // Test 3: Check backward compatibility
                if (typeof CompensationAlgorithm === 'undefined') {
                    logTest('❌ CompensationAlgorithm alias not found', 'error');
                    return;
                }
                logTest('✅ Backward compatibility alias found', 'success');
                
                // Test 4: Check port types
                if (!algorithm.portTypes || algorithm.portTypes.length === 0) {
                    logTest('❌ Port types not initialized', 'error');
                    return;
                }
                logTest(`✅ Port types initialized: ${algorithm.portTypes.length} types`, 'success');
                
                logTest('=== Algorithm Loading Test PASSED ===', 'success');
                
            } catch (error) {
                logTest(`❌ Algorithm loading failed: ${error.message}`, 'error');
            }
        }
        
        // Test basic methods
        function testBasicMethods() {
            logTest('=== Testing Basic Methods ===', 'info');
            
            if (!algorithm) {
                logTest('❌ Algorithm not loaded. Run loading test first.', 'error');
                return;
            }
            
            try {
                // Test required methods exist
                const requiredMethods = [
                    'calculatePortBalance',
                    'calculatePortImbalance', 
                    'compensate',
                    'validateStrongSolvability',
                    'generateNextRound',
                    'analyzeGeneration',
                    'emergencyFix'
                ];
                
                let allMethodsExist = true;
                requiredMethods.forEach(methodName => {
                    if (typeof algorithm[methodName] !== 'function') {
                        logTest(`❌ Method ${methodName} not found`, 'error');
                        allMethodsExist = false;
                    } else {
                        logTest(`✅ Method ${methodName} exists`, 'success');
                    }
                });
                
                if (allMethodsExist) {
                    logTest('=== Basic Methods Test PASSED ===', 'success');
                } else {
                    logTest('=== Basic Methods Test FAILED ===', 'error');
                }
                
            } catch (error) {
                logTest(`❌ Basic methods test failed: ${error.message}`, 'error');
            }
        }
        
        // Test port balance calculation
        function testPortBalance() {
            logTest('=== Testing Port Balance ===', 'info');
            
            if (!algorithm) {
                logTest('❌ Algorithm not loaded. Run loading test first.', 'error');
                return;
            }
            
            try {
                // Create simple test nodes
                const testNodes = [
                    {
                        id: 'test_source',
                        type: 'start',
                        level: 0,
                        depth: 0,
                        inputPorts: [],
                        outputPorts: [
                            { id: 'out1', type: 'square', color: 'red', side: 'output', nodeId: 'test_source', portTypeKey: 'square-red' }
                        ]
                    },
                    {
                        id: 'test_sink',
                        type: 'end',
                        level: 2,
                        depth: 200,
                        inputPorts: [
                            { id: 'in1', type: 'square', color: 'red', side: 'input', nodeId: 'test_sink', portTypeKey: 'square-red' }
                        ],
                        outputPorts: []
                    }
                ];
                
                // Test calculatePortBalance (backward compatibility)
                const balance1 = algorithm.calculatePortBalance(testNodes);
                if (!balance1 || !(balance1 instanceof Map)) {
                    logTest('❌ calculatePortBalance returned invalid result', 'error');
                    return;
                }
                logTest('✅ calculatePortBalance works correctly', 'success');
                
                // Test calculatePortImbalance (new method)
                const balance2 = algorithm.calculatePortImbalance(testNodes);
                if (!balance2 || !(balance2 instanceof Map)) {
                    logTest('❌ calculatePortImbalance returned invalid result', 'error');
                    return;
                }
                logTest('✅ calculatePortImbalance works correctly', 'success');
                
                // Check balance result
                const squareRedBalance = balance1.get('square-red');
                if (!squareRedBalance || typeof squareRedBalance.delta !== 'number') {
                    logTest('❌ Balance result format incorrect', 'error');
                    return;
                }
                logTest(`✅ Balance calculated: square-red delta = ${squareRedBalance.delta}`, 'success');
                
                logTest('=== Port Balance Test PASSED ===', 'success');
                
            } catch (error) {
                logTest(`❌ Port balance test failed: ${error.message}`, 'error');
            }
        }
        
        // Test compensation
        function testCompensation() {
            logTest('=== Testing Compensation ===', 'info');
            
            if (!algorithm) {
                logTest('❌ Algorithm not loaded. Run loading test first.', 'error');
                return;
            }
            
            try {
                // Create imbalanced test nodes
                const testNodes = [
                    {
                        id: 'imbalanced_source',
                        type: 'start',
                        level: 0,
                        depth: 0,
                        inputPorts: [],
                        outputPorts: [
                            { id: 'out1', type: 'square', color: 'red', side: 'output', nodeId: 'imbalanced_source', portTypeKey: 'square-red' },
                            { id: 'out2', type: 'square', color: 'red', side: 'output', nodeId: 'imbalanced_source', portTypeKey: 'square-red' }
                        ]
                    }
                ];
                
                // Test compensation
                const compensationNodes = algorithm.compensate(testNodes);
                if (!Array.isArray(compensationNodes)) {
                    logTest('❌ Compensation returned invalid result', 'error');
                    return;
                }
                logTest(`✅ Compensation generated ${compensationNodes.length} nodes`, 'success');
                
                // Test final balance
                const allNodes = [...testNodes, ...compensationNodes];
                const finalBalance = algorithm.calculatePortBalance(allNodes);
                const squareRedFinal = finalBalance.get('square-red');
                
                if (squareRedFinal && squareRedFinal.delta === 0) {
                    logTest('✅ Compensation successfully balanced ports', 'success');
                } else {
                    logTest(`⚠️ Compensation result: delta = ${squareRedFinal?.delta}`, 'warn');
                }
                
                // Test analyzeGeneration
                const analysis = algorithm.analyzeGeneration(allNodes);
                if (!analysis || typeof analysis.totalNodes !== 'number') {
                    logTest('❌ analyzeGeneration returned invalid result', 'error');
                    return;
                }
                logTest(`✅ Analysis: ${analysis.totalNodes} total nodes`, 'success');
                
                logTest('=== Compensation Test PASSED ===', 'success');
                
            } catch (error) {
                logTest(`❌ Compensation test failed: ${error.message}`, 'error');
            }
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            logTest('Test log cleared', 'info');
        }
        
        // Auto-run loading test on page load
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                testAlgorithmLoading();
            }, 500);
        });
    </script>
</body>
</html>
