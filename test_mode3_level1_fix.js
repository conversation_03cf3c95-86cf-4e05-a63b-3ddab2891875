// Test the Mode 3 Level 1 fix
console.log('🔧 Testing Mode 3 Level 1 fix...');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// Mock gameState
const mockGameState = {
    nodes: [],
    placedNodes: [],
    temporaryNodes: [],
    connections: []
};

// Mock the fixed functions
function analyzeGlobalSystemState() {
    const allPlacedNodes = [
        ...(mockGameState.placedNodes || []),
        ...(mockGameState.nodes || [])
    ].filter((node, index, array) => 
        array.findIndex(n => n.id === node.id) === index
    );
    
    console.log(`📊 发现节点: placedNodes=${(mockGameState.placedNodes || []).length}, nodes=${(mockGameState.nodes || []).length}, 合并后=${allPlacedNodes.length}`);
    
    return {
        placedNodes: allPlacedNodes,
        startNodes: allPlacedNodes.filter(n => n.type === 'start'),
        endNodes: allPlacedNodes.filter(n => n.type === 'end'),
        intermediateNodes: allPlacedNodes.filter(n => n.type === 'normal'),
        globalPortBalance: new Map()
    };
}

function calculateGlobalConstraintRequirements(globalAnalysis, wave) {
    const requirements = {
        portBalanceDeficits: new Map(),
        requiredLayerNodes: new Map(),
        minimumNewNodes: Math.max(1, wave)
    };

    // 计算端口平衡
    globalAnalysis.placedNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!globalAnalysis.globalPortBalance.has(typeKey)) {
                globalAnalysis.globalPortBalance.set(typeKey, { input: 0, output: 0 });
            }
            const balance = globalAnalysis.globalPortBalance.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                balance.input++;
            } else {
                balance.output++;
            }
        });
    });

    // 计算缺口
    globalAnalysis.globalPortBalance.forEach((balance, typeKey) => {
        const deficit = balance.output - balance.input;
        if (deficit !== 0) {
            requirements.portBalanceDeficits.set(typeKey, deficit);
        }
    });

    return requirements;
}

function generateConstraintSatisfyingTemporaryNodes(globalConstraints) {
    console.log('🔧 生成满足全局约束的临时节点 (修复版本)...');
    
    const temporaryNodes = [];
    let nodeIndex = 0;

    const currentAnalysis = analyzeGlobalSystemState();
    console.log(`🎯 当前系统: ${currentAnalysis.startNodes.length}起点, ${currentAnalysis.endNodes.length}终点`);
    
    // 如果没有端口平衡缺口，创建桥接节点
    if (globalConstraints.portBalanceDeficits.size === 0 || 
        Array.from(globalConstraints.portBalanceDeficits.values()).every(deficit => deficit === 0)) {
        
        console.log('📝 基础场景检测: 创建起点到终点的桥接节点');
        
        const requiredTypes = new Set();
        currentAnalysis.startNodes.forEach(node => {
            if (node.outputPorts) {
                node.outputPorts.forEach(port => {
                    requiredTypes.add(`${port.type}-${port.color}`);
                });
            }
        });
        
        console.log(`🔗 需要桥接的端口类型: ${Array.from(requiredTypes).join(', ')}`);
        
        for (const portTypeKey of requiredTypes) {
            const [type, color] = portTypeKey.split('-');
            
            const bridgeNode = new TestNode('normal');
            bridgeNode.id = `bridge_${Date.now()}_${nodeIndex}`;
            bridgeNode.label = `Bridge-${nodeIndex + 1}`;
            
            bridgeNode.addInputPort(type, color);
            bridgeNode.addOutputPort(type, color);
            
            bridgeNode.moveTo(200 + nodeIndex * 100, 200);
            
            temporaryNodes.push(bridgeNode);
            console.log(`✅ 创建桥接节点: ${bridgeNode.label} (${type}-${color})`);
            nodeIndex++;
        }
    }

    // 确保至少有一个临时节点
    if (temporaryNodes.length === 0) {
        const fallbackNode = new TestNode('normal');
        fallbackNode.id = `temp_fallback_${nodeIndex}`;
        fallbackNode.label = 'Bridge';
        fallbackNode.addInputPort('square', '#ff5252');
        fallbackNode.addOutputPort('square', '#ff5252');
        temporaryNodes.push(fallbackNode);
        console.log('⚠️ 使用回退节点');
    }

    console.log(`🎯 生成完成: ${temporaryNodes.length} 个临时节点`);
    return temporaryNodes;
}

function verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes) {
    const allNodes = [...globalAnalysis.placedNodes, ...temporaryNodes];
    const typeCounts = new Map();

    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!typeCounts.has(typeKey)) {
                typeCounts.set(typeKey, { input: 0, output: 0 });
            }
            const count = typeCounts.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                count.input++;
            } else {
                count.output++;
            }
        });
    });

    const imbalances = [];
    for (const [typeKey, count] of typeCounts.entries()) {
        if (count.input !== count.output) {
            imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
        }
    }

    return {
        isValid: imbalances.length === 0,
        imbalances,
        allNodes: allNodes.length
    };
}

// Test the complete flow
function testMode3Level1Fix() {
    console.log('\n🧪 开始Mode 3 Level 1修复测试...\n');

    // 1. 模拟generateInitialInfiniteBlueprint()的效果
    console.log('步骤1: 创建初始蓝图');
    mockGameState.placedNodes = [];
    mockGameState.nodes = [];
    mockGameState.temporaryNodes = [];

    const startNode = new TestNode('start');
    startNode.id = 'infinite_start';
    startNode.addOutputPort('square', '#ff5252');
    startNode.moveTo(100, 200);

    const endNode = new TestNode('end');
    endNode.id = 'infinite_end';
    endNode.addInputPort('square', '#ff5252');
    endNode.moveTo(500, 200);

    mockGameState.placedNodes.push(startNode, endNode);
    
    console.log(`✅ 初始蓝图: ${mockGameState.placedNodes.length} 个节点`);

    // 2. 全局约束满足算法
    console.log('\n步骤2: 全局约束满足算法');
    const globalAnalysis = analyzeGlobalSystemState();
    const globalConstraints = calculateGlobalConstraintRequirements(globalAnalysis, 1);
    const temporaryNodes = generateConstraintSatisfyingTemporaryNodes(globalConstraints);
    const verification = verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes);

    // 3. 结果验证
    console.log('\n📊 测试结果:');
    console.log(`系统状态: ${verification.allNodes} 总节点`);
    console.log(`可解性: ${verification.isValid ? '✅ 可解' : '❌ 不可解'}`);
    
    if (!verification.isValid) {
        console.log(`不平衡项: ${verification.imbalances.join(', ')}`);
    }

    // 4. 详细端口分析
    console.log('\n🔍 详细端口分析:');
    const allNodes = [...globalAnalysis.placedNodes, ...temporaryNodes];
    allNodes.forEach(node => {
        const inputPorts = node.inputPorts.map(p => `${p.type}-${p.color}`).join(', ');
        const outputPorts = node.outputPorts.map(p => `${p.type}-${p.color}`).join(', ');
        console.log(`${node.type} ${node.label}: 输入[${inputPorts}] 输出[${outputPorts}]`);
    });

    return verification.isValid;
}

// 运行测试
const success = testMode3Level1Fix();

console.log('\n📝 测试总结:');
if (success) {
    console.log('🎉 Mode 3 Level 1 修复成功！');
    console.log('✅ 端口类型完全匹配');
    console.log('✅ 系统全局可解');
    console.log('✅ 桥接节点正确生成');
} else {
    console.log('❌ 修复仍有问题，需要进一步调试');
}

console.log('\n✅ 测试完成');