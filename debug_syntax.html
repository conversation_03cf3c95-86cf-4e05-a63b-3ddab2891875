<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Syntax Issues</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Debug Syntax Issues</h1>
        
        <button onclick="testBasicSyntax()">Test Basic Syntax</button>
        <button onclick="testGameJSLoading()">Test game.js Loading</button>
        <button onclick="clearOutput()">Clear Output</button>
        
        <div class="test-output" id="testOutput">
            <div class="info">Ready to debug syntax issues...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type;
            
            const div = document.createElement('div');
            div.className = colorClass;
            div.textContent = `[${timestamp}] ${message}`;
            output.appendChild(div);
            
            // Auto scroll to bottom
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
        }
        
        function testBasicSyntax() {
            clearOutput();
            log('Testing basic JavaScript syntax...', 'info');
            
            try {
                // Test basic function
                function testFunction() {
                    return { test: 'success' };
                }
                
                const result = testFunction();
                log('SUCCESS: Basic function test passed', 'success');
                
                // Test class
                class TestClass {
                    constructor() {
                        this.id = 'test_' + Date.now();
                    }
                    
                    testMethod() {
                        return { id: this.id, status: 'working' };
                    }
                }
                
                const testInstance = new TestClass();
                const methodResult = testInstance.testMethod();
                log('SUCCESS: Class test passed', 'success');
                
                // Test arrow functions
                const arrowTest = () => ({ arrow: 'working' });
                const arrowResult = arrowTest();
                log('SUCCESS: Arrow function test passed', 'success');
                
                log('All basic syntax tests passed!', 'success');
                
            } catch (error) {
                log('ERROR in basic syntax test: ' + error.message, 'error');
                console.error(error);
            }
        }
        
        function testGameJSLoading() {
            clearOutput();
            log('Testing game.js loading...', 'info');
            
            // Create script element to load game.js
            const script = document.createElement('script');
            script.src = 'game.js';
            script.onload = function() {
                log('SUCCESS: game.js loaded successfully', 'success');
                
                // Test if key functions exist
                const functionsToTest = [
                    'generateDeterministicSolvableScenario',
                    'calculateLayeredScenarioSpec',
                    'generateLayeredPortPlan',
                    'validateLayeredScenarioCompletely'
                ];
                
                functionsToTest.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        log('SUCCESS: Function ' + funcName + ' found', 'success');
                    } else {
                        log('WARNING: Function ' + funcName + ' not found', 'warning');
                    }
                });
                
                // Test basic algorithm call
                try {
                    const testDifficulty = {
                        level: 1,
                        availableTypes: ['square', 'circle'],
                        availableColors: ['#ff5252', '#2196F3']
                    };
                    
                    if (typeof generateDeterministicSolvableScenario === 'function') {
                        log('Attempting to call generateDeterministicSolvableScenario...', 'info');
                        const scenario = generateDeterministicSolvableScenario(testDifficulty);
                        
                        if (scenario) {
                            log('SUCCESS: Algorithm call successful', 'success');
                            log('Scenario has startNode: ' + (scenario.startNode ? 'YES' : 'NO'), 'info');
                            log('Scenario has endNode: ' + (scenario.endNode ? 'YES' : 'NO'), 'info');
                            log('Scenario has nodes: ' + (scenario.nodes ? scenario.nodes.length : 0), 'info');
                        } else {
                            log('WARNING: Algorithm returned null/undefined', 'warning');
                        }
                    } else {
                        log('ERROR: generateDeterministicSolvableScenario not available', 'error');
                    }
                    
                } catch (error) {
                    log('ERROR in algorithm test: ' + error.message, 'error');
                    log('Error stack: ' + error.stack, 'error');
                }
            };
            
            script.onerror = function() {
                log('ERROR: Failed to load game.js', 'error');
            };
            
            // Remove any existing game.js script first
            const existingScript = document.querySelector('script[src="game.js"]');
            if (existingScript) {
                existingScript.remove();
            }
            
            document.head.appendChild(script);
        }
        
        // Test on page load
        window.addEventListener('load', () => {
            log('Page loaded successfully', 'info');
        });
        
        // Catch any global errors
        window.addEventListener('error', (event) => {
            log('GLOBAL ERROR: ' + event.message + ' at line ' + event.lineno, 'error');
        });
    </script>
</body>
</html>
