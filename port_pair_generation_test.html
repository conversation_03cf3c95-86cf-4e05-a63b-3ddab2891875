<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Port Pair Generation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .scenario {
            font-family: monospace;
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            border-left: 4px solid #007bff;
        }
        .port-analysis {
            font-family: monospace;
            font-size: 12px;
            background: #e9ecef;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .flow-diagram {
            background: #f1f3f4;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 Port Pair Generation Test</h1>
        <p>Testing the new DAG-aware port pair generation algorithm that ensures every output port has a corresponding input port at a higher level.</p>

        <div class="test-section">
            <h2>Test Controls</h2>
            <button onclick="runAllTests()">🧪 Run All Tests</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="test-section">
            <h2>Test Case 1: Your Critical Example</h2>
            <p>The exact problematic scenario you identified that requires same-level connections.</p>
            <div class="scenario">
Original Problematic Distribution:
nodeA (Level 1): 1 input, 1 output
nodeB (Level 2): 1 input, 2 outputs  
nodeC (Level 3): 1 input, 0 outputs

Port Debt Analysis:
Level 1: net=0 (1 out - 1 in), cumulative debt=0
Level 2: net=1 (2 out - 1 in), cumulative debt=1 ❌
Level 3: net=-1 (0 out - 1 in), cumulative debt=0

Expected Fix: Generate 1 input port at level 3+ to match level 2's excess output
            </div>
            <button onclick="testCriticalExample()">Run Test 1</button>
            <div id="test1-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 2: Multiple Level Debt</h2>
            <p>Complex scenario with debt accumulation across multiple levels.</p>
            <div class="scenario">
Problematic Distribution:
nodeA (Level 0): 0 input, 3 outputs
nodeB (Level 1): 1 input, 2 outputs
nodeC (Level 2): 1 input, 1 output
nodeD (Level 4): 2 inputs, 0 outputs

Port Debt Analysis:
Level 0: net=3, cumulative debt=3
Level 1: net=1, cumulative debt=4
Level 2: net=0, cumulative debt=4
Level 4: net=-2, cumulative debt=2 ❌

Expected Fix: Generate 2 input ports at level 3+ to clear remaining debt
            </div>
            <button onclick="testMultipleLevelDebt()">Run Test 2</button>
            <div id="test2-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 3: Already Valid Distribution</h2>
            <p>A scenario that already satisfies DAG constraints.</p>
            <div class="scenario">
Valid Distribution:
nodeA (Level 0): 0 input, 2 outputs
nodeB (Level 1): 1 input, 1 output
nodeC (Level 2): 2 inputs, 0 outputs

Port Debt Analysis:
Level 0: net=2, cumulative debt=2
Level 1: net=0, cumulative debt=2
Level 2: net=-2, cumulative debt=0 ✅

Expected: No additional ports needed
            </div>
            <button onclick="testValidDistribution()">Run Test 3</button>
            <div id="test3-result"></div>
        </div>

        <div class="test-section">
            <h2>Test Case 4: Empty to DAG Generation</h2>
            <p>Generate a complete DAG from scratch using port pairs.</p>
            <div class="scenario">
Starting State: No nodes

Expected: Generate a minimal DAG with source → sink flow
            </div>
            <button onclick="testEmptyToDAG()">Run Test 4</button>
            <div id="test4-result"></div>
        </div>

        <div class="test-section">
            <h2>Overall Test Results</h2>
            <div id="overall-results"></div>
        </div>
    </div>

    <script src="compensation_algorithm.js"></script>
    <script>
        // Initialize the compensation algorithm
        const portTypes = [
            { shape: 'square', color: 'red' }
        ];

        const compensationAlgorithm = new HierarchicalCompensationAlgorithm(portTypes, 5);

        function logResult(testId, message, type = 'info') {
            const resultDiv = document.getElementById(testId + '-result');
            const className = type === 'success' ? 'success' : type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'info';
            resultDiv.innerHTML += `<div class="result ${className}">${message}</div>`;
        }

        function clearResults() {
            for (let i = 1; i <= 4; i++) {
                document.getElementById(`test${i}-result`).innerHTML = '';
            }
            document.getElementById('overall-results').innerHTML = '';
        }

        function createNode(id, level, inputPorts, outputPorts) {
            const node = {
                id: id,
                type: level === 0 ? 'start' : level === 5 ? 'end' : 'intermediate',
                level: level,
                depth: level * 100,
                inputPorts: [],
                outputPorts: [],
                x: 50, y: 50,
                area: 'placed'
            };

            // Add input ports
            for (let i = 0; i < inputPorts; i++) {
                node.inputPorts.push({
                    id: `${id}_in_${i}`,
                    type: 'square',
                    color: 'red',
                    side: 'input',
                    nodeId: id,
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                });
            }

            // Add output ports
            for (let i = 0; i < outputPorts; i++) {
                node.outputPorts.push({
                    id: `${id}_out_${i}`,
                    type: 'square',
                    color: 'red',
                    side: 'output',
                    nodeId: id,
                    portTypeKey: 'square-red',
                    x: 0, y: 0
                });
            }

            return node;
        }

        function analyzePortDistribution(nodes, testId) {
            const distribution = compensationAlgorithm.buildPortLevelDistribution('square-red', nodes);
            let analysisStr = '<div class="port-analysis">Port Distribution Analysis:<br>';
            
            let cumulativeDebt = 0;
            const levels = Array.from(distribution.keys()).sort((a, b) => a - b);
            
            for (const level of levels) {
                const data = distribution.get(level);
                if (data.inputs > 0 || data.outputs > 0) {
                    const net = data.outputs - data.inputs;
                    cumulativeDebt += net;
                    const status = cumulativeDebt <= 0 ? '✅' : '❌';
                    analysisStr += `Level ${level}: ${data.inputs} in, ${data.outputs} out, net=${net}, debt=${cumulativeDebt} ${status}<br>`;
                }
            }
            
            analysisStr += '</div>';
            logResult(testId, analysisStr, 'info');
            
            return distribution;
        }

        // Test Case 1: Your Critical Example
        function testCriticalExample() {
            logResult('test1', '🧪 Testing critical example with port pair generation...', 'info');
            
            const originalNodes = [
                createNode('nodeA', 1, 1, 1), // Level 1: 1 input, 1 output
                createNode('nodeB', 2, 1, 2), // Level 2: 1 input, 2 outputs
                createNode('nodeC', 3, 1, 0)  // Level 3: 1 input, 0 outputs
            ];

            logResult('test1', 'Original problematic distribution:', 'info');
            const originalDistribution = analyzePortDistribution(originalNodes, 'test1');

            // Test the new compensation algorithm
            const compensationNodes = compensationAlgorithm.compensate(originalNodes);
            const allNodes = [...originalNodes, ...compensationNodes];

            logResult('test1', `Generated ${compensationNodes.length} compensation nodes`, 'info');
            
            if (compensationNodes.length > 0) {
                logResult('test1', 'Fixed distribution:', 'info');
                const fixedDistribution = analyzePortDistribution(allNodes, 'test1');
                
                // Validate the fix
                const isValid = compensationAlgorithm.validateDAGCompliantDistribution(
                    compensationAlgorithm.analyzePortDistribution(allNodes)
                );
                
                if (isValid) {
                    logResult('test1', '✅ Successfully fixed critical example with port pairs', 'success');
                    return true;
                } else {
                    logResult('test1', '❌ Fix did not create valid DAG distribution', 'error');
                    return false;
                }
            } else {
                logResult('test1', '❌ No compensation nodes generated', 'error');
                return false;
            }
        }

        // Test Case 2: Multiple Level Debt
        function testMultipleLevelDebt() {
            logResult('test2', '🧪 Testing multiple level debt scenario...', 'info');
            
            const originalNodes = [
                createNode('nodeA', 0, 0, 3), // Level 0: 0 input, 3 outputs
                createNode('nodeB', 1, 1, 2), // Level 1: 1 input, 2 outputs
                createNode('nodeC', 2, 1, 1), // Level 2: 1 input, 1 output
                createNode('nodeD', 4, 2, 0)  // Level 4: 2 inputs, 0 outputs
            ];

            logResult('test2', 'Original distribution with multiple level debt:', 'info');
            analyzePortDistribution(originalNodes, 'test2');

            const compensationNodes = compensationAlgorithm.compensate(originalNodes);
            const allNodes = [...originalNodes, ...compensationNodes];

            logResult('test2', `Generated ${compensationNodes.length} compensation nodes`, 'info');
            
            if (compensationNodes.length > 0) {
                logResult('test2', 'Fixed distribution:', 'info');
                analyzePortDistribution(allNodes, 'test2');
                
                const isValid = compensationAlgorithm.validateDAGCompliantDistribution(
                    compensationAlgorithm.analyzePortDistribution(allNodes)
                );
                
                if (isValid) {
                    logResult('test2', '✅ Successfully resolved multiple level debt', 'success');
                    return true;
                } else {
                    logResult('test2', '❌ Multiple level debt not properly resolved', 'error');
                    return false;
                }
            } else {
                logResult('test2', '❌ No compensation nodes generated for debt scenario', 'error');
                return false;
            }
        }

        // Test Case 3: Already Valid Distribution
        function testValidDistribution() {
            logResult('test3', '🧪 Testing already valid distribution...', 'info');
            
            const validNodes = [
                createNode('nodeA', 0, 0, 2), // Level 0: 0 input, 2 outputs
                createNode('nodeB', 1, 1, 1), // Level 1: 1 input, 1 output
                createNode('nodeC', 2, 2, 0)  // Level 2: 2 inputs, 0 outputs
            ];

            logResult('test3', 'Already valid distribution:', 'info');
            analyzePortDistribution(validNodes, 'test3');

            const compensationNodes = compensationAlgorithm.compensate(validNodes);

            if (compensationNodes.length === 0) {
                logResult('test3', '✅ Correctly identified no compensation needed', 'success');
                return true;
            } else {
                logResult('test3', `⚠️ Generated ${compensationNodes.length} unnecessary compensation nodes`, 'warning');
                return false;
            }
        }

        // Test Case 4: Empty to DAG Generation
        function testEmptyToDAG() {
            logResult('test4', '🧪 Testing DAG generation from empty state...', 'info');
            
            const emptyNodes = [];
            const compensationNodes = compensationAlgorithm.compensate(emptyNodes);

            if (compensationNodes.length > 0) {
                logResult('test4', `Generated ${compensationNodes.length} nodes from empty state`, 'info');
                analyzePortDistribution(compensationNodes, 'test4');
                
                const isValid = compensationAlgorithm.validateDAGCompliantDistribution(
                    compensationAlgorithm.analyzePortDistribution(compensationNodes)
                );
                
                if (isValid) {
                    logResult('test4', '✅ Successfully generated valid DAG from empty state', 'success');
                    return true;
                } else {
                    logResult('test4', '❌ Generated DAG is not valid', 'error');
                    return false;
                }
            } else {
                logResult('test4', '⚠️ No nodes generated from empty state', 'warning');
                return false;
            }
        }

        function runAllTests() {
            clearResults();
            
            const results = [];
            results.push(testCriticalExample());
            results.push(testMultipleLevelDebt());
            results.push(testValidDistribution());
            results.push(testEmptyToDAG());
            
            const passedTests = results.filter(r => r).length;
            const totalTests = results.length;
            
            const overallDiv = document.getElementById('overall-results');
            if (passedTests === totalTests) {
                overallDiv.innerHTML = `<div class="result success">🎉 All ${totalTests} tests passed! Port pair generation is working correctly.</div>`;
            } else {
                overallDiv.innerHTML = `<div class="result error">❌ ${passedTests}/${totalTests} tests passed. Some issues need to be addressed.</div>`;
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
