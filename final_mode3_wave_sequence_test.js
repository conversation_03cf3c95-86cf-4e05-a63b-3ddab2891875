// Final test of complete Mode 3 wave sequence with fixes
console.log('🌊 最终Mode 3波次序列测试...');

class TestNode {
    constructor(type) {
        this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
        this.inputPorts = [];
        this.outputPorts = [];
        this.x = 0;
        this.y = 0;
    }

    addInputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'input' 
        };
        this.inputPorts.push(port);
        return port;
    }

    addOutputPort(type, color) {
        const port = { 
            id: `port_${Math.random().toString(36).substr(2, 9)}`, 
            type, color, side: 'output' 
        };
        this.outputPorts.push(port);
        return port;
    }

    getAllPorts() {
        return [...this.inputPorts, ...this.outputPorts];
    }

    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

const mockGameState = {
    nodes: [],
    temporaryNodes: [],
    portTypes: ['square', 'diamond', 'triangle', 'circle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
    infiniteMode: {
        currentWave: 1,
        adaptiveDifficulty: 1
    }
};

global.Node = TestNode;
global.gameState = mockGameState;

function getRandomPortType() {
    return mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
}

function getRandomPortColor() {
    return mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
}

// 修复后的结构修改函数
function performStructuralModificationsFixed(wave) {
    const modifications = {
        addedNodes: [],
        addedStartNodes: [],
        addedEndNodes: [],
        modifiedNodes: []
    };

    const placedNodes = mockGameState.nodes.filter(node => 
        node.type !== 'start' && node.type !== 'end' && 
        !mockGameState.temporaryNodes.includes(node)
    );

    // 1. 添加平衡的新节点
    if (Math.random() < 0.4) {
        const newNode = new TestNode('normal');
        newNode.id = `wave_${wave}_added_${Date.now()}`;
        newNode.label = `W${wave}-Add`;
        
        const portType = getRandomPortType();
        const portColor = getRandomPortColor();
        newNode.addInputPort(portType, portColor);
        newNode.addOutputPort(portType, portColor);
        
        mockGameState.nodes.push(newNode);
        modifications.addedNodes.push(newNode);
    }

    // 2. 修改现有节点（平衡端口对）
    if (placedNodes.length > 0 && Math.random() < 0.4) {
        const nodeToModify = placedNodes[0];
        const originalState = {
            node: nodeToModify,
            inputPorts: [...nodeToModify.inputPorts],
            outputPorts: [...nodeToModify.outputPorts]
        };

        if (nodeToModify.inputPorts.length + nodeToModify.outputPorts.length < 6) {
            const portType = getRandomPortType();
            const portColor = getRandomPortColor();
            
            nodeToModify.addInputPort(portType, portColor);
            nodeToModify.addOutputPort(portType, portColor);
        }

        modifications.modifiedNodes.push(originalState);
    }

    // 3. 添加平衡的起点终点对
    if (Math.random() < 0.3) {
        const sharedPortType = getRandomPortType();
        const sharedPortColor = getRandomPortColor();
        
        const newStartNode = new TestNode('start');
        newStartNode.id = `start_wave_${wave}_${Date.now()}`;
        newStartNode.label = `Start-${wave}`;
        newStartNode.addOutputPort(sharedPortType, sharedPortColor);
        
        const newEndNode = new TestNode('end');
        newEndNode.id = `end_wave_${wave}_${Date.now()}`;
        newEndNode.label = `End-${wave}`;
        newEndNode.addInputPort(sharedPortType, sharedPortColor);
        
        mockGameState.nodes.push(newStartNode, newEndNode);
        modifications.addedStartNodes.push(newStartNode);
        modifications.addedEndNodes.push(newEndNode);
    }

    return modifications;
}

// 全局系统状态分析
function analyzeGlobalSystemState() {
    const allPlacedNodes = [...mockGameState.nodes];
    
    return {
        placedNodes: allPlacedNodes,
        startNodes: allPlacedNodes.filter(n => n.type === 'start'),
        endNodes: allPlacedNodes.filter(n => n.type === 'end'),
        intermediateNodes: allPlacedNodes.filter(n => n.type === 'normal')
    };
}

// 生成约束满足的临时节点（修复版本）
function generateConstraintSatisfyingTemporaryNodesFixed() {
    const temporaryNodes = [];
    let nodeIndex = 0;

    const currentAnalysis = analyzeGlobalSystemState();
    
    // 收集起点输出端口类型
    const requiredTypes = new Set();
    currentAnalysis.startNodes.forEach(node => {
        if (node.outputPorts) {
            node.outputPorts.forEach(port => {
                requiredTypes.add(`${port.type}-${port.color}`);
            });
        }
    });
    
    // 为每种端口类型创建匹配的桥接节点
    for (const portTypeKey of requiredTypes) {
        const [type, color] = portTypeKey.split('-');
        
        const bridgeNode = new TestNode('normal');
        bridgeNode.id = `bridge_${Date.now()}_${nodeIndex}`;
        bridgeNode.label = `Bridge-${nodeIndex + 1}`;
        
        bridgeNode.addInputPort(type, color);
        bridgeNode.addOutputPort(type, color);
        
        temporaryNodes.push(bridgeNode);
        nodeIndex++;
    }

    if (temporaryNodes.length === 0) {
        const fallbackNode = new TestNode('normal');
        fallbackNode.id = `temp_fallback_${nodeIndex}`;
        fallbackNode.label = 'Bridge';
        fallbackNode.addInputPort('square', '#ff5252');
        fallbackNode.addOutputPort('square', '#ff5252');
        temporaryNodes.push(fallbackNode);
    }

    return temporaryNodes;
}

// 检查系统可解性
function checkSystemSolvability() {
    const allNodes = [...mockGameState.nodes, ...mockGameState.temporaryNodes];
    const typeCounts = new Map();

    allNodes.forEach(node => {
        node.getAllPorts().forEach(port => {
            const typeKey = `${port.type}-${port.color}`;
            if (!typeCounts.has(typeKey)) {
                typeCounts.set(typeKey, { input: 0, output: 0 });
            }
            const count = typeCounts.get(typeKey);
            if (port.side === 'input' || node.inputPorts.includes(port)) {
                count.input++;
            } else {
                count.output++;
            }
        });
    });

    const imbalances = [];
    for (const [typeKey, count] of typeCounts.entries()) {
        if (count.input !== count.output) {
            imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
        }
    }

    return {
        isSolvable: imbalances.length === 0,
        imbalances,
        allNodes: allNodes.length,
        typeCounts
    };
}

// 模拟完整的Mode 3波次序列
function simulateCompleteMode3Sequence() {
    console.log('🎮 模拟完整Mode 3波次序列...\n');
    
    let totalWaves = 5;
    let solvableWaves = 0;
    
    // 初始化
    mockGameState.nodes = [];
    mockGameState.temporaryNodes = [];
    
    const startNode = new TestNode('start');
    startNode.id = 'initial_start';
    startNode.addOutputPort('square', '#ff5252');
    
    const endNode = new TestNode('end');
    endNode.id = 'initial_end';
    endNode.addInputPort('square', '#ff5252');
    
    mockGameState.nodes.push(startNode, endNode);
    
    console.log('=== 初始化完成 ===');
    console.log('基础节点: 1起点, 1终点\n');
    
    let wave;
    for (wave = 1; wave <= totalWaves; wave++) {
        console.log(`=== 波次 ${wave} ===`);
        mockGameState.infiniteMode.currentWave = wave;
        
        // 1. 生成临时节点
        mockGameState.temporaryNodes = generateConstraintSatisfyingTemporaryNodesFixed();
        
        // 2. 检查可解性
        const solvability = checkSystemSolvability();
        
        const analysis = analyzeGlobalSystemState();
        console.log(`系统状态: ${analysis.placedNodes.length} 摆放节点 (${analysis.startNodes.length}起点, ${analysis.endNodes.length}终点), ${mockGameState.temporaryNodes.length} 临时节点`);
        console.log(`可解性: ${solvability.isSolvable ? '✅ 可解' : '❌ 不可解'}`);
        
        if (solvability.isSolvable) {
            solvableWaves++;
            console.log(`总节点: ${solvability.allNodes}`);
            
            // 显示端口平衡
            let balancedPorts = 0;
            let totalPortTypes = 0;
            for (const [typeKey, count] of solvability.typeCounts.entries()) {
                totalPortTypes++;
                if (count.input === count.output) {
                    balancedPorts++;
                }
            }
            console.log(`端口平衡: ${balancedPorts}/${totalPortTypes} 类型平衡`);
        } else {
            console.log(`❌ 不可解原因: ${solvability.imbalances.join(', ')}`);
            console.log('⚠️ 这表明修复可能还有问题');
        }
        
        // 3. 模拟波次完成，执行结构修改
        if (wave < totalWaves && solvability.isSolvable) {
            console.log(`\n🔄 波次 ${wave} 完成，执行结构修改...`);
            const modifications = performStructuralModificationsFixed(wave + 1);
            
            const modificationSummary = [];
            if (modifications.addedNodes.length > 0) modificationSummary.push(`${modifications.addedNodes.length}新节点`);
            if (modifications.addedStartNodes.length > 0) modificationSummary.push(`${modifications.addedStartNodes.length}新起点`);
            if (modifications.addedEndNodes.length > 0) modificationSummary.push(`${modifications.addedEndNodes.length}新终点`);
            if (modifications.modifiedNodes.length > 0) modificationSummary.push(`${modifications.modifiedNodes.length}节点修改`);
            
            if (modificationSummary.length > 0) {
                console.log(`结构修改: ${modificationSummary.join(', ')}`);
            } else {
                console.log('无结构修改');
            }
        }
        
        console.log(''); // 空行分隔
        
        // 如果不可解，停止模拟
        if (!solvability.isSolvable) {
            console.log(`🚨 在波次 ${wave} 发现不可解情况，停止模拟\n`);
            break;
        }
    }
    
    return {
        totalWaves: wave <= totalWaves ? wave : totalWaves,
        solvableWaves,
        successRate: (solvableWaves / (wave <= totalWaves ? wave : totalWaves)) * 100
    };
}

// 运行多次完整序列测试
function runMultipleCompleteTests() {
    console.log('🎯 运行多次完整Mode 3序列测试...\n');
    
    let numTests = 3;
    let completelySuccessfulTests = 0;
    let results = [];
    
    for (let testNum = 1; testNum <= numTests; testNum++) {
        console.log(`${'='.repeat(20)} 测试 ${testNum} ${'='.repeat(20)}`);
        
        const result = simulateCompleteMode3Sequence();
        results.push(result);
        
        console.log(`测试 ${testNum} 结果: ${result.solvableWaves}/${result.totalWaves} 波次可解 (${result.successRate.toFixed(1)}%)`);
        
        if (result.successRate === 100) {
            completelySuccessfulTests++;
        }
        
        console.log('\n');
    }
    
    // 总结
    console.log('='.repeat(60));
    console.log('📊 多次测试总结');
    console.log('='.repeat(60));
    console.log(`完成测试: ${numTests}`);
    console.log(`完全成功: ${completelySuccessfulTests} (${(completelySuccessfulTests/numTests * 100).toFixed(1)}%)`);
    
    const avgSuccessRate = results.reduce((sum, r) => sum + r.successRate, 0) / results.length;
    console.log(`平均成功率: ${avgSuccessRate.toFixed(1)}%`);
    
    if (completelySuccessfulTests === numTests) {
        console.log('\n🎉 所有测试完全成功！Mode 3 w 1-2 不可解问题已修复！');
        console.log('✅ 结构修改平衡性算法工作正常');
        console.log('✅ 临时节点生成算法稳定');
        console.log('✅ 全局约束满足算法可靠');
    } else if (avgSuccessRate >= 90) {
        console.log('\n✅ 大部分测试成功，修复基本有效');
        console.log('⚠️ 可能仍有边缘情况需要处理');
    } else {
        console.log('\n⚠️ 修复效果有限，需要进一步改进');
    }
}

// 执行测试
runMultipleCompleteTests();

console.log('\n✅ 最终Mode 3波次序列测试完成');