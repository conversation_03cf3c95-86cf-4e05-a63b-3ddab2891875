<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Execute Flow-First Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 600px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .status {
            background: #333;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
    </style>
</head>
<body>
    <h1>🚀 Flow-First Algorithm Test Execution</h1>
    
    <div class="status">
        <p><strong>Status:</strong> <span id="status">Initializing...</span></p>
        <p><strong>Progress:</strong> <span id="progress">0/0</span></p>
    </div>

    <div class="output" id="output"></div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[AUTO-TEST] Game script loaded, starting tests...');
            setTimeout(runAutoTests, 1000);
        };
        script.onerror = function() {
            console.error('[AUTO-TEST] Failed to load game script');
            document.getElementById('status').innerHTML = '<span class="fail">Failed to load game script</span>';
        };
        document.head.appendChild(script);

        // Mock console to capture output
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            const className = type === 'error' ? 'fail' : (message.includes('PASS') || message.includes('SUCCESS') ? 'pass' : (message.includes('FAIL') || message.includes('ERROR') ? 'fail' : ''));
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function runAutoTests() {
            try {
                document.getElementById('status').innerHTML = '<span class="warn">Running tests...</span>';
                
                // Wait for game initialization
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('[AUTO-TEST] ========== FLOW-FIRST ALGORITHM TESTING ==========');
                
                // Test 1: Quick Flow-First Algorithm Test
                console.log('[AUTO-TEST] === QUICK FLOW-FIRST TEST ===');
                if (window.testFlowFirstAlgorithm) {
                    const quickResult = window.testFlowFirstAlgorithm();
                    console.log('[AUTO-TEST] Quick test result:', quickResult.success ? 'PASS' : 'FAIL');
                    if (!quickResult.success) {
                        console.log('[AUTO-TEST] Quick test error:', quickResult.error);
                    }
                } else {
                    console.error('[AUTO-TEST] testFlowFirstAlgorithm function not available');
                }
                
                document.getElementById('status').innerHTML = '<span class="warn">Running comprehensive test...</span>';
                
                // Test 2: Comprehensive Mode 3 Test
                console.log('[AUTO-TEST] === COMPREHENSIVE MODE 3 TEST ===');
                if (window.runMode3Test) {
                    document.getElementById('progress').textContent = '0/10';
                    
                    const testResults = await window.runMode3Test();
                    
                    document.getElementById('progress').textContent = '10/10';
                    
                    // Analyze results
                    const passRate = testResults.summary.passedTests / testResults.summary.totalTests;
                    const success = passRate >= 0.9; // 90% target
                    
                    console.log('[AUTO-TEST] === COMPREHENSIVE TEST RESULTS ===');
                    console.log(`[AUTO-TEST] Pass Rate: ${(passRate * 100).toFixed(1)}% (Target: 90%)`);
                    console.log(`[AUTO-TEST] Passed: ${testResults.summary.passedTests}/10`);
                    console.log(`[AUTO-TEST] Failed: ${testResults.summary.failedTests}`);
                    console.log(`[AUTO-TEST] Errors: ${testResults.summary.errorTests}`);
                    console.log(`[AUTO-TEST] Timeouts: ${testResults.summary.timeoutTests}`);
                    console.log(`[AUTO-TEST] Total Duration: ${testResults.totalDuration.toFixed(2)}ms`);
                    
                    // SYSTEM.md criteria analysis
                    console.log('[AUTO-TEST] === SYSTEM.MD CRITERIA ANALYSIS ===');
                    Object.keys(testResults.systemMdCriteria).forEach(criteria => {
                        const stats = testResults.systemMdCriteria[criteria];
                        const total = stats.passed + stats.failed;
                        const rate = total > 0 ? (stats.passed / total * 100).toFixed(1) : '0.0';
                        const status = parseFloat(rate) >= 95 ? 'EXCELLENT' : (parseFloat(rate) >= 80 ? 'GOOD' : 'NEEDS_IMPROVEMENT');
                        console.log(`[AUTO-TEST] ${criteria}: ${stats.passed}/${total} (${rate}%) - ${status}`);
                    });
                    
                    // Performance analysis
                    if (testResults.performance) {
                        console.log('[AUTO-TEST] === PERFORMANCE ANALYSIS ===');
                        console.log(`[AUTO-TEST] Average Time: ${testResults.performance.averageTime.toFixed(2)}ms`);
                        console.log(`[AUTO-TEST] Max Time: ${testResults.performance.maxTime.toFixed(2)}ms`);
                        console.log(`[AUTO-TEST] Min Time: ${testResults.performance.minTime.toFixed(2)}ms`);
                        
                        if (testResults.performance.memoryLeaks && testResults.performance.memoryLeaks.length > 0) {
                            console.log(`[AUTO-TEST] Memory Leaks Detected: ${testResults.performance.memoryLeaks.length}`);
                            testResults.performance.memoryLeaks.forEach(leak => {
                                console.log(`[AUTO-TEST]   Level ${leak.level}: +${(leak.delta / 1024 / 1024).toFixed(2)}MB`);
                            });
                        } else {
                            console.log('[AUTO-TEST] No memory leaks detected');
                        }
                        
                        if (testResults.performance.bottlenecks && testResults.performance.bottlenecks.length > 0) {
                            console.log(`[AUTO-TEST] Performance Bottlenecks: ${testResults.performance.bottlenecks.length}`);
                            testResults.performance.bottlenecks.forEach(bottleneck => {
                                console.log(`[AUTO-TEST]   Level ${bottleneck.level}: ${bottleneck.duration.toFixed(2)}ms`);
                            });
                        } else {
                            console.log('[AUTO-TEST] No performance bottlenecks detected');
                        }
                    }
                    
                    // Failed levels analysis
                    const failedLevels = testResults.levels.filter(level => level.status !== 'PASS');
                    if (failedLevels.length > 0) {
                        console.log('[AUTO-TEST] === FAILED LEVELS ANALYSIS ===');
                        failedLevels.forEach(level => {
                            console.log(`[AUTO-TEST] Level ${level.level}: ${level.status} (${level.duration.toFixed(2)}ms)`);
                            if (level.errors.length > 0) {
                                console.log(`[AUTO-TEST]   Errors: ${level.errors.join('; ')}`);
                            }
                            
                            // Analyze SYSTEM.md failures for this level
                            const systemMdFailures = [];
                            Object.keys(level.systemMdResults || {}).forEach(criteria => {
                                if (!level.systemMdResults[criteria]) {
                                    systemMdFailures.push(criteria);
                                }
                            });
                            if (systemMdFailures.length > 0) {
                                console.log(`[AUTO-TEST]   SYSTEM.md failures: ${systemMdFailures.join(', ')}`);
                            }
                        });
                    } else {
                        console.log('[AUTO-TEST] === ALL LEVELS PASSED ===');
                    }
                    
                    // Algorithm performance assessment
                    console.log('[AUTO-TEST] === ALGORITHM ASSESSMENT ===');
                    if (success) {
                        console.log('[AUTO-TEST] ✅ FLOW-FIRST ALGORITHM SUCCESS - Target achieved!');
                        console.log('[AUTO-TEST] The Flow-First algorithm meets the 90% pass rate requirement');
                    } else {
                        console.log('[AUTO-TEST] ⚠️ FLOW-FIRST ALGORITHM NEEDS IMPROVEMENT');
                        console.log(`[AUTO-TEST] Current pass rate: ${(passRate * 100).toFixed(1)}% (Target: 90%)`);
                        console.log('[AUTO-TEST] Recommendations for improvement:');
                        
                        // Analyze what needs improvement
                        const portMappingStats = testResults.systemMdCriteria.portMapping;
                        const portMappingRate = portMappingStats.passed / (portMappingStats.passed + portMappingStats.failed);
                        if (portMappingRate < 0.95) {
                            console.log('[AUTO-TEST] - Improve port mapping algorithm');
                        }
                        
                        const dagTopologyStats = testResults.systemMdCriteria.dagTopology;
                        const dagTopologyRate = dagTopologyStats.passed / (dagTopologyStats.passed + dagTopologyStats.failed);
                        if (dagTopologyRate < 0.95) {
                            console.log('[AUTO-TEST] - Improve DAG topology validation');
                        }
                        
                        const flowConservationStats = testResults.systemMdCriteria.flowConservation;
                        const flowConservationRate = flowConservationStats.passed / (flowConservationStats.passed + flowConservationStats.failed);
                        if (flowConservationRate < 0.95) {
                            console.log('[AUTO-TEST] - Improve flow conservation logic');
                        }
                        
                        const portBalanceStats = testResults.systemMdCriteria.portBalance;
                        const portBalanceRate = portBalanceStats.passed / (portBalanceStats.passed + portBalanceStats.failed);
                        if (portBalanceRate < 0.95) {
                            console.log('[AUTO-TEST] - Improve port balance calculation');
                        }
                    }
                    
                    const statusText = success ? 
                        `<span class="pass">SUCCESS - ${(passRate * 100).toFixed(1)}% pass rate achieved!</span>` :
                        `<span class="warn">NEEDS IMPROVEMENT - ${(passRate * 100).toFixed(1)}% pass rate</span>`;
                    
                    document.getElementById('status').innerHTML = statusText;
                    
                    console.log(`[AUTO-TEST] === FINAL RESULT: ${success ? 'SUCCESS' : 'NEEDS_IMPROVEMENT'} ===`);
                    
                    // Store results for further analysis
                    window.flowFirstTestResults = testResults;
                    console.log('[AUTO-TEST] Results stored in window.flowFirstTestResults for analysis');
                    
                } else {
                    console.error('[AUTO-TEST] runMode3Test function not available');
                    document.getElementById('status').innerHTML = '<span class="fail">Test function not available</span>';
                }
                
            } catch (error) {
                console.error('[AUTO-TEST] Auto test execution failed:', error);
                document.getElementById('status').innerHTML = '<span class="fail">Test execution failed</span>';
            }
        }

        // Initialize
        console.log('[AUTO-TEST] Flow-First algorithm test execution framework initialized');
    </script>
</body>
</html>
