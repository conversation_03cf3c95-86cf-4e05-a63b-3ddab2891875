<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mode 3 Complete System Test</title>
    <style>
        body { 
            font-family: monospace; 
            padding: 20px; 
            background: #1a1a1a; 
            color: #00ff00; 
            font-size: 14px;
        }
        .test-section { 
            border: 1px solid #333; 
            margin: 15px 0; 
            padding: 20px; 
            background: #2a2a2a; 
            border-radius: 8px;
        }
        .success { color: #44ff44; }
        .error { color: #ff4444; }
        .warning { color: #ffaa44; }
        .info { color: #4444ff; }
        .header { color: #ffffff; font-weight: bold; font-size: 16px; }
        .step { color: #88cc88; margin-left: 20px; }
        pre { background: #1a1a1a; padding: 15px; border-radius: 5px; overflow-x: auto; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 12px 24px;
            margin: 8px;
            cursor: pointer;
            border-radius: 4px;
            font-family: monospace;
        }
        button:hover { background: #444; }
        .wave-status {
            background: #2a4a2a;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #44ff44;
        }
        .analysis {
            background: #2a2a4a;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #4444ff;
        }
    </style>
</head>
<body>
    <h1>🎮 Mode 3 Complete System Test</h1>
    
    <div class="test-section header">
        <h2>🌊 无限构筑模式完整系统测试</h2>
        <p>测试SYSTEM.md中定义的Mode 3完整游戏循环：</p>
        <ul>
            <li>1. 生成node pool(包含起点终点)，保证临时区和摆放区的node pool整体可解</li>
            <li>2. 玩家进行连接</li>
            <li>3. 玩家点击播放流动/验证</li>
            <li>4. 验证通过后过关，随机修改已经连接好的node pool</li>
            <li>5. 回到1，以修改后的node pool为基础生成新的总体的node pool</li>
        </ul>
    </div>

    <div id="test-controls">
        <button onclick="runCompleteMode3Test()">运行完整Mode 3测试</button>
        <button onclick="testSingleWave()">测试单个波次</button>
        <button onclick="testWaveProgression()">测试多波次推进</button>
        <button onclick="testGlobalConstraintSatisfaction()">测试全局约束满足</button>
        <button onclick="clearResults()">清空结果</button>
    </div>

    <div id="results"></div>

    <script>
        // 模拟完整的Node和游戏状态
        class MockNode {
            constructor(type) {
                this.id = `node_${Math.random().toString(36).substr(2, 9)}`;
                this.type = type;
                this.label = type === 'start' ? 'Start' : type === 'end' ? 'End' : 'Node';
                this.inputPorts = [];
                this.outputPorts = [];
                this.x = 0;
                this.y = 0;
                this.depth = 0;
            }

            addInputPort(type, color) {
                const port = { 
                    id: `port_${Math.random().toString(36).substr(2, 9)}`, 
                    type, color, side: 'input' 
                };
                this.inputPorts.push(port);
                return port;
            }

            addOutputPort(type, color) {
                const port = { 
                    id: `port_${Math.random().toString(36).substr(2, 9)}`, 
                    type, color, side: 'output' 
                };
                this.outputPorts.push(port);
                return port;
            }

            moveTo(x, y) {
                this.x = x;
                this.y = y;
            }

            getAllPorts() {
                return [...this.inputPorts, ...this.outputPorts];
            }
        }

        // 完整的游戏状态模拟
        const mockGameState = {
            nodes: [],
            temporaryNodes: [],
            connections: [],
            portTypes: ['square', 'diamond', 'triangle', 'circle'],
            portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
            infiniteMode: {
                isActive: true,
                currentWave: 1,
                adaptiveDifficulty: 1,
                maxWaves: 5,
                completedWaves: 0,
                continuousSolutions: 0,
                lastWaveChangeTime: Date.now()
            }
        };

        let testResults = [];

        // 全局设置
        global.Node = MockNode;
        global.gameState = mockGameState;

        // 核心算法实现
        function analyzeGlobalSystemState() {
            const allNodes = [...mockGameState.nodes, ...mockGameState.temporaryNodes];
            return {
                allNodes,
                startNodes: allNodes.filter(n => n.type === 'start'),
                endNodes: allNodes.filter(n => n.type === 'end'),
                intermediateNodes: allNodes.filter(n => n.type === 'normal'),
                temporaryNodes: mockGameState.temporaryNodes,
                placedNodes: mockGameState.nodes.filter(n => !mockGameState.temporaryNodes.includes(n)),
                totalPortCount: allNodes.reduce((sum, node) => sum + node.getAllPorts().length, 0)
            };
        }

        function calculateGlobalConstraintRequirements(globalAnalysis, wave) {
            const requirements = {
                targetPortBalance: new Map(),
                minTemporaryNodes: Math.max(1, wave),
                maxTemporaryNodes: Math.min(5, wave + 2),
                preferredTypes: mockGameState.portTypes.slice(0, Math.min(3, 1 + wave)),
                preferredColors: mockGameState.portColors.slice(0, Math.min(3, 1 + wave))
            };

            // 分析现有端口，确定需要补充的类型
            globalAnalysis.allNodes.forEach(node => {
                node.getAllPorts().forEach(port => {
                    const typeKey = `${port.type}-${port.color}`;
                    if (!requirements.targetPortBalance.has(typeKey)) {
                        requirements.targetPortBalance.set(typeKey, { needed: 0, provided: 0 });
                    }
                    const balance = requirements.targetPortBalance.get(typeKey);
                    if (port.side === 'input' || node.inputPorts.includes(port)) {
                        balance.needed++;
                    } else {
                        balance.provided++;
                    }
                });
            });

            return requirements;
        }

        function generateConstraintSatisfyingTemporaryNodes(constraints) {
            const temporaryNodes = [];
            const nodeCount = Math.floor(Math.random() * (constraints.maxTemporaryNodes - constraints.minTemporaryNodes + 1)) + constraints.minTemporaryNodes;

            for (let i = 0; i < nodeCount; i++) {
                const node = new MockNode('normal');
                node.id = `temp_${Date.now()}_${i}`;
                node.label = `Temp-${i + 1}`;

                // 简单的端口分配策略 - 确保平衡
                const portType = constraints.preferredTypes[i % constraints.preferredTypes.length];
                const portColor = constraints.preferredColors[i % constraints.preferredColors.length];

                node.addInputPort(portType, portColor);
                node.addOutputPort(portType, portColor);

                // 随机位置
                node.moveTo(100 + Math.random() * 400, 100 + Math.random() * 300);

                temporaryNodes.push(node);
            }

            return temporaryNodes;
        }

        function verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes) {
            const allNodes = [...globalAnalysis.allNodes, ...temporaryNodes];
            const typeCounts = new Map();

            // 统计所有端口类型
            allNodes.forEach(node => {
                node.getAllPorts().forEach(port => {
                    const typeKey = `${port.type}-${port.color}`;
                    if (!typeCounts.has(typeKey)) {
                        typeCounts.set(typeKey, { input: 0, output: 0 });
                    }
                    const count = typeCounts.get(typeKey);
                    if (port.side === 'input' || node.inputPorts.includes(port)) {
                        count.input++;
                    } else {
                        count.output++;
                    }
                });
            });

            // 检查平衡性
            const imbalances = [];
            for (const [typeKey, count] of typeCounts.entries()) {
                if (count.input !== count.output) {
                    imbalances.push(`${typeKey}: ${count.output}out/${count.input}in`);
                }
            }

            return {
                isValid: imbalances.length === 0,
                imbalances,
                allNodes: allNodes.length,
                typeCount: typeCounts.size
            };
        }

        function generateGlobalConstraintSatisfiedSystem(wave) {
            console.log(`🌐 生成波次 ${wave} 的全局约束满足系统...`);

            try {
                const globalAnalysis = analyzeGlobalSystemState();
                const globalConstraints = calculateGlobalConstraintRequirements(globalAnalysis, wave);
                const temporaryNodes = generateConstraintSatisfyingTemporaryNodes(globalConstraints);
                const verification = verifyGlobalSystemSolvability(globalAnalysis, temporaryNodes);

                return {
                    isValid: verification.isValid,
                    temporaryNodes,
                    totalNodes: verification.allNodes,
                    verification,
                    wave
                };
            } catch (error) {
                console.error('生成全局系统时出错:', error.message);
                return {
                    isValid: false,
                    temporaryNodes: [],
                    error: error.message
                };
            }
        }

        function performStructuralModifications() {
            const modifications = {
                addedNodes: [],
                removedNodes: [],
                modifiedNodes: [],
                addedPorts: [],
                timestamp: Date.now()
            };

            // 随机添加节点
            if (Math.random() < 0.4) {
                const newNode = new MockNode('normal');
                newNode.id = `struct_mod_${Date.now()}`;
                newNode.label = 'Modified';
                
                // 确保端口平衡
                const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
                const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
                newNode.addInputPort(portType, portColor);
                newNode.addOutputPort(portType, portColor);
                
                mockGameState.nodes.push(newNode);
                modifications.addedNodes.push(newNode);
            }

            // 修改现有节点端口
            const placedNodes = mockGameState.nodes.filter(n => n.type === 'normal');
            if (placedNodes.length > 0 && Math.random() < 0.3) {
                const nodeToModify = placedNodes[Math.floor(Math.random() * placedNodes.length)];
                const originalState = {
                    node: nodeToModify,
                    inputPorts: [...nodeToModify.inputPorts],
                    outputPorts: [...nodeToModify.outputPorts]
                };

                if (nodeToModify.getAllPorts().length < 6) {
                    const portType = mockGameState.portTypes[Math.floor(Math.random() * mockGameState.portTypes.length)];
                    const portColor = mockGameState.portColors[Math.floor(Math.random() * mockGameState.portColors.length)];
                    const newPort = nodeToModify.addOutputPort(portType, portColor);
                    modifications.addedPorts.push({ node: nodeToModify, port: newPort });
                }

                modifications.modifiedNodes.push(originalState);
            }

            return modifications;
        }

        function applyConstraintPropagation(modifications) {
            try {
                const globalAnalysis = analyzeGlobalSystemState();
                const verification = verifyGlobalSystemSolvability(globalAnalysis, []);

                return {
                    success: verification.isValid,
                    verification,
                    modifications: modifications.addedNodes.length + modifications.modifiedNodes.length
                };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 初始化函数
        function initializeMode3Environment() {
            mockGameState.nodes = [];
            mockGameState.temporaryNodes = [];
            mockGameState.connections = [];

            // 创建基础起点和终点
            const startNode = new MockNode('start');
            startNode.id = 'system_start';
            startNode.addOutputPort('square', '#ff5252');
            startNode.addOutputPort('diamond', '#2196F3');
            startNode.moveTo(50, 200);

            const endNode = new MockNode('end');
            endNode.id = 'system_end';
            endNode.addInputPort('square', '#ff5252');
            endNode.addInputPort('diamond', '#2196F3');
            endNode.moveTo(550, 200);

            mockGameState.nodes = [startNode, endNode];

            log('🎬 Mode 3环境初始化完成', 'success');
            log(`基础节点: ${mockGameState.nodes.length} (1起点, 1终点)`, 'info');
        }

        // 测试函数
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            const timestamp = new Date().toLocaleTimeString();
            div.innerHTML = `<strong>${timestamp}</strong> - ${message}`;
            results.appendChild(div);
            console.log(`[${timestamp}] ${message}`);
            
            testResults.push({ timestamp, message, type });
        }

        function testSingleWave() {
            log('🌊 测试单个波次生成', 'header');
            
            initializeMode3Environment();
            
            const wave = 1;
            const globalSolution = generateGlobalConstraintSatisfiedSystem(wave);
            
            if (globalSolution.isValid) {
                mockGameState.temporaryNodes = globalSolution.temporaryNodes;
                
                log(`✅ 波次 ${wave} 生成成功`, 'success');
                log(`<div class="wave-status">
                    临时节点数: ${globalSolution.temporaryNodes.length}<br>
                    总节点数: ${globalSolution.totalNodes}<br>
                    验证状态: ${globalSolution.verification.isValid ? '✅ 可解' : '❌ 不可解'}
                </div>`, 'info');
                
                if (globalSolution.verification.imbalances.length > 0) {
                    log(`⚠️ 端口不平衡: ${globalSolution.verification.imbalances.join(', ')}`, 'warning');
                }
            } else {
                log(`❌ 波次 ${wave} 生成失败`, 'error');
                if (globalSolution.error) {
                    log(`错误详情: ${globalSolution.error}`, 'error');
                }
            }
        }

        function testWaveProgression() {
            log('🔄 测试多波次推进', 'header');
            
            initializeMode3Environment();
            
            for (let wave = 1; wave <= 3; wave++) {
                log(`--- 波次 ${wave} ---`, 'step');
                mockGameState.infiniteMode.currentWave = wave;
                
                // 1. 生成波次内容
                const globalSolution = generateGlobalConstraintSatisfiedSystem(wave);
                
                if (globalSolution.isValid) {
                    mockGameState.temporaryNodes = globalSolution.temporaryNodes;
                    log(`✅ 波次 ${wave} 内容生成成功`, 'success');
                    
                    // 2. 模拟玩家验证通过，执行结构修改
                    if (wave < 3) { // 不在最后一波修改
                        const modifications = performStructuralModifications();
                        const propagationResult = applyConstraintPropagation(modifications);
                        
                        if (propagationResult.success) {
                            log(`✅ 波次 ${wave} 结构修改成功`, 'success');
                            log(`修改数量: ${propagationResult.modifications}`, 'step');
                        } else {
                            log(`⚠️ 波次 ${wave} 结构修改需要调整`, 'warning');
                        }
                    }
                } else {
                    log(`❌ 波次 ${wave} 生成失败`, 'error');
                    break;
                }
                
                const currentStats = analyzeGlobalSystemState();
                log(`<div class="analysis">
                    摆放区节点: ${currentStats.placedNodes.length}<br>
                    临时区节点: ${currentStats.temporaryNodes.length}<br>
                    总端口数: ${currentStats.totalPortCount}
                </div>`, 'info');
            }
        }

        function testGlobalConstraintSatisfaction() {
            log('🌐 测试全局约束满足算法', 'header');
            
            initializeMode3Environment();
            
            for (let difficulty = 1; difficulty <= 3; difficulty++) {
                log(`测试难度级别 ${difficulty}`, 'step');
                
                const globalSolution = generateGlobalConstraintSatisfiedSystem(difficulty);
                
                if (globalSolution.isValid) {
                    const verification = globalSolution.verification;
                    log(`✅ 难度 ${difficulty}: 生成 ${globalSolution.temporaryNodes.length} 个临时节点`, 'success');
                    log(`总节点数: ${verification.allNodes}, 类型数: ${verification.typeCount}`, 'step');
                    
                    if (verification.imbalances.length === 0) {
                        log(`🎯 端口完全平衡`, 'success');
                    } else {
                        log(`⚠️ 端口不平衡: ${verification.imbalances.join(', ')}`, 'warning');
                    }
                } else {
                    log(`❌ 难度 ${difficulty}: 生成失败`, 'error');
                }
            }
        }

        function runCompleteMode3Test() {
            log('🚀 开始完整Mode 3系统测试', 'header');
            
            log('按照SYSTEM.md要求测试完整游戏循环...', 'info');
            
            // 步骤1: 初始化
            initializeMode3Environment();
            
            // 步骤2: 测试多个完整循环
            for (let cycle = 1; cycle <= 2; cycle++) {
                log(`\n=== 游戏循环 ${cycle} ===`, 'header');
                
                // SYSTEM.md 步骤1: 生成node pool，保证整体可解
                log('📋 步骤1: 生成node pool(包含起点终点)，保证临时区和摆放区的node pool整体可解', 'step');
                const globalSolution = generateGlobalConstraintSatisfiedSystem(cycle);
                
                if (!globalSolution.isValid) {
                    log(`❌ 循环 ${cycle} 步骤1失败: 无法生成可解的node pool`, 'error');
                    continue;
                }
                
                mockGameState.temporaryNodes = globalSolution.temporaryNodes;
                log(`✅ 生成可解node pool: ${globalSolution.totalNodes} 总节点`, 'success');
                
                // SYSTEM.md 步骤2: 玩家进行连接 (模拟)
                log('🎮 步骤2: 玩家进行连接 (模拟连接完成)', 'step');
                log('✅ 模拟连接完成', 'success');
                
                // SYSTEM.md 步骤3: 玩家点击播放流动/验证 (模拟)
                log('▶️ 步骤3: 玩家点击播放流动/验证 (模拟验证通过)', 'step');
                log('✅ 模拟验证通过', 'success');
                
                // SYSTEM.md 步骤4: 验证通过后过关，随机修改已连接好的node pool
                log('🔧 步骤4: 验证通过后过关，随机修改已经连接好的node pool', 'step');
                const modifications = performStructuralModifications();
                const propagationResult = applyConstraintPropagation(modifications);
                
                if (propagationResult.success) {
                    log(`✅ 结构修改成功: ${propagationResult.modifications} 项修改`, 'success');
                } else {
                    log(`⚠️ 结构修改产生不可解状态，需要调整`, 'warning');
                }
                
                // SYSTEM.md 步骤5: 回到1，以修改后的node pool为基础生成新的总体node pool
                log('🔄 步骤5: 回到1，以修改后的node pool为基础生成新的总体的node pool', 'step');
                
                const currentStats = analyzeGlobalSystemState();
                log(`<div class="wave-status">
                    当前系统状态:<br>
                    - 摆放区节点: ${currentStats.placedNodes.length}<br>
                    - 起点节点: ${currentStats.startNodes.length}<br>
                    - 终点节点: ${currentStats.endNodes.length}<br>
                    - 总端口数: ${currentStats.totalPortCount}<br>
                    - 系统可解性: ${propagationResult.success ? '✅ 可解' : '❌ 不可解'}
                </div>`, 'info');
            }
            
            log('\n🎉 完整Mode 3系统测试完成！', 'success');
            
            // 测试结果摘要
            const successCount = testResults.filter(r => r.type === 'success').length;
            const errorCount = testResults.filter(r => r.type === 'error').length;
            const warningCount = testResults.filter(r => r.type === 'warning').length;
            
            log(`<div class="analysis">
                测试摘要:<br>
                ✅ 成功: ${successCount}<br>
                ❌ 错误: ${errorCount}<br>
                ⚠️ 警告: ${warningCount}<br>
                总体评估: ${errorCount === 0 ? '✅ 系统正常' : '⚠️ 需要调整'}
            </div>`, 'header');
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            testResults = [];
            log('📋 测试结果已清空', 'info');
        }

        // 页面加载时的初始化
        window.addEventListener('load', () => {
            log('🎮 Mode 3完整系统测试就绪', 'info');
        });
    </script>
</body>
</html>