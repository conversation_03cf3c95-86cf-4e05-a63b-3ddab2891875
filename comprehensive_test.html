<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Algorithm Test</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 20px;
            background-color: #1e1e1e;
            color: #d4d4d4;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        h1 {
            color: #569cd6;
            text-align: center;
        }
        
        .test-controls {
            background: #2d2d30;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-output {
            background: #1e1e1e;
            border: 1px solid #3e3e42;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
            line-height: 1.3;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .success { color: #4ec9b0; }
        .error { color: #f44747; }
        .warning { color: #ffcc02; }
        .info { color: #9cdcfe; }
        
        button {
            background: #0e639c;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        button:hover {
            background: #1177bb;
        }
        
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .progress {
            width: 100%;
            height: 20px;
            background: #3e3e42;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #0e639c, #1177bb);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .test-summary {
            background: #2d2d30;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
            display: none;
        }
        
        .test-summary.show {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧮 Comprehensive Layered DAG Algorithm Test</h1>
        
        <div class="test-controls">
            <button id="runAllTests" onclick="runAllTests()">🚀 Run All Tests</button>
            <button onclick="runBasicTests()">🔧 Basic Tests</button>
            <button onclick="runPortBalanceTests()">⚖️ Port Balance Tests</button>
            <button onclick="runDepthTests()">📊 Depth Constraint Tests</button>
            <button onclick="runEdgeCaseTests()">🔍 Edge Case Tests</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
            
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
        </div>
        
        <div class="test-output" id="testOutput">
            <div class="info">Ready to run comprehensive algorithm tests...</div>
        </div>
        
        <div class="test-summary" id="testSummary">
            <h3>📊 Test Summary</h3>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script src="game.js?v=layered-dag-algorithm-2025"></script>
    
    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            errors: []
        };
        
        function log(message, type = 'info') {
            const output = document.getElementById('testOutput');
            const timestamp = new Date().toLocaleTimeString();
            
            const div = document.createElement('div');
            div.className = type;
            div.textContent = '[' + timestamp + '] ' + message;
            output.appendChild(div);
            
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
            document.getElementById('testSummary').classList.remove('show');
            testResults = { total: 0, passed: 0, failed: 0, errors: [] };
        }
        
        function updateProgress(percent) {
            document.getElementById('progressBar').style.width = percent + '%';
        }
        
        function recordTest(testName, passed, error = null) {
            testResults.total++;
            if (passed) {
                testResults.passed++;
                log('✅ ' + testName + ': PASSED', 'success');
            } else {
                testResults.failed++;
                testResults.errors.push(testName + ': ' + (error || 'Unknown error'));
                log('❌ ' + testName + ': FAILED' + (error ? ' - ' + error : ''), 'error');
            }
        }
        
        function showSummary() {
            const summary = document.getElementById('testSummary');
            const content = document.getElementById('summaryContent');
            
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            
            content.innerHTML = `
                <p><strong>Total Tests:</strong> ${testResults.total}</p>
                <p><strong>Passed:</strong> <span class="success">${testResults.passed}</span></p>
                <p><strong>Failed:</strong> <span class="error">${testResults.failed}</span></p>
                <p><strong>Success Rate:</strong> ${successRate}%</p>
                ${testResults.errors.length > 0 ? '<p><strong>Errors:</strong></p><ul>' + testResults.errors.map(e => '<li class="error">' + e + '</li>').join('') + '</ul>' : ''}
            `;
            
            summary.classList.add('show');
        }
        
        async function runAllTests() {
            clearOutput();
            log('🚀 Starting comprehensive algorithm test suite...', 'info');
            
            const button = document.getElementById('runAllTests');
            button.disabled = true;
            button.textContent = 'Running Tests...';
            
            try {
                await runBasicTests(false);
                await delay(100);
                updateProgress(25);
                
                await runPortBalanceTests(false);
                await delay(100);
                updateProgress(50);
                
                await runDepthTests(false);
                await delay(100);
                updateProgress(75);
                
                await runEdgeCaseTests(false);
                await delay(100);
                updateProgress(100);
                
                log('🎉 All tests completed!', 'success');
                showSummary();
                
            } catch (error) {
                log('💥 Test suite failed: ' + error.message, 'error');
            } finally {
                button.disabled = false;
                button.textContent = '🚀 Run All Tests';
            }
        }
        
        async function runBasicTests(standalone = true) {
            if (standalone) {
                clearOutput();
                log('🔧 Running basic algorithm tests...', 'info');
            }
            
            // Test 1: Function availability
            const requiredFunctions = [
                'generateDeterministicSolvableScenario',
                'calculateLayeredScenarioSpec',
                'generateLayeredPortPlan',
                'buildLayeredScenarioFromPortPlan',
                'validateLayeredScenarioCompletely'
            ];
            
            requiredFunctions.forEach(funcName => {
                recordTest('Function ' + funcName + ' available', typeof window[funcName] === 'function');
            });
            
            // Test 2: Basic scenario generation
            for (let level = 1; level <= 5; level++) {
                try {
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4))
                    };
                    
                    const scenario = generateDeterministicSolvableScenario(difficulty);
                    recordTest('Level ' + level + ' generation', scenario !== null && scenario !== undefined);
                    
                    if (scenario) {
                        recordTest('Level ' + level + ' has start node', scenario.startNode && scenario.startNode.type === 'start');
                        recordTest('Level ' + level + ' has end node', scenario.endNode && scenario.endNode.type === 'end');
                        recordTest('Level ' + level + ' has connections', scenario.guaranteedConnections && scenario.guaranteedConnections.length > 0);
                    }
                    
                } catch (error) {
                    recordTest('Level ' + level + ' generation', false, error.message);
                }
            }
            
            if (standalone) {
                log('🔧 Basic tests completed', 'info');
                showSummary();
            }
        }
        
        async function runPortBalanceTests(standalone = true) {
            if (standalone) {
                clearOutput();
                log('⚖️ Running port balance tests...', 'info');
            }
            
            for (let level = 1; level <= 5; level++) {
                try {
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle', 'triangle'].slice(0, Math.min(level + 1, 3)),
                        availableColors: ['#ff5252', '#2196F3', '#4CAF50'].slice(0, Math.min(level + 1, 3))
                    };
                    
                    const scenario = generateDeterministicSolvableScenario(difficulty);
                    
                    if (scenario) {
                        const typeBalance = new Map();
                        
                        // Count all ports
                        const allNodes = [scenario.startNode, scenario.endNode, ...(scenario.nodes || [])];
                        allNodes.forEach(node => {
                            if (node.inputPorts) {
                                node.inputPorts.forEach(port => {
                                    const key = port.type + ':' + port.color;
                                    if (!typeBalance.has(key)) {
                                        typeBalance.set(key, { input: 0, output: 0 });
                                    }
                                    typeBalance.get(key).input++;
                                });
                            }
                            
                            if (node.outputPorts) {
                                node.outputPorts.forEach(port => {
                                    const key = port.type + ':' + port.color;
                                    if (!typeBalance.has(key)) {
                                        typeBalance.set(key, { input: 0, output: 0 });
                                    }
                                    typeBalance.get(key).output++;
                                });
                            }
                        });
                        
                        // Check balance
                        let balanced = true;
                        let imbalanceDetails = [];
                        for (const [typeKey, balance] of typeBalance) {
                            if (balance.input !== balance.output) {
                                balanced = false;
                                imbalanceDetails.push(typeKey + '(in:' + balance.input + ',out:' + balance.output + ')');
                            }
                        }
                        
                        recordTest('Level ' + level + ' port balance', balanced, imbalanceDetails.join(', '));
                    } else {
                        recordTest('Level ' + level + ' port balance', false, 'Scenario generation failed');
                    }
                    
                } catch (error) {
                    recordTest('Level ' + level + ' port balance', false, error.message);
                }
            }
            
            if (standalone) {
                log('⚖️ Port balance tests completed', 'info');
                showSummary();
            }
        }
        
        async function runDepthTests(standalone = true) {
            if (standalone) {
                clearOutput();
                log('📊 Running depth constraint tests...', 'info');
            }
            
            for (let level = 1; level <= 5; level++) {
                try {
                    const difficulty = {
                        level: level,
                        availableTypes: ['square', 'circle'],
                        availableColors: ['#ff5252', '#2196F3']
                    };
                    
                    const scenario = generateDeterministicSolvableScenario(difficulty);
                    
                    if (scenario) {
                        // Test depth assignments
                        recordTest('Level ' + level + ' start depth is 0', scenario.startNode.depth === 0);
                        recordTest('Level ' + level + ' end depth > 0', scenario.endNode.depth > 0);
                        
                        // Test connection depth constraints
                        if (scenario.guaranteedConnections) {
                            let validDepths = 0;
                            scenario.guaranteedConnections.forEach(conn => {
                                if (conn.sourceDepth < conn.targetDepth) {
                                    validDepths++;
                                }
                            });
                            
                            const allValid = validDepths === scenario.guaranteedConnections.length;
                            recordTest('Level ' + level + ' depth constraints', allValid, 
                                'Valid: ' + validDepths + '/' + scenario.guaranteedConnections.length);
                        }
                        
                        // Test intermediate node depths
                        if (scenario.nodes) {
                            let validIntermediateDepths = true;
                            scenario.nodes.forEach(node => {
                                if (node.depth <= 0 || node.depth >= scenario.endNode.depth) {
                                    validIntermediateDepths = false;
                                }
                            });
                            recordTest('Level ' + level + ' intermediate depths', validIntermediateDepths);
                        }
                    } else {
                        recordTest('Level ' + level + ' depth constraints', false, 'Scenario generation failed');
                    }
                    
                } catch (error) {
                    recordTest('Level ' + level + ' depth constraints', false, error.message);
                }
            }
            
            if (standalone) {
                log('📊 Depth constraint tests completed', 'info');
                showSummary();
            }
        }
        
        async function runEdgeCaseTests(standalone = true) {
            if (standalone) {
                clearOutput();
                log('🔍 Running edge case tests...', 'info');
            }
            
            const edgeCases = [
                { name: 'Level 0', level: 0, availableTypes: ['square'], availableColors: ['#ff5252'] },
                { name: 'High level', level: 10, availableTypes: ['square', 'circle'], availableColors: ['#ff5252', '#2196F3'] },
                { name: 'No types', level: 3, availableTypes: [], availableColors: ['#ff5252'] },
                { name: 'No colors', level: 3, availableTypes: ['square'], availableColors: [] },
                { name: 'Single type/color', level: 3, availableTypes: ['square'], availableColors: ['#ff5252'] },
                { name: 'Many types', level: 2, availableTypes: ['square', 'circle', 'triangle', 'diamond'], availableColors: ['#ff5252'] }
            ];
            
            edgeCases.forEach(testCase => {
                try {
                    const scenario = generateDeterministicSolvableScenario(testCase);
                    
                    // For edge cases, we accept either a valid scenario or graceful failure (null)
                    const handled = scenario === null || (scenario && scenario.startNode && scenario.endNode);
                    recordTest('Edge case: ' + testCase.name, handled);
                    
                } catch (error) {
                    // Edge cases may throw errors, which is acceptable
                    recordTest('Edge case: ' + testCase.name, true, 'Threw error (acceptable): ' + error.message);
                }
            });
            
            if (standalone) {
                log('🔍 Edge case tests completed', 'info');
                showSummary();
            }
        }
        
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(() => {
                log('Page loaded, ready to run comprehensive tests', 'info');
            }, 500);
        });
        
        // Catch global errors
        window.addEventListener('error', (event) => {
            log('GLOBAL ERROR: ' + event.message + ' at line ' + event.lineno, 'error');
        });
    </script>
</body>
</html>
