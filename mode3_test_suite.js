// ========== 模式3测试套件 ==========

class Mode3TestSuite {
    constructor() {
        this.testResults = [];
        this.currentTest = null;
    }

    // 运行完整的模式3测试套件
    async runComprehensiveMode3Tests() {
        console.log('[MODE3-TEST] Starting comprehensive Mode 3 test suite');
        
        const testSuites = [
            this.testContinuousExpansionStability,
            this.testConstraintPreservation,
            this.testIncrementalSolvability,
            this.testEdgeCases,
            this.testPerformanceBenchmarks
        ];
        
        const results = [];
        
        for (const testSuite of testSuites) {
            try {
                const result = await testSuite.call(this);
                results.push(result);
                console.log(`[MODE3-TEST] ${result.name}: ${result.passed ? 'PASSED' : 'FAILED'}`);
            } catch (error) {
                console.error(`[MODE3-TEST] Test suite failed:`, error);
                results.push({
                    name: testSuite.name,
                    passed: false,
                    error: error.message
                });
            }
        }
        
        return this.generateTestReport(results);
    }

    // 测试1: 连续扩展稳定性
    async testContinuousExpansionStability() {
        console.log('[MODE3-TEST] Testing continuous expansion stability');
        
        let graph = this.generateInitialGraph();
        const expansionResults = [];
        
        for (let wave = 1; wave <= 20; wave++) {
            const beforeSize = graph.nodes.length;
            
            try {
                // 应用扩展
                const modification = {
                    addNodes: this.generateWaveNodes(wave),
                    addConnections: []
                };
                
                graph = mode3Engine.maintainStrongSolvabilityDuringModification(graph, modification);
                
                // 验证扩展后仍然强可解
                const validation = systemMdValidator.validateAllConstraints(graph.nodes);
                
                expansionResults.push({
                    wave,
                    beforeSize,
                    afterSize: graph.nodes.length,
                    solvable: validation.allSatisfied,
                    constraints: validation
                });
                
                if (!validation.allSatisfied) {
                    console.error(`[MODE3-TEST] Wave ${wave} broke solvability:`, validation);
                    break;
                }
                
                // 验证图确实在增长
                if (graph.nodes.length <= beforeSize) {
                    console.error(`[MODE3-TEST] Wave ${wave} did not expand the graph`);
                    break;
                }
                
            } catch (error) {
                console.error(`[MODE3-TEST] Wave ${wave} expansion failed:`, error);
                break;
            }
        }
        
        const allPassed = expansionResults.every(result => result.solvable);
        
        return {
            name: 'Continuous Expansion Stability',
            passed: allPassed,
            details: expansionResults,
            summary: `${expansionResults.filter(r => r.solvable).length}/${expansionResults.length} waves maintained solvability`
        };
    }

    // 测试2: 约束保持测试
    async testConstraintPreservation() {
        console.log('[MODE3-TEST] Testing constraint preservation');
        
        const testCases = [
            { name: 'Port Type Balance', constraint: 'portTypeBalance' },
            { name: 'DAG Topology', constraint: 'dagTopology' },
            { name: 'Flow Conservation', constraint: 'flowConservation' },
            { name: 'Port Mapping', constraint: 'portMapping' }
        ];
        
        const results = [];
        
        for (const testCase of testCases) {
            const graph = this.generateTestGraph();
            const beforeValidation = systemMdValidator.validateAllConstraints(graph.nodes);
            
            // 应用修改
            const modification = this.generateConstraintTestModification(testCase.constraint);
            const modifiedGraph = mode3Engine.maintainStrongSolvabilityDuringModification(graph, modification);
            const afterValidation = systemMdValidator.validateAllConstraints(modifiedGraph.nodes);
            
            const constraintPreserved = afterValidation[testCase.constraint].satisfied;
            
            results.push({
                constraint: testCase.name,
                beforeSatisfied: beforeValidation[testCase.constraint].satisfied,
                afterSatisfied: constraintPreserved,
                preserved: constraintPreserved
            });
        }
        
        const allPreserved = results.every(r => r.preserved);
        
        return {
            name: 'Constraint Preservation',
            passed: allPreserved,
            details: results,
            summary: `${results.filter(r => r.preserved).length}/${results.length} constraints preserved`
        };
    }

    // 测试3: 增量可解性测试
    async testIncrementalSolvability() {
        console.log('[MODE3-TEST] Testing incremental solvability');
        
        const graph = this.generateInitialGraph();
        const modifications = [
            { type: 'add_node', nodeType: 'intermediate' },
            { type: 'add_connection', random: true },
            { type: 'add_multiple_nodes', count: 3 },
            { type: 'complex_modification', nodes: 2, connections: 3 }
        ];
        
        const results = [];
        let currentGraph = graph;
        
        for (const modification of modifications) {
            try {
                const beforeValidation = systemMdValidator.validateAllConstraints(currentGraph.nodes);
                const modificationRequest = this.createModificationRequest(modification);
                
                currentGraph = mode3Engine.maintainStrongSolvabilityDuringModification(currentGraph, modificationRequest);
                const afterValidation = systemMdValidator.validateAllConstraints(currentGraph.nodes);
                
                results.push({
                    modification: modification.type,
                    beforeSolvable: beforeValidation.allSatisfied,
                    afterSolvable: afterValidation.allSatisfied,
                    maintained: afterValidation.allSatisfied
                });
                
            } catch (error) {
                results.push({
                    modification: modification.type,
                    beforeSolvable: true,
                    afterSolvable: false,
                    maintained: false,
                    error: error.message
                });
            }
        }
        
        const allMaintained = results.every(r => r.maintained);
        
        return {
            name: 'Incremental Solvability',
            passed: allMaintained,
            details: results,
            summary: `${results.filter(r => r.maintained).length}/${results.length} modifications maintained solvability`
        };
    }

    // 测试4: 边界情况测试
    async testEdgeCases() {
        console.log('[MODE3-TEST] Testing edge cases');
        
        const edgeCases = [
            {
                name: "单一端口类型",
                config: {
                    portTypes: [{ shape: 'square', color: '#ff5252' }],
                    startNodes: 3,
                    endNodes: 3,
                    middleNodes: 5
                }
            },
            {
                name: "极多端口类型",
                config: {
                    portTypes: this.generateAllPortTypeCombinations(4, 4), // 16种类型
                    startNodes: 1,
                    endNodes: 1,
                    middleNodes: 10
                }
            },
            {
                name: "深度限制",
                config: {
                    maxDepth: 2,
                    middleNodes: 20 // 强制所有中间节点在深度1
                }
            },
            {
                name: "最小图",
                config: {
                    startNodes: 1,
                    endNodes: 1,
                    middleNodes: 0
                }
            }
        ];
        
        const results = [];
        
        for (const edgeCase of edgeCases) {
            try {
                const graph = this.generateGraphFromConfig(edgeCase.config);
                const validation = systemMdValidator.validateAllConstraints(graph.nodes);
                
                results.push({
                    name: edgeCase.name,
                    passed: validation.allSatisfied,
                    nodeCount: graph.nodes.length,
                    constraints: validation
                });
                
            } catch (error) {
                results.push({
                    name: edgeCase.name,
                    passed: false,
                    error: error.message
                });
            }
        }
        
        const allPassed = results.every(r => r.passed);
        
        return {
            name: 'Edge Cases',
            passed: allPassed,
            details: results,
            summary: `${results.filter(r => r.passed).length}/${results.length} edge cases passed`
        };
    }

    // 测试5: 性能基准测试
    async testPerformanceBenchmarks() {
        console.log('[MODE3-TEST] Testing performance benchmarks');
        
        const benchmarks = [
            { name: 'Small Graph', nodeCount: 10, waves: 5 },
            { name: 'Medium Graph', nodeCount: 50, waves: 10 },
            { name: 'Large Graph', nodeCount: 100, waves: 15 }
        ];
        
        const results = [];
        
        for (const benchmark of benchmarks) {
            const startTime = performance.now();
            
            try {
                let graph = this.generateGraphWithNodeCount(benchmark.nodeCount);
                
                for (let wave = 1; wave <= benchmark.waves; wave++) {
                    const modification = {
                        addNodes: this.generateWaveNodes(wave),
                        addConnections: []
                    };
                    
                    graph = mode3Engine.maintainStrongSolvabilityDuringModification(graph, modification);
                }
                
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                results.push({
                    name: benchmark.name,
                    passed: duration < 5000, // 5秒限制
                    duration: duration,
                    finalNodeCount: graph.nodes.length,
                    averageTimePerWave: duration / benchmark.waves
                });
                
            } catch (error) {
                const endTime = performance.now();
                results.push({
                    name: benchmark.name,
                    passed: false,
                    duration: endTime - startTime,
                    error: error.message
                });
            }
        }
        
        const allPassed = results.every(r => r.passed);
        
        return {
            name: 'Performance Benchmarks',
            passed: allPassed,
            details: results,
            summary: `${results.filter(r => r.passed).length}/${results.length} benchmarks passed`
        };
    }

    // 辅助方法
    generateInitialGraph() {
        return {
            nodes: [
                { id: 'start1', type: 'start', depth: 0, inputPorts: [], outputPorts: [{ id: 'start1_out', type: 'square', color: '#ff5252', side: 'output' }] },
                { id: 'end1', type: 'end', depth: 2, inputPorts: [{ id: 'end1_in', type: 'square', color: '#ff5252', side: 'input' }], outputPorts: [] }
            ],
            connections: []
        };
    }

    generateTestGraph() {
        return generateSystemMdCompliantNodePool(2, 2);
    }

    generateWaveNodes(wave) {
        const nodes = [];
        const nodeCount = Math.min(wave, 3);
        
        for (let i = 0; i < nodeCount; i++) {
            nodes.push({
                id: `wave${wave}_node${i}`,
                type: 'intermediate',
                depth: 1,
                inputPorts: [],
                outputPorts: []
            });
        }
        
        return nodes;
    }

    generateConstraintTestModification(constraint) {
        // 生成针对特定约束的测试修改
        switch (constraint) {
            case 'portTypeBalance':
                return {
                    addNodes: [{
                        id: 'balance_test',
                        type: 'intermediate',
                        depth: 1,
                        inputPorts: [{ id: 'bt_in', type: 'circle', color: '#2196F3', side: 'input' }],
                        outputPorts: []
                    }]
                };
            default:
                return { addNodes: [], addConnections: [] };
        }
    }

    createModificationRequest(modification) {
        switch (modification.type) {
            case 'add_node':
                return {
                    addNodes: [{
                        id: `mod_${Date.now()}`,
                        type: modification.nodeType,
                        depth: 1,
                        inputPorts: [],
                        outputPorts: []
                    }]
                };
            default:
                return { addNodes: [], addConnections: [] };
        }
    }

    generateAllPortTypeCombinations(shapeCount, colorCount) {
        const shapes = ['square', 'circle', 'triangle', 'diamond'].slice(0, shapeCount);
        const colors = ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, colorCount);
        const combinations = [];
        
        shapes.forEach(shape => {
            colors.forEach(color => {
                combinations.push({ shape, color });
            });
        });
        
        return combinations;
    }

    generateGraphFromConfig(config) {
        // 根据配置生成图
        return this.generateInitialGraph();
    }

    generateGraphWithNodeCount(nodeCount) {
        const nodes = [];
        
        // 生成指定数量的节点
        for (let i = 0; i < nodeCount; i++) {
            const nodeType = i === 0 ? 'start' : (i === nodeCount - 1 ? 'end' : 'intermediate');
            const depth = i === 0 ? 0 : (i === nodeCount - 1 ? 2 : 1);
            
            nodes.push({
                id: `node_${i}`,
                type: nodeType,
                depth: depth,
                inputPorts: [],
                outputPorts: []
            });
        }
        
        return { nodes, connections: [] };
    }

    generateTestReport(results) {
        const totalTests = results.length;
        const passedTests = results.filter(r => r.passed).length;
        const passRate = (passedTests / totalTests * 100).toFixed(1);
        
        return {
            summary: {
                totalTests,
                passedTests,
                failedTests: totalTests - passedTests,
                passRate: `${passRate}%`
            },
            results,
            overallPassed: passedTests === totalTests
        };
    }
}

// 全局测试套件实例
const mode3TestSuite = new Mode3TestSuite();

// 导出测试函数
window.runMode3ComprehensiveTest = () => mode3TestSuite.runComprehensiveMode3Tests();
window.testMode3ContinuousExpansion = () => mode3TestSuite.testContinuousExpansionStability();
window.testMode3ConstraintPreservation = () => mode3TestSuite.testConstraintPreservation();
window.testMode3IncrementalSolvability = () => mode3TestSuite.testIncrementalSolvability();
window.testMode3EdgeCases = () => mode3TestSuite.testEdgeCases();
window.testMode3Performance = () => mode3TestSuite.testPerformanceBenchmarks();
