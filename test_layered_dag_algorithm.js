// 测试层级DAG算法的数学约束验证
// 验证算法是否满足所有数学规范

// 模拟游戏环境中的必要类和函数
class Port {
    constructor(type, color, side, nodeId) {
        this.id = `port_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.color = color;
        this.side = side;
        this.nodeId = nodeId;
        this.connectedTo = null;
        this.x = 0;
        this.y = 0;
    }
    
    canConnectTo(otherPort) {
        return this.type === otherPort.type && 
               this.color === otherPort.color &&
               this.side !== otherPort.side &&
               this.nodeId !== otherPort.nodeId;
    }
}

class Node {
    constructor(type = 'normal') {
        this.id = `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        this.type = type;
        this.x = 0;
        this.y = 0;
        this.width = 120;
        this.height = 60;
        this.inputPorts = [];
        this.outputPorts = [];
        this.depth = -1;
        this.label = this.generateLabel();
    }
    
    generateLabel() {
        switch(this.type) {
            case 'start': return 'Start';
            case 'end': return 'End';
            default: return `Node ${Math.floor(Math.random() * 1000)}`;
        }
    }
    
    addInputPort(type, color) {
        if (this.type === 'start') return;
        const port = new Port(type, color, 'input', this.id);
        this.inputPorts.push(port);
        return port;
    }
    
    addOutputPort(type, color) {
        if (this.type === 'end') return;
        const port = new Port(type, color, 'output', this.id);
        this.outputPorts.push(port);
        return port;
    }
    
    moveTo(x, y) {
        this.x = x;
        this.y = y;
    }
}

// 测试配置
const TEST_CONFIG = {
    levels: [1, 2, 3, 4, 5],
    testsPerLevel: 10,
    maxExecutionTime: 5000, // 5秒超时
    verbose: true
};

// 测试结果收集器
class TestResultCollector {
    constructor() {
        this.results = [];
        this.summary = {
            totalTests: 0,
            passedTests: 0,
            failedTests: 0,
            errors: []
        };
    }
    
    addResult(testName, level, passed, details = {}) {
        const result = {
            testName,
            level,
            passed,
            timestamp: new Date().toISOString(),
            details
        };
        
        this.results.push(result);
        this.summary.totalTests++;
        
        if (passed) {
            this.summary.passedTests++;
        } else {
            this.summary.failedTests++;
            this.summary.errors.push(`${testName} (Level ${level}): ${details.error || 'Unknown error'}`);
        }
        
        if (TEST_CONFIG.verbose) {
            console.log(`${passed ? '✅' : '❌'} ${testName} (Level ${level}): ${passed ? 'PASSED' : 'FAILED'}`);
            if (!passed && details.error) {
                console.log(`   Error: ${details.error}`);
            }
        }
    }
    
    printSummary() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 层级DAG算法测试总结');
        console.log('='.repeat(60));
        console.log(`总测试数: ${this.summary.totalTests}`);
        console.log(`通过: ${this.summary.passedTests} (${(this.summary.passedTests/this.summary.totalTests*100).toFixed(1)}%)`);
        console.log(`失败: ${this.summary.failedTests} (${(this.summary.failedTests/this.summary.totalTests*100).toFixed(1)}%)`);
        
        if (this.summary.errors.length > 0) {
            console.log('\n❌ 失败的测试:');
            this.summary.errors.forEach(error => console.log(`   ${error}`));
        }
        
        console.log('='.repeat(60));
        return this.summary.failedTests === 0;
    }
}

// 创建测试难度配置
function createTestDifficulty(level) {
    return {
        level: level,
        nodes: level + 1,
        chains: level,
        types: Math.min(level + 1, 3),
        colors: Math.min(level + 1, 3),
        duplicateNodes: level > 3,
        maxPortsPerNode: Math.min(level + 1, 4),
        availableTypes: ['square', 'circle', 'triangle', 'diamond'].slice(0, Math.min(level + 1, 4)),
        availableColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'].slice(0, Math.min(level + 1, 4)),
        multipleSolutions: level > 2
    };
}

// 数学约束验证函数

// 验证1: 层级结构约束 (L_0: 起点, L_max: 终点, L_d: 中间节点)
function validateLayerStructureConstraints(scenario) {
    const errors = [];
    
    // 检查起点在深度0
    if (scenario.startNode.depth !== 0) {
        errors.push(`起点深度错误: 期望0, 实际${scenario.startNode.depth}`);
    }
    
    // 检查终点在最大深度
    const maxDepth = Math.max(...scenario.allNodes.map(n => n.depth));
    if (scenario.endNode.depth !== maxDepth) {
        errors.push(`终点深度错误: 期望${maxDepth}, 实际${scenario.endNode.depth}`);
    }
    
    // 检查中间节点深度范围
    scenario.allNodes.forEach(node => {
        if (node.type === 'normal') {
            if (node.depth <= 0 || node.depth >= maxDepth) {
                errors.push(`中间节点${node.id}深度违规: ${node.depth}`);
            }
        }
    });
    
    return { isValid: errors.length === 0, errors };
}

// 验证2: DAG拓扑约束 (depth(u) < depth(v) for all edges (u,v))
function validateDAGTopologyConstraints(scenario) {
    const errors = [];
    
    if (!scenario.guaranteedConnections) {
        errors.push('缺少连接信息');
        return { isValid: false, errors };
    }
    
    // 检查所有连接满足深度约束
    scenario.guaranteedConnections.forEach((conn, index) => {
        if (conn.sourceDepth >= conn.targetDepth) {
            errors.push(`连接${index + 1}违反DAG约束: depth(${conn.sourceDepth}) >= depth(${conn.targetDepth})`);
        }
    });
    
    return { isValid: errors.length === 0, errors };
}

// 验证3: 端口类型平衡约束 (∀τ: |input_ports(τ)| = |output_ports(τ)|)
function validatePortTypeBalanceConstraints(scenario) {
    const errors = [];
    const typeBalance = new Map();
    
    // 统计所有端口类型
    scenario.allNodes.forEach(node => {
        node.inputPorts.forEach(port => {
            const key = `${port.type}:${port.color}`;
            if (!typeBalance.has(key)) {
                typeBalance.set(key, { input: 0, output: 0 });
            }
            typeBalance.get(key).input++;
        });
        
        node.outputPorts.forEach(port => {
            const key = `${port.type}:${port.color}`;
            if (!typeBalance.has(key)) {
                typeBalance.set(key, { input: 0, output: 0 });
            }
            typeBalance.get(key).output++;
        });
    });
    
    // 检查平衡性
    for (const [typeKey, balance] of typeBalance) {
        if (balance.input !== balance.output) {
            errors.push(`端口类型${typeKey}不平衡: 输入${balance.input} ≠ 输出${balance.output}`);
        }
    }
    
    return { isValid: errors.length === 0, errors, typeBalance };
}

// 验证4: 端口流守恒约束
function validatePortFlowConservationConstraints(scenario) {
    const errors = [];
    
    scenario.allNodes.forEach(node => {
        if (node.type === 'start') {
            // 起点: 只有输出端口, 无输入端口
            if (node.inputPorts.length > 0) {
                errors.push(`起点${node.id}违反流守恒: 有${node.inputPorts.length}个输入端口`);
            }
            if (node.outputPorts.length === 0) {
                errors.push(`起点${node.id}违反流守恒: 无输出端口`);
            }
        } else if (node.type === 'end') {
            // 终点: 只有输入端口, 无输出端口
            if (node.outputPorts.length > 0) {
                errors.push(`终点${node.id}违反流守恒: 有${node.outputPorts.length}个输出端口`);
            }
            if (node.inputPorts.length === 0) {
                errors.push(`终点${node.id}违反流守恒: 无输入端口`);
            }
        } else if (node.type === 'normal') {
            // 中间节点: 必须有输入和输出端口
            if (node.inputPorts.length === 0) {
                errors.push(`中间节点${node.id}违反流守恒: 无输入端口`);
            }
            if (node.outputPorts.length === 0) {
                errors.push(`中间节点${node.id}违反流守恒: 无输出端口`);
            }
        }
    });
    
    return { isValid: errors.length === 0, errors };
}

// 验证5: 连通性约束 (每个节点在至少一条有效路径上)
function validateConnectivityConstraints(scenario) {
    const errors = [];
    
    // 构建图
    const graph = new Map();
    scenario.guaranteedConnections.forEach(conn => {
        if (!graph.has(conn.from.node)) {
            graph.set(conn.from.node, []);
        }
        graph.get(conn.from.node).push(conn.to.node);
    });
    
    // 检查从起点到终点的路径存在性
    const pathExists = checkPathExistence(scenario.startNode.id, scenario.endNode.id, graph);
    if (!pathExists) {
        errors.push('不存在从起点到终点的路径');
    }
    
    // 检查每个中间节点都在某条路径上
    scenario.allNodes.forEach(node => {
        if (node.type === 'normal') {
            const onPath = isNodeOnValidPath(node.id, scenario.startNode.id, scenario.endNode.id, graph);
            if (!onPath) {
                errors.push(`中间节点${node.id}不在任何有效路径上`);
            }
        }
    });
    
    return { isValid: errors.length === 0, errors };
}

// 辅助函数: 检查路径存在性
function checkPathExistence(startId, endId, graph) {
    const queue = [startId];
    const visited = new Set();
    
    while (queue.length > 0) {
        const currentId = queue.shift();
        
        if (visited.has(currentId)) continue;
        visited.add(currentId);
        
        if (currentId === endId) return true;
        
        const neighbors = graph.get(currentId) || [];
        neighbors.forEach(neighbor => {
            if (!visited.has(neighbor)) {
                queue.push(neighbor);
            }
        });
    }
    
    return false;
}

// 辅助函数: 检查节点是否在有效路径上
function isNodeOnValidPath(nodeId, startId, endId, graph) {
    // 检查从起点能否到达该节点
    const canReachFromStart = checkPathExistence(startId, nodeId, graph);

    // 检查从该节点能否到达终点
    const canReachEnd = checkPathExistence(nodeId, endId, graph);

    return canReachFromStart && canReachEnd;
}

// 主测试函数
function runLayeredDAGAlgorithmTests() {
    console.log('🚀 开始层级DAG算法数学约束验证测试\n');

    const collector = new TestResultCollector();

    // 导入算法函数 (需要从game.js中获取)
    if (typeof generateDeterministicSolvableScenario === 'undefined') {
        console.error('❌ 无法找到算法函数，请确保已加载game.js');
        return false;
    }

    // 对每个关卡进行测试
    for (const level of TEST_CONFIG.levels) {
        console.log(`\n--- 测试关卡 ${level} ---`);

        for (let testIndex = 0; testIndex < TEST_CONFIG.testsPerLevel; testIndex++) {
            const difficulty = createTestDifficulty(level);

            try {
                // 生成场景
                const startTime = performance.now();
                const scenario = generateDeterministicSolvableScenario(difficulty);
                const generationTime = performance.now() - startTime;

                if (generationTime > TEST_CONFIG.maxExecutionTime) {
                    collector.addResult(`生成性能测试`, level, false, {
                        error: `生成时间超时: ${generationTime.toFixed(2)}ms > ${TEST_CONFIG.maxExecutionTime}ms`
                    });
                    continue;
                }

                // 运行所有数学约束验证
                runMathematicalConstraintTests(scenario, level, testIndex, collector);

            } catch (error) {
                collector.addResult(`算法执行测试`, level, false, {
                    error: `算法执行失败: ${error.message}`
                });
            }
        }
    }

    return collector.printSummary();
}

// 运行数学约束测试
function runMathematicalConstraintTests(scenario, level, testIndex, collector) {
    const testPrefix = `L${level}T${testIndex + 1}`;

    // 测试1: 层级结构约束
    const layerTest = validateLayerStructureConstraints(scenario);
    collector.addResult(`${testPrefix}_层级结构`, level, layerTest.isValid, {
        error: layerTest.errors.join('; ')
    });

    // 测试2: DAG拓扑约束
    const dagTest = validateDAGTopologyConstraints(scenario);
    collector.addResult(`${testPrefix}_DAG拓扑`, level, dagTest.isValid, {
        error: dagTest.errors.join('; ')
    });

    // 测试3: 端口类型平衡约束
    const balanceTest = validatePortTypeBalanceConstraints(scenario);
    collector.addResult(`${testPrefix}_端口平衡`, level, balanceTest.isValid, {
        error: balanceTest.errors.join('; '),
        typeBalance: balanceTest.typeBalance
    });

    // 测试4: 端口流守恒约束
    const flowTest = validatePortFlowConservationConstraints(scenario);
    collector.addResult(`${testPrefix}_流守恒`, level, flowTest.isValid, {
        error: flowTest.errors.join('; ')
    });

    // 测试5: 连通性约束
    const connectivityTest = validateConnectivityConstraints(scenario);
    collector.addResult(`${testPrefix}_连通性`, level, connectivityTest.isValid, {
        error: connectivityTest.errors.join('; ')
    });

    // 测试6: 基本结构完整性
    const structureTest = validateBasicStructure(scenario);
    collector.addResult(`${testPrefix}_基本结构`, level, structureTest.isValid, {
        error: structureTest.errors.join('; ')
    });
}

// 验证基本结构完整性
function validateBasicStructure(scenario) {
    const errors = [];

    if (!scenario.startNode) errors.push('缺少起点节点');
    if (!scenario.endNode) errors.push('缺少终点节点');
    if (!scenario.allNodes || scenario.allNodes.length === 0) errors.push('缺少节点列表');
    if (!scenario.guaranteedConnections) errors.push('缺少连接信息');
    if (!scenario.nodesByLayer) errors.push('缺少层级信息');

    // 检查节点ID唯一性
    const nodeIds = new Set();
    scenario.allNodes.forEach(node => {
        if (nodeIds.has(node.id)) {
            errors.push(`节点ID重复: ${node.id}`);
        }
        nodeIds.add(node.id);
    });

    return { isValid: errors.length === 0, errors };
}

// 性能基准测试
function runPerformanceBenchmark() {
    console.log('\n🏃‍♂️ 运行性能基准测试...');

    const benchmarkResults = [];

    for (const level of TEST_CONFIG.levels) {
        const difficulty = createTestDifficulty(level);
        const iterations = 100;
        const times = [];

        for (let i = 0; i < iterations; i++) {
            const startTime = performance.now();
            try {
                generateDeterministicSolvableScenario(difficulty);
                const endTime = performance.now();
                times.push(endTime - startTime);
            } catch (error) {
                console.warn(`性能测试失败 (Level ${level}, Iteration ${i}): ${error.message}`);
            }
        }

        if (times.length > 0) {
            const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
            const maxTime = Math.max(...times);
            const minTime = Math.min(...times);

            benchmarkResults.push({
                level,
                avgTime: avgTime.toFixed(2),
                maxTime: maxTime.toFixed(2),
                minTime: minTime.toFixed(2),
                successRate: (times.length / iterations * 100).toFixed(1)
            });
        }
    }

    console.log('\n📊 性能基准结果:');
    console.log('关卡 | 平均时间(ms) | 最大时间(ms) | 最小时间(ms) | 成功率(%)');
    console.log('-'.repeat(65));
    benchmarkResults.forEach(result => {
        console.log(`  ${result.level}  |     ${result.avgTime}     |     ${result.maxTime}     |     ${result.minTime}     |    ${result.successRate}`);
    });
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runLayeredDAGAlgorithmTests,
        runPerformanceBenchmark,
        validateLayerStructureConstraints,
        validateDAGTopologyConstraints,
        validatePortTypeBalanceConstraints,
        validatePortFlowConservationConstraints,
        validateConnectivityConstraints
    };
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.runLayeredDAGAlgorithmTests = runLayeredDAGAlgorithmTests;
    window.runPerformanceBenchmark = runPerformanceBenchmark;
} else if (typeof global !== 'undefined') {
    // Node.js环境
    global.runLayeredDAGAlgorithmTests = runLayeredDAGAlgorithmTests;
    global.runPerformanceBenchmark = runPerformanceBenchmark;
}
