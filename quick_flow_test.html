<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Flow-First Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover { background: #45a049; }
    </style>
</head>
<body>
    <h1>🚀 Quick Flow-First Algorithm Test</h1>
    
    <button onclick="runQuickTest()">⚡ Run Quick Test</button>
    <button onclick="clearOutput()">🧹 Clear</button>
    
    <div class="output" id="output"></div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[QUICK] Game script loaded');
            runQuickTest();
        };
        script.onerror = function() {
            console.error('[QUICK] Failed to load game script');
        };
        document.head.appendChild(script);

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            const className = type === 'error' ? 'fail' : (message.includes('SUCCESS') ? 'pass' : '');
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        function runQuickTest() {
            console.log('[QUICK] Starting quick Flow-First algorithm test...');
            
            try {
                // Test 1: Check if Flow-First functions exist
                console.log('[QUICK] Checking Flow-First functions...');
                
                const functions = [
                    'analyzeRequiredFlows',
                    'designFlowNetwork', 
                    'synthesizeNodesFromFlows',
                    'validateAndOptimizeDAG'
                ];
                
                functions.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        console.log(`[QUICK] ✅ ${funcName} - Available`);
                    } else {
                        console.log(`[QUICK] ❌ ${funcName} - Missing`);
                    }
                });
                
                // Test 2: Test Flow-First algorithm with simple input
                console.log('[QUICK] Testing Flow-First algorithm...');
                
                const testRequirements = {
                    unconnectedOutputs: [
                        { type: 'square', color: '#ff5252', nodeId: 'test_start', depth: 0 }
                    ],
                    unconnectedInputs: [
                        { type: 'square', color: '#ff5252', nodeId: 'test_end', depth: 999 }
                    ]
                };
                
                console.log('[QUICK] Test requirements:', testRequirements);
                
                // Test generateNodesForRequirements
                if (typeof generateNodesForRequirements === 'function') {
                    console.log('[QUICK] Testing generateNodesForRequirements...');
                    
                    const startTime = performance.now();
                    const result = generateNodesForRequirements(testRequirements, 1, 1);
                    const endTime = performance.now();
                    
                    console.log(`[QUICK] Algorithm completed in ${(endTime - startTime).toFixed(2)}ms`);
                    console.log('[QUICK] Result:', result);
                    
                    if (result && result.length > 0) {
                        console.log(`[QUICK] ✅ SUCCESS - Generated ${result.length} nodes`);
                        
                        // Test validation
                        console.log('[QUICK] Testing validation functions...');
                        
                        try {
                            const portBalance = validatePortBalance(result);
                            console.log(`[QUICK] Port Balance: ${portBalance.isValid ? 'PASS' : 'FAIL'}`);
                            
                            const dagTopology = validateDAGTopologyNew(result);
                            console.log(`[QUICK] DAG Topology: ${dagTopology.isValid ? 'PASS' : 'FAIL'}`);
                            
                            const flowConservation = validateFlowConservation(result);
                            console.log(`[QUICK] Flow Conservation: ${flowConservation.isValid ? 'PASS' : 'FAIL'}`);
                            
                            const portMapping = validatePortMapping(result);
                            console.log(`[QUICK] Port Mapping: ${portMapping.isValid ? 'PASS' : 'FAIL'}`);
                            
                            const allValid = portBalance.isValid && dagTopology.isValid && 
                                           flowConservation.isValid && portMapping.isValid;
                            
                            console.log(`[QUICK] Overall Validation: ${allValid ? 'ALL PASS' : 'SOME FAILURES'}`);
                            
                        } catch (validationError) {
                            console.error('[QUICK] Validation error:', validationError);
                        }
                        
                    } else {
                        console.log('[QUICK] ❌ FAIL - No nodes generated');
                    }
                    
                } else {
                    console.log('[QUICK] ❌ generateNodesForRequirements function not available');
                }
                
                // Test 3: Test individual Flow-First stages
                console.log('[QUICK] Testing individual Flow-First stages...');
                
                try {
                    if (typeof analyzeRequiredFlows === 'function') {
                        const flowAnalysis = analyzeRequiredFlows(testRequirements, 1, 1);
                        console.log('[QUICK] ✅ analyzeRequiredFlows completed');
                        console.log('[QUICK] Flow analysis result:', flowAnalysis);
                        
                        if (typeof designFlowNetwork === 'function') {
                            const flowNetwork = designFlowNetwork(flowAnalysis);
                            console.log('[QUICK] ✅ designFlowNetwork completed');
                            console.log('[QUICK] Flow network result:', flowNetwork);
                            
                            if (typeof synthesizeNodesFromFlows === 'function') {
                                const nodes = synthesizeNodesFromFlows(flowNetwork, 1);
                                console.log('[QUICK] ✅ synthesizeNodesFromFlows completed');
                                console.log('[QUICK] Synthesized nodes:', nodes);
                                
                                if (typeof validateAndOptimizeDAG === 'function') {
                                    const validation = validateAndOptimizeDAG(nodes, flowAnalysis);
                                    console.log('[QUICK] ✅ validateAndOptimizeDAG completed');
                                    console.log('[QUICK] Validation result:', validation);
                                }
                            }
                        }
                    }
                } catch (stageError) {
                    console.error('[QUICK] Stage test error:', stageError);
                }
                
                console.log('[QUICK] Quick test completed');
                
            } catch (error) {
                console.error('[QUICK] Quick test failed:', error);
            }
        }

        function clearOutput() {
            output.innerHTML = '';
        }

        // Initialize
        console.log('[QUICK] Quick Flow-First test framework loaded');
    </script>
</body>
</html>
