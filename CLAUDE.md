# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Blueprint is a visual node-based connection game inspired by Unreal Engine's Blueprint system. Players drag and connect different types of nodes to build valid data flow graphs. The game features multiple modes including puzzle, tetris, and infinite construction modes.

## Core Architecture

### Main Files Structure
- `index.html` - Main game interface with dual canvas setup (temporary area + blueprint area)
- `game.js` - Core game logic (~400KB, contains all game systems)
- `styles.css` - Modern UI styling with glass morphism effects
- `server.js` - Simple HTTP server for local development

### Game System Architecture

The game uses a dual-canvas architecture:
- **Temporary Area Canvas** (`temp-canvas`): Left side, stores available nodes for dragging
- **Blueprint Area Canvas** (`game-canvas`): Right side, main construction area with start/end nodes

Core game state is managed in the `gameState` object containing:
- `temporaryNodes[]` - Nodes in the temporary area
- `placedNodes[]` - Nodes placed in the blueprint area
- `connections[]` - Connection relationships between ports
- Game modes: 'puzzle', 'tetris', 'infinite'

### Node and Port System

Nodes contain ports with different types and shapes:
- Port types: `['square', 'diamond', 'triangle', 'circle']`
- Port colors: `['#ff5252', '#2196F3', '#4CAF50', '#FFC107']`
- Connection rules: Only same-type ports can connect, no cycles allowed (DAG validation)

### Algorithm Systems

The game implements several key algorithms:
- **DAG Validation**: Cycle detection using DFS
- **Path Finding**: BFS for route validation from start to end nodes  
- **Layered DAG Algorithm**: Advanced node generation and balancing
- **Port Balance Algorithm**: Ensures solvable puzzle generation

## Development Commands

### Running the Game
```bash
# Start local development server (Windows)
run_game.bat

# Manual Python server start
python -m http.server 8000

# Using Node.js HTTP server  
node server.js
```

The game runs on `http://localhost:8000` by default.

### Testing

The project has extensive testing with multiple test files:

**Core Algorithm Tests:**
- `test_layered_dag_algorithm.html` - Tests the main DAG generation algorithm
- `test_deterministic_algorithm.html` - Validates deterministic behavior
- `test_improved_port_balance.html` - Tests port balancing algorithms

**Integration Tests:**
- `test_complete_game_system.html` - Full game system validation
- `test_actual_implementation.html` - Tests actual game implementation
- `test_multi_solution.html` - Tests multiple solution scenarios

**Feature Tests:**
- `test_drag_drop.html` & `test_drag_fixes.html` - Drag and drop functionality
- `test_infinite_mode.js` - Infinite mode testing
- `test_timer_fix.html` - Timer system validation

**Running Tests:**
```bash
# Open any test file in browser after starting server
http://localhost:8000/test_complete_game_system.html

# For Node.js specific tests
node test_algorithm_node.js
node test_solvability.js
```

### Code Structure

**Main Game Classes:**
- `Node` class - Base node with position, ports, and type
- `Port` class - Connection endpoints with type validation
- `Connection` class - Manages relationships between ports

**Key Game Functions:**
- `generateNewNode()` - Creates nodes based on current algorithm
- `validateDAG()` - Checks for cycles and validates graph structure
- `findPath()` - BFS pathfinding from start to end nodes
- `checkSolvability()` - Determines if current state is solvable

## Game Modes

1. **Puzzle Mode** - Default mode with predefined node sets
2. **Tetris Mode** - Time-limited with node queue system
3. **Infinite Mode** - Wave-based construction with adaptive difficulty

## Key Features to Understand

- **Port Type Matching**: Only identical port types/colors can connect
- **DAG Enforcement**: No cycles allowed in the connection graph
- **Multi-area Design**: Temporary storage vs. construction area
- **Real-time Validation**: Continuous checking of graph validity
- **Visual Flow Animation**: Data flow visualization through connected paths

## Important Keyboard Shortcuts

- `Enter` - Validate blueprint
- `Space` - Play flow animation  
- `C` - Clear blueprint area
- `R` - Regenerate nodes
- `H` - Show hint/solution
- `X` - Toggle crossing restrictions
- `V` - Show crossing points
- `Delete` - Remove selected node

## File Naming Conventions

- `test_*.html` - Browser-based test files
- `test_*.js` - Node.js test files  
- `algorithm_*.js` - Algorithm implementation files
- `*_test.html` - Alternative test file naming
- Report files use ALL_CAPS with underscores (e.g., `FINAL_IMPLEMENTATION_REPORT.md`)