<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Fix Test</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            margin: 0;
        }
        .status {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
            font-size: 18px;
        }
        .output {
            background: #000;
            border: 1px solid #333;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            height: 500px;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .pass { color: #00ff00; }
        .fail { color: #ff0000; }
        .warn { color: #ffaa00; }
    </style>
</head>
<body>
    <h1>🔧 Quick Fix Verification Test</h1>
    
    <div class="status">
        <p><strong>Status:</strong> <span id="status">Testing fixes...</span></p>
    </div>

    <div class="output" id="output"></div>

    <script>
        // Load the main game script
        const script = document.createElement('script');
        script.src = 'game.js?v=' + Date.now();
        script.onload = function() {
            console.log('[QUICK-FIX-TEST] Game script loaded, testing fixes...');
            setTimeout(testFixes, 1000);
        };
        script.onerror = function() {
            console.error('[QUICK-FIX-TEST] Failed to load game script');
            document.getElementById('status').innerHTML = '<span class="fail">Failed to load game script</span>';
        };
        document.head.appendChild(script);

        // Console capture
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const output = document.getElementById('output');
        
        function appendOutput(message, type = 'log') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            let className = '';
            
            if (type === 'error' || message.includes('ERROR') || message.includes('FAIL')) {
                className = 'fail';
            } else if (message.includes('PASS') || message.includes('SUCCESS') || message.includes('✅')) {
                className = 'pass';
            } else if (message.includes('WARN') || message.includes('⚠️')) {
                className = 'warn';
            }
            
            const formattedMessage = `<span class="${className}">${timestamp} ${prefix} ${message}</span>\n`;
            output.innerHTML += formattedMessage;
            output.scrollTop = output.scrollHeight;
        }
        
        console.log = function(...args) {
            appendOutput(args.join(' '), 'log');
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            appendOutput(args.join(' '), 'error');
            originalConsoleError.apply(console, args);
        };

        async function testFixes() {
            try {
                console.log('[QUICK-FIX-TEST] ========== TESTING CRITICAL FIXES ==========');
                
                let allTestsPassed = true;
                
                // Test 1: Simple Flow-First Algorithm Test
                console.log('[QUICK-FIX-TEST] === TEST 1: SIMPLE FLOW-FIRST ALGORITHM ===');
                try {
                    const simpleRequirements = {
                        unconnectedOutputs: [{ type: 'square', color: '#ff5252', nodeId: 'start1', depth: 0 }],
                        unconnectedInputs: [{ type: 'square', color: '#ff5252', nodeId: 'end1', depth: 999 }]
                    };
                    
                    const result = generateNodesForRequirements(simpleRequirements, 1, 1);
                    
                    if (result && result.length > 0) {
                        console.log('[QUICK-FIX-TEST] ✅ Test 1 PASSED: Generated', result.length, 'nodes');
                    } else {
                        console.log('[QUICK-FIX-TEST] ❌ Test 1 FAILED: No nodes generated');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[QUICK-FIX-TEST] ❌ Test 1 ERROR:', error.message);
                    allTestsPassed = false;
                }
                
                // Test 2: Mismatched Types (Transformation Test)
                console.log('[QUICK-FIX-TEST] === TEST 2: TRANSFORMATION TEST ===');
                try {
                    const transformRequirements = {
                        unconnectedOutputs: [{ type: 'square', color: '#ff5252', nodeId: 'start1', depth: 0 }],
                        unconnectedInputs: [{ type: 'circle', color: '#2196F3', nodeId: 'end1', depth: 999 }]
                    };
                    
                    const result = generateNodesForRequirements(transformRequirements, 2, 2);
                    
                    if (result && result.length > 0) {
                        console.log('[QUICK-FIX-TEST] ✅ Test 2 PASSED: Generated', result.length, 'nodes with transformation');
                    } else {
                        console.log('[QUICK-FIX-TEST] ❌ Test 2 FAILED: No nodes generated');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[QUICK-FIX-TEST] ❌ Test 2 ERROR:', error.message);
                    allTestsPassed = false;
                }
                
                // Test 3: Multiple Sources and Sinks
                console.log('[QUICK-FIX-TEST] === TEST 3: MULTIPLE SOURCES/SINKS TEST ===');
                try {
                    const multipleRequirements = {
                        unconnectedOutputs: [
                            { type: 'square', color: '#ff5252', nodeId: 'start1', depth: 0 },
                            { type: 'circle', color: '#2196F3', nodeId: 'start2', depth: 0 }
                        ],
                        unconnectedInputs: [
                            { type: 'square', color: '#ff5252', nodeId: 'end1', depth: 999 },
                            { type: 'triangle', color: '#4CAF50', nodeId: 'end2', depth: 999 }
                        ]
                    };
                    
                    const result = generateNodesForRequirements(multipleRequirements, 3, 3);
                    
                    if (result && result.length > 0) {
                        console.log('[QUICK-FIX-TEST] ✅ Test 3 PASSED: Generated', result.length, 'nodes with multiple flows');
                    } else {
                        console.log('[QUICK-FIX-TEST] ❌ Test 3 FAILED: No nodes generated');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[QUICK-FIX-TEST] ❌ Test 3 ERROR:', error.message);
                    allTestsPassed = false;
                }
                
                // Test 4: Edge Case - Empty Requirements
                console.log('[QUICK-FIX-TEST] === TEST 4: EDGE CASE - EMPTY REQUIREMENTS ===');
                try {
                    const result = generateNodesForRequirements({}, 1, 1);
                    
                    if (result && result.length > 0) {
                        console.log('[QUICK-FIX-TEST] ✅ Test 4 PASSED: Handled empty requirements, generated', result.length, 'nodes');
                    } else {
                        console.log('[QUICK-FIX-TEST] ❌ Test 4 FAILED: Could not handle empty requirements');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[QUICK-FIX-TEST] ❌ Test 4 ERROR:', error.message);
                    allTestsPassed = false;
                }
                
                // Test 5: Edge Case - Null Requirements
                console.log('[QUICK-FIX-TEST] === TEST 5: EDGE CASE - NULL REQUIREMENTS ===');
                try {
                    const result = generateNodesForRequirements(null, 1, 1);
                    
                    if (result && result.length > 0) {
                        console.log('[QUICK-FIX-TEST] ✅ Test 5 PASSED: Handled null requirements, generated', result.length, 'nodes');
                    } else {
                        console.log('[QUICK-FIX-TEST] ❌ Test 5 FAILED: Could not handle null requirements');
                        allTestsPassed = false;
                    }
                } catch (error) {
                    console.error('[QUICK-FIX-TEST] ❌ Test 5 ERROR:', error.message);
                    allTestsPassed = false;
                }
                
                // Final Assessment
                console.log('[QUICK-FIX-TEST] ========== FINAL ASSESSMENT ==========');
                if (allTestsPassed) {
                    console.log('[QUICK-FIX-TEST] 🎉 ALL TESTS PASSED! Critical fixes are working correctly.');
                    document.getElementById('status').innerHTML = '<span class="pass">All fixes working correctly! ✅</span>';
                } else {
                    console.log('[QUICK-FIX-TEST] ⚠️ Some tests failed. Further fixes may be needed.');
                    document.getElementById('status').innerHTML = '<span class="warn">Some tests failed - needs more work</span>';
                }
                
                console.log('[QUICK-FIX-TEST] === READY FOR COMPREHENSIVE TESTING ===');
                console.log('[QUICK-FIX-TEST] The critical errors have been addressed. You can now run:');
                console.log('[QUICK-FIX-TEST] - window.testFlowFirstAlgorithm()');
                console.log('[QUICK-FIX-TEST] - window.testSolvabilityGuarantee()');
                console.log('[QUICK-FIX-TEST] - window.runMode3Test()');
                
            } catch (error) {
                console.error('[QUICK-FIX-TEST] Test execution failed:', error);
                document.getElementById('status').innerHTML = '<span class="fail">Test execution failed</span>';
            }
        }

        // Initialize
        console.log('[QUICK-FIX-TEST] Quick fix verification test framework initialized');
    </script>
</body>
</html>
