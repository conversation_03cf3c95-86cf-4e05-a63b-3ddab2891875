<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hierarchical Algorithm - Complete Validation</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: 'Courier New', monospace;
            background: #1a1a1a;
            color: #00ff00;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
            border: 2px solid #00ff00;
        }
        
        .test-section {
            background: #2a2a2a;
            border: 2px solid #4CAF50;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .controls {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-family: 'Courier New', monospace;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #45a049;
        }
        
        .btn.test {
            background: #2196F3;
        }
        
        .btn.test:hover {
            background: #1976D2;
        }
        
        .btn.danger {
            background: #f44336;
        }
        
        .btn.danger:hover {
            background: #d32f2f;
        }
        
        .log {
            background: #000;
            border: 1px solid #333;
            border-radius: 5px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-size: 12px;
            white-space: pre-wrap;
            margin: 20px 0;
        }
        
        .log-success { color: #00ff00; }
        .log-error { color: #ff0000; }
        .log-warn { color: #ffaa00; }
        .log-info { color: #00aaff; }
        
        .test-result {
            background: #1a3a1a;
            border: 1px solid #4CAF50;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        
        .test-result.failed {
            background: #3a1a1a;
            border-color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Complete Hierarchical Algorithm Test</h1>
            <p>全面验证分层补偿算法的所有功能</p>
        </div>
        
        <div class="controls">
            <button class="btn test" onclick="runCompleteTest()">🚀 Run Complete Test</button>
            <button class="btn" onclick="testBasicFunctions()">🔧 Test Basic Functions</button>
            <button class="btn" onclick="testCompensation()">⚖️ Test Compensation</button>
            <button class="btn" onclick="testValidation()">✅ Test Validation</button>
            <button class="btn" onclick="testGameLoop()">🎮 Test Game Loop</button>
            <button class="btn danger" onclick="clearLog()">🗑️ Clear Log</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="test-results">
                Ready to run tests...
            </div>
        </div>
        
        <div>
            <h3>📋 Test Log</h3>
            <div class="log" id="test-log"></div>
        </div>
    </div>

    <!-- Load the hierarchical compensation algorithm -->
    <script src="compensation_algorithm.js"></script>
    
    <script>
        let algorithm = null;
        let testResults = [];
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            algorithm = new HierarchicalCompensationAlgorithm();
            logTest('Hierarchical Compensation Algorithm Test initialized', 'success');
        });
        
        // Logging system
        function logTest(message, type = 'info') {
            const timestamp = new Date().toISOString().substr(11, 12);
            const logElement = document.getElementById('test-log');
            const className = `log-${type}`;
            const formattedMessage = `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logElement.innerHTML += formattedMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[ALGORITHM-TEST] ${message}`);
        }
        
        // Run complete test suite
        function runCompleteTest() {
            logTest('=== STARTING COMPLETE TEST SUITE ===', 'info');
            testResults = [];
            
            // Test 1: Basic Functions
            testBasicFunctions();
            
            // Test 2: Compensation
            setTimeout(() => testCompensation(), 1000);
            
            // Test 3: Validation
            setTimeout(() => testValidation(), 2000);
            
            // Test 4: Game Loop
            setTimeout(() => testGameLoop(), 3000);
            
            // Final Summary
            setTimeout(() => showFinalResults(), 4000);
        }
        
        // Test basic functions
        function testBasicFunctions() {
            logTest('--- Testing Basic Functions ---', 'info');
            
            try {
                // Test 1: Algorithm initialization
                addTestResult('Algorithm Initialization', algorithm !== null, 'Algorithm instance created');
                
                // Test 2: Port types
                addTestResult('Port Types', algorithm.portTypes.length === 4, `Found ${algorithm.portTypes.length} port types`);
                
                // Test 3: Node creation
                const sourceNode = algorithm.createSourceNode('square-red', 2);
                addTestResult('Source Node Creation', sourceNode && sourceNode.outputPorts.length === 2, 'Source node created with correct ports');
                
                const sinkNode = algorithm.createSinkNode('circle-blue', 1, 2);
                addTestResult('Sink Node Creation', sinkNode && sinkNode.inputPorts.length === 1, 'Sink node created with correct ports');
                
                // Test 4: Level management
                const level = algorithm.getNodeLevel(sourceNode);
                addTestResult('Level Management', level === 0, `Source node at level ${level}`);
                
                logTest('✅ Basic Functions Test Completed', 'success');
                
            } catch (error) {
                logTest(`❌ Basic Functions Test Failed: ${error.message}`, 'error');
                addTestResult('Basic Functions', false, error.message);
            }
        }
        
        // Test compensation
        function testCompensation() {
            logTest('--- Testing Compensation ---', 'info');
            
            try {
                // Create test scenario with imbalance
                const testNodes = [
                    {
                        id: 'test_source',
                        type: 'start',
                        level: 0,
                        depth: 0,
                        inputPorts: [],
                        outputPorts: [
                            { id: 'out1', type: 'square', color: 'red', side: 'output', nodeId: 'test_source', portTypeKey: 'square-red' },
                            { id: 'out2', type: 'square', color: 'red', side: 'output', nodeId: 'test_source', portTypeKey: 'square-red' }
                        ]
                    },
                    {
                        id: 'test_sink',
                        type: 'end',
                        level: 2,
                        depth: 200,
                        inputPorts: [
                            { id: 'in1', type: 'square', color: 'red', side: 'input', nodeId: 'test_sink', portTypeKey: 'square-red' }
                        ],
                        outputPorts: []
                    }
                ];
                
                // Test 1: Port balance calculation
                const balance = algorithm.calculatePortImbalance(testNodes);
                const squareRedBalance = balance.get('square-red');
                addTestResult('Port Balance Calculation', squareRedBalance && squareRedBalance.delta === 1, `Square-red delta: ${squareRedBalance?.delta}`);
                
                // Test 2: Compensation
                const compensationNodes = algorithm.compensate(testNodes);
                addTestResult('Compensation Generation', compensationNodes.length > 0, `Generated ${compensationNodes.length} compensation nodes`);
                
                // Test 3: Balance after compensation
                const allNodes = [...testNodes, ...compensationNodes];
                const finalBalance = algorithm.calculatePortImbalance(allNodes);
                const finalSquareRed = finalBalance.get('square-red');
                addTestResult('Balance After Compensation', finalSquareRed && finalSquareRed.delta === 0, `Final square-red delta: ${finalSquareRed?.delta}`);
                
                logTest('✅ Compensation Test Completed', 'success');
                
            } catch (error) {
                logTest(`❌ Compensation Test Failed: ${error.message}`, 'error');
                addTestResult('Compensation', false, error.message);
            }
        }
        
        // Test validation
        function testValidation() {
            logTest('--- Testing Validation ---', 'info');
            
            try {
                // Create balanced test scenario
                const balancedNodes = [
                    {
                        id: 'balanced_source',
                        type: 'start',
                        level: 0,
                        depth: 0,
                        inputPorts: [],
                        outputPorts: [
                            { id: 'out1', type: 'square', color: 'red', side: 'output', nodeId: 'balanced_source', portTypeKey: 'square-red' }
                        ]
                    },
                    {
                        id: 'balanced_sink',
                        type: 'end',
                        level: 2,
                        depth: 200,
                        inputPorts: [
                            { id: 'in1', type: 'square', color: 'red', side: 'input', nodeId: 'balanced_sink', portTypeKey: 'square-red' }
                        ],
                        outputPorts: []
                    }
                ];
                
                // Test 1: Port balance validation
                const balanceValid = algorithm.validatePortBalance(balancedNodes);
                addTestResult('Port Balance Validation', balanceValid, 'Balanced nodes pass validation');
                
                // Test 2: Global topology validation
                const topologyValid = algorithm.validateGlobalTopologicalOrder(balancedNodes);
                addTestResult('Global Topology Validation', topologyValid, 'Topology validation passed');
                
                // Test 3: Strong solvability
                const solvabilityValid = algorithm.validateStrongSolvability(balancedNodes, []);
                addTestResult('Strong Solvability', solvabilityValid, 'Strong solvability validation passed');
                
                logTest('✅ Validation Test Completed', 'success');
                
            } catch (error) {
                logTest(`❌ Validation Test Failed: ${error.message}`, 'error');
                addTestResult('Validation', false, error.message);
            }
        }
        
        // Test game loop
        function testGameLoop() {
            logTest('--- Testing Game Loop ---', 'info');
            
            try {
                // Test 1: Difficulty adjustment
                algorithm.adjustDifficultyForWave(5);
                addTestResult('Difficulty Adjustment', algorithm.difficultyConfig.maxPortTypes === 2, `Wave 5: ${algorithm.difficultyConfig.maxPortTypes} port types`);
                
                // Test 2: Modification generation
                const testNodes = [
                    {
                        id: 'game_source',
                        type: 'start',
                        level: 0,
                        depth: 0,
                        inputPorts: [],
                        outputPorts: [
                            { id: 'out1', type: 'square', color: 'red', side: 'output', nodeId: 'game_source', portTypeKey: 'square-red' }
                        ]
                    }
                ];
                
                const modifications = [
                    { type: 'add_node', nodeType: 'intermediate', inputCount: 1, outputCount: 1 }
                ];
                
                // Test 3: Generate next round
                const newNodes = algorithm.generateNextRound(testNodes, modifications, { wave: 5 }, null);
                addTestResult('Generate Next Round', newNodes.length > 0, `Generated ${newNodes.length} new nodes`);
                
                // Test 4: Backward compatibility
                const oldBalance = algorithm.calculatePortBalance(testNodes);
                addTestResult('Backward Compatibility', oldBalance instanceof Map, 'Old method compatibility maintained');
                
                logTest('✅ Game Loop Test Completed', 'success');
                
            } catch (error) {
                logTest(`❌ Game Loop Test Failed: ${error.message}`, 'error');
                addTestResult('Game Loop', false, error.message);
            }
        }
        
        // Add test result
        function addTestResult(testName, passed, message) {
            testResults.push({ name: testName, passed: passed, message: message });
            
            const icon = passed ? '✅' : '❌';
            const type = passed ? 'success' : 'error';
            logTest(`${icon} ${testName}: ${message}`, type);
        }
        
        // Show final results
        function showFinalResults() {
            logTest('=== FINAL TEST RESULTS ===', 'info');
            
            const passedTests = testResults.filter(r => r.passed).length;
            const totalTests = testResults.length;
            const passRate = ((passedTests / totalTests) * 100).toFixed(1);
            
            logTest(`Tests Passed: ${passedTests}/${totalTests} (${passRate}%)`, passedTests === totalTests ? 'success' : 'warn');
            
            // Update results display
            const resultsElement = document.getElementById('test-results');
            const resultHtml = testResults.map(result => {
                const className = result.passed ? 'test-result' : 'test-result failed';
                const icon = result.passed ? '✅' : '❌';
                return `<div class="${className}">${icon} <strong>${result.name}:</strong> ${result.message}</div>`;
            }).join('');
            
            resultsElement.innerHTML = resultHtml + 
                `<div class="test-result" style="margin-top: 20px; text-align: center; font-size: 1.2em;">
                    <strong>Overall Result: ${passedTests}/${totalTests} Tests Passed (${passRate}%)</strong>
                </div>`;
            
            if (passedTests === totalTests) {
                logTest('🎉 ALL TESTS PASSED! Algorithm is working correctly.', 'success');
            } else {
                logTest('⚠️ Some tests failed. Please check the implementation.', 'warn');
            }
        }
        
        // Clear log
        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
            document.getElementById('test-results').innerHTML = 'Ready to run tests...';
            testResults = [];
            logTest('Test log cleared', 'info');
        }
    </script>
</body>
</html>
