// ========== 蓝图连接游戏 - 核心逻辑 (精简版) ==========

// 核心游戏状态
const gameState = {
    day: 1,
    selectedTool: 'select',
    mode: 'infinite', // 专注于模式3
    
    // 节点和连接
    temporaryNodes: [],
    placedNodes: [],
    connections: [],
    
    // 端口类型定义
    portTypes: ['square', 'diamond', 'triangle', 'circle'],
    portColors: ['#ff5252', '#2196F3', '#4CAF50', '#FFC107'],
    
    // 游戏区域
    gridSize: 20,
    temporaryArea: { x: 0, y: 0, width: 300, height: 600, canvas: null, ctx: null, maxNodes: 6 },
    placementArea: { x: 0, y: 0, width: 700, height: 600, canvas: null, ctx: null },
    
    // 交互状态
    draggingNode: null,
    selectedNode: null,
    selectedPort: null,
    creatingConnection: null,
    hoveredPort: null,
    connectingFrom: null, // 正在连接的起始端口
    mousePosition: { x: 0, y: 0 }, // 鼠标位置
    
    // 模式3专用状态
    infiniteMode: {
        isActive: false,
        wave: 0,
        difficulty: 1,
        currentBlueprint: null,
        solvabilityGuaranteed: false,
        lastModificationTime: null,
        continuousValidation: true
    }
};

// ========== SYSTEM.md 数学约束验证系统 ==========

class SystemMdConstraintValidator {
    constructor() {
        this.constraints = {
            portTypeBalance: true,
            dagTopology: true,
            flowConservation: true,
            portMapping: true
        };
    }

    // 验证所有SYSTEM.md约束
    validateAllConstraints(nodePool) {
        const validation = {
            portTypeBalance: this.validatePortTypeBalance(nodePool),
            dagTopology: this.validateDAGTopology(nodePool),
            flowConservation: this.validateFlowConservation(nodePool),
            portMapping: this.validatePortMapping(nodePool),
            allSatisfied: false
        };
        
        validation.allSatisfied = validation.portTypeBalance.satisfied &&
                                  validation.dagTopology.satisfied &&
                                  validation.flowConservation.satisfied &&
                                  validation.portMapping.satisfied;
        
        return validation;
    }

    // 约束1: 端口类型平衡 - ∀τ ∈ Γ: |Output_τ| = |Input_τ|
    validatePortTypeBalance(nodePool) {
        const typeCounts = new Map();
        
        nodePool.forEach(node => {
            const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
            allPorts.forEach(port => {
                const key = `${port.type}-${port.color}`;
                if (!typeCounts.has(key)) {
                    typeCounts.set(key, { input: 0, output: 0 });
                }
                
                if (port.side === 'input' || node.inputPorts.includes(port)) {
                    typeCounts.get(key).input++;
                } else {
                    typeCounts.get(key).output++;
                }
            });
        });
        
        let balanced = true;
        const imbalances = [];
        
        typeCounts.forEach((counts, type) => {
            if (counts.input !== counts.output) {
                balanced = false;
                imbalances.push({
                    type,
                    input: counts.input,
                    output: counts.output,
                    difference: counts.output - counts.input
                });
            }
        });
        
        return { satisfied: balanced, typeCounts: Object.fromEntries(typeCounts), imbalances };
    }

    // 约束2: DAG拓扑 - ∀(u,v) ∈ E: depth(u) < depth(v)
    validateDAGTopology(nodePool) {
        const depthViolations = [];
        let hasValidDepths = true;
        
        const startNodes = nodePool.filter(n => n.type === 'start');
        const endNodes = nodePool.filter(n => n.type === 'end');
        
        startNodes.forEach(node => {
            if (node.depth !== 0) {
                hasValidDepths = false;
                depthViolations.push(`Start node ${node.id} has depth ${node.depth}, expected 0`);
            }
        });
        
        const maxDepth = Math.max(...nodePool.map(n => n.depth || 0));
        endNodes.forEach(node => {
            if (node.depth !== maxDepth) {
                hasValidDepths = false;
                depthViolations.push(`End node ${node.id} has depth ${node.depth}, expected ${maxDepth}`);
            }
        });
        
        return { satisfied: hasValidDepths, maxDepth, violations: depthViolations };
    }

    // 约束3: 流守恒 - ∀v ∈ V: Σ(in_ports) = Σ(out_ports)
    validateFlowConservation(nodePool) {
        const violations = [];
        let conserved = true;
        
        nodePool.forEach(node => {
            const inputCount = (node.inputPorts || []).length;
            const outputCount = (node.outputPorts || []).length;
            
            if (node.type === 'start' && inputCount > 0) {
                conserved = false;
                violations.push(`Start node ${node.id} has ${inputCount} input ports, expected 0`);
            }
            
            if (node.type === 'end' && outputCount > 0) {
                conserved = false;
                violations.push(`End node ${node.id} has ${outputCount} output ports, expected 0`);
            }
            
            if (node.type === 'intermediate' && (inputCount === 0 || outputCount === 0)) {
                conserved = false;
                violations.push(`Intermediate node ${node.id} has ${inputCount} inputs and ${outputCount} outputs, both should be > 0`);
            }
        });
        
        return { satisfied: conserved, violations };
    }

    // 约束4: 端口映射 - ∃Φ: Output_ports → Input_ports (bijective)
    validatePortMapping(nodePool) {
        const outputPorts = [];
        const inputPorts = [];
        
        nodePool.forEach(node => {
            if (node.outputPorts) outputPorts.push(...node.outputPorts);
            if (node.inputPorts) inputPorts.push(...node.inputPorts);
        });
        
        const outputByType = new Map();
        const inputByType = new Map();
        
        outputPorts.forEach(port => {
            const key = `${port.type}-${port.color}`;
            if (!outputByType.has(key)) outputByType.set(key, []);
            outputByType.get(key).push(port);
        });
        
        inputPorts.forEach(port => {
            const key = `${port.type}-${port.color}`;
            if (!inputByType.has(key)) inputByType.set(key, []);
            inputByType.get(key).push(port);
        });
        
        let mappingExists = true;
        const mappingIssues = [];
        
        outputByType.forEach((outputs, type) => {
            const inputs = inputByType.get(type) || [];
            if (outputs.length !== inputs.length) {
                mappingExists = false;
                mappingIssues.push(`Type ${type}: ${outputs.length} outputs, ${inputs.length} inputs`);
            }
        });
        
        return {
            satisfied: mappingExists,
            outputCount: outputPorts.length,
            inputCount: inputPorts.length,
            issues: mappingIssues
        };
    }
}

// ========== 路径骨架生成器 ==========

class PathSkeletonGenerator {
    constructor() {
        this.validator = new SystemMdConstraintValidator();
    }

    // 生成路径骨架 - 保证每个节点至少在一条从起点到终点的路径上
    generatePathSkeleton(nodePool) {
        console.log('[PATH-SKELETON] Generating path skeleton for', nodePool.length, 'nodes');
        
        const startNodes = nodePool.filter(n => n.type === 'start');
        const endNodes = nodePool.filter(n => n.type === 'end');
        const intermediateNodes = nodePool.filter(n => n.type === 'intermediate');
        
        const paths = [];
        const coveredNodes = new Set();
        
        // 为每个起点-终点对生成路径
        startNodes.forEach(startNode => {
            endNodes.forEach(endNode => {
                const path = this.generateSinglePath(startNode, endNode, intermediateNodes, coveredNodes);
                if (path.length > 0) {
                    paths.push(path);
                    path.forEach(nodeId => coveredNodes.add(nodeId));
                }
            });
        });
        
        // 确保所有节点都被覆盖
        const uncoveredNodes = intermediateNodes.filter(node => !coveredNodes.has(node.id));
        if (uncoveredNodes.length > 0) {
            const additionalPaths = this.createAdditionalPaths(startNodes, endNodes, uncoveredNodes);
            paths.push(...additionalPaths);
        }
        
        console.log('[PATH-SKELETON] Generated', paths.length, 'paths covering all nodes');
        return { paths, coveredNodes: Array.from(coveredNodes) };
    }

    generateSinglePath(startNode, endNode, intermediateNodes, alreadyCovered) {
        const path = [startNode.id];
        
        // 贪心选择未覆盖的中间节点
        const availableNodes = intermediateNodes.filter(node => !alreadyCovered.has(node.id));
        
        // 按深度排序选择中间节点
        const sortedNodes = availableNodes.sort((a, b) => (a.depth || 0) - (b.depth || 0));
        
        // 选择1-3个中间节点形成路径
        const pathLength = Math.min(3, Math.max(1, sortedNodes.length));
        for (let i = 0; i < pathLength && i < sortedNodes.length; i++) {
            path.push(sortedNodes[i].id);
        }
        
        path.push(endNode.id);
        return path;
    }

    createAdditionalPaths(startNodes, endNodes, uncoveredNodes) {
        const additionalPaths = [];
        
        uncoveredNodes.forEach(uncoveredNode => {
            const startNode = startNodes[Math.floor(Math.random() * startNodes.length)];
            const endNode = endNodes[Math.floor(Math.random() * endNodes.length)];
            
            const path = [startNode.id, uncoveredNode.id, endNode.id];
            additionalPaths.push(path);
        });
        
        return additionalPaths;
    }
}

// ========== 端口分配器 ==========

class PortAllocator {
    constructor() {
        this.validator = new SystemMdConstraintValidator();
    }

    // 基于路径骨架分配端口
    allocatePortsBasedOnSkeleton(nodePool, pathSkeleton) {
        console.log('[PORT-ALLOCATOR] Allocating ports based on path skeleton');
        
        // 计算每个节点的连接需求
        const connectionRequirements = this.calculateConnectionRequirements(nodePool, pathSkeleton);
        
        // 分配端口确保类型平衡
        this.allocateBalancedPorts(nodePool, connectionRequirements);
        
        // 验证分配结果
        const validation = this.validator.validateAllConstraints(nodePool);
        console.log('[PORT-ALLOCATOR] Port allocation validation:', validation.allSatisfied);
        
        return validation;
    }

    calculateConnectionRequirements(nodePool, pathSkeleton) {
        const requirements = new Map();
        
        // 初始化所有节点的需求
        nodePool.forEach(node => {
            requirements.set(node.id, {
                inputConnections: 0,
                outputConnections: 0,
                requiredTypes: new Set()
            });
        });
        
        // 基于路径计算连接需求
        pathSkeleton.paths.forEach(path => {
            for (let i = 0; i < path.length - 1; i++) {
                const fromNodeId = path[i];
                const toNodeId = path[i + 1];
                
                const fromReq = requirements.get(fromNodeId);
                const toReq = requirements.get(toNodeId);
                
                if (fromReq) fromReq.outputConnections++;
                if (toReq) toReq.inputConnections++;
            }
        });
        
        return requirements;
    }

    allocateBalancedPorts(nodePool, requirements) {
        const portTypes = gameState.portTypes;
        const portColors = gameState.portColors;
        
        // 为每个节点分配端口
        nodePool.forEach(node => {
            const req = requirements.get(node.id);
            if (!req) return;
            
            // 清空现有端口
            node.inputPorts = [];
            node.outputPorts = [];
            
            // 分配输入端口
            for (let i = 0; i < req.inputConnections; i++) {
                const portType = portTypes[i % portTypes.length];
                const portColor = portColors[i % portColors.length];
                
                node.inputPorts.push({
                    id: `${node.id}_in_${i}`,
                    type: portType,
                    color: portColor,
                    side: 'input',
                    nodeId: node.id
                });
            }
            
            // 分配输出端口
            for (let i = 0; i < req.outputConnections; i++) {
                const portType = portTypes[i % portTypes.length];
                const portColor = portColors[i % portColors.length];
                
                node.outputPorts.push({
                    id: `${node.id}_out_${i}`,
                    type: portType,
                    color: portColor,
                    side: 'output',
                    nodeId: node.id
                });
            }
        });
    }
}

// ========== 平衡调整器 ==========

class PortBalancer {
    constructor() {
        this.validator = new SystemMdConstraintValidator();
    }

    // 平衡端口数量确保SYSTEM.md约束
    balancePortCounts(nodePool) {
        console.log('[PORT-BALANCER] Balancing port counts for SYSTEM.md compliance');
        
        let iteration = 0;
        const maxIterations = 10;
        
        while (iteration < maxIterations) {
            const validation = this.validator.validateAllConstraints(nodePool);
            
            if (validation.allSatisfied) {
                console.log('[PORT-BALANCER] All constraints satisfied after', iteration, 'iterations');
                return { success: true, iterations: iteration };
            }
            
            // 修复端口类型不平衡
            if (!validation.portTypeBalance.satisfied) {
                this.fixPortTypeImbalances(nodePool, validation.portTypeBalance.imbalances);
            }
            
            iteration++;
        }
        
        console.log('[PORT-BALANCER] Failed to balance after', maxIterations, 'iterations');
        return { success: false, iterations: maxIterations };
    }

    fixPortTypeImbalances(nodePool, imbalances) {
        imbalances.forEach(imbalance => {
            const [shape, color] = imbalance.type.split('-');
            
            if (imbalance.difference > 0) {
                // 太多输出端口，添加输入端口
                this.addInputPorts(nodePool, shape, color, imbalance.difference);
            } else {
                // 太多输入端口，添加输出端口
                this.addOutputPorts(nodePool, shape, color, -imbalance.difference);
            }
        });
    }

    addInputPorts(nodePool, shape, color, count) {
        const eligibleNodes = nodePool.filter(n => n.type === 'intermediate' || n.type === 'end');
        
        for (let i = 0; i < count; i++) {
            const node = eligibleNodes[i % eligibleNodes.length];
            if (!node.inputPorts) node.inputPorts = [];
            
            node.inputPorts.push({
                id: `${node.id}_in_balance_${node.inputPorts.length}`,
                type: shape,
                color: color,
                side: 'input',
                nodeId: node.id
            });
        }
    }

    addOutputPorts(nodePool, shape, color, count) {
        const eligibleNodes = nodePool.filter(n => n.type === 'start' || n.type === 'intermediate');
        
        for (let i = 0; i < count; i++) {
            const node = eligibleNodes[i % eligibleNodes.length];
            if (!node.outputPorts) node.outputPorts = [];
            
            node.outputPorts.push({
                id: `${node.id}_out_balance_${node.outputPorts.length}`,
                type: shape,
                color: color,
                side: 'output',
                nodeId: node.id
            });
        }
    }
}

// 全局实例
const systemMdValidator = new SystemMdConstraintValidator();
const pathSkeletonGenerator = new PathSkeletonGenerator();
const portAllocator = new PortAllocator();
const portBalancer = new PortBalancer();

// ========== 模式3：强可解性增量修改算法 ==========

class Mode3IncrementalSolvabilityEngine {
    constructor() {
        this.validator = new SystemMdConstraintValidator();
        this.pathGenerator = new PathSkeletonGenerator();
        this.portAllocator = new PortAllocator();
        this.portBalancer = new PortBalancer();
    }

    // 模式3核心算法：保持强可解性的增量修改
    maintainStrongSolvabilityDuringModification(currentGraph, modification) {
        console.log('[MODE3] Maintaining strong solvability during modification');

        // 步骤1：快照当前状态
        const snapshot = this.createGraphSnapshot(currentGraph);

        // 步骤2：应用修改（暂不考虑平衡）
        const modifiedGraph = this.applyModifications(snapshot, modification);

        // 步骤3：分析破坏的平衡
        const imbalance = this.analyzeImbalance(modifiedGraph);

        // 步骤4：生成补偿节点/端口
        const compensation = this.generateCompensation(imbalance, modifiedGraph);

        // 步骤5：应用补偿并验证
        const finalGraph = this.applyCompensation(modifiedGraph, compensation);

        // 步骤6：确保所有新增部分可达
        this.ensureReachability(finalGraph, snapshot);

        return finalGraph;
    }

    createGraphSnapshot(graph) {
        return {
            nodes: JSON.parse(JSON.stringify(graph.nodes || [])),
            connections: JSON.parse(JSON.stringify(graph.connections || [])),
            portTypeBalance: this.calculatePortBalance(graph)
        };
    }

    calculatePortBalance(graph) {
        const balance = new Map();
        const nodes = graph.nodes || [];

        nodes.forEach(node => {
            const allPorts = [...(node.inputPorts || []), ...(node.outputPorts || [])];
            allPorts.forEach(port => {
                const key = `${port.type}-${port.color}`;
                if (!balance.has(key)) {
                    balance.set(key, { input: 0, output: 0 });
                }

                if (port.side === 'input' || node.inputPorts.includes(port)) {
                    balance.get(key).input++;
                } else {
                    balance.get(key).output++;
                }
            });
        });

        return balance;
    }

    applyModifications(snapshot, modification) {
        const modifiedGraph = {
            nodes: [...snapshot.nodes],
            connections: [...snapshot.connections]
        };

        // 应用节点添加
        if (modification.addNodes) {
            modifiedGraph.nodes.push(...modification.addNodes);
        }

        // 应用连接添加
        if (modification.addConnections) {
            modifiedGraph.connections.push(...modification.addConnections);
        }

        return modifiedGraph;
    }

    analyzeImbalance(graph) {
        const currentBalance = this.calculatePortBalance(graph);
        const imbalance = {
            portTypeDeficits: new Map(),
            unreachableNodes: new Set(),
            depthConflicts: []
        };

        // 分析端口类型不平衡
        currentBalance.forEach((counts, type) => {
            const inputDeficit = Math.max(0, counts.output - counts.input);
            const outputDeficit = Math.max(0, counts.input - counts.output);

            if (inputDeficit > 0 || outputDeficit > 0) {
                imbalance.portTypeDeficits.set(type, { inputDeficit, outputDeficit });
            }
        });

        // 分析不可达节点
        const reachableNodes = this.findReachableNodes(graph);
        graph.nodes.forEach(node => {
            if (!reachableNodes.has(node.id)) {
                imbalance.unreachableNodes.add(node.id);
            }
        });

        return imbalance;
    }

    findReachableNodes(graph) {
        const reachable = new Set();
        const startNodes = graph.nodes.filter(n => n.type === 'start');

        // 从起点开始DFS
        const visited = new Set();
        const stack = [...startNodes.map(n => n.id)];

        while (stack.length > 0) {
            const nodeId = stack.pop();
            if (visited.has(nodeId)) continue;

            visited.add(nodeId);
            reachable.add(nodeId);

            // 找到所有可达的下一层节点
            const connections = graph.connections.filter(conn => conn.fromNode === nodeId);
            connections.forEach(conn => {
                if (!visited.has(conn.toNode)) {
                    stack.push(conn.toNode);
                }
            });
        }

        return reachable;
    }

    generateCompensation(imbalance, graph) {
        const plan = {
            newNodes: [],
            portAdditions: [],
            depthAdjustments: []
        };

        // 处理端口类型不平衡
        imbalance.portTypeDeficits.forEach((deficit, portType) => {
            if (deficit.inputDeficit > 0) {
                plan.portAdditions.push(...this.distributeInputPorts(deficit.inputDeficit, portType, graph));
            }

            if (deficit.outputDeficit > 0) {
                plan.portAdditions.push(...this.distributeOutputPorts(deficit.outputDeficit, portType, graph));
            }
        });

        // 处理不可达节点
        if (imbalance.unreachableNodes.size > 0) {
            plan.newNodes.push(...this.createBridgeNodes(imbalance.unreachableNodes, graph));
        }

        return plan;
    }

    distributeInputPorts(count, portType, graph) {
        const [shape, color] = portType.split('-');
        const eligibleNodes = graph.nodes.filter(n => n.type === 'intermediate' || n.type === 'end');
        const additions = [];

        for (let i = 0; i < count; i++) {
            const node = eligibleNodes[i % eligibleNodes.length];
            additions.push({
                nodeId: node.id,
                portType: 'input',
                shape,
                color
            });
        }

        return additions;
    }

    distributeOutputPorts(count, portType, graph) {
        const [shape, color] = portType.split('-');
        const eligibleNodes = graph.nodes.filter(n => n.type === 'start' || n.type === 'intermediate');
        const additions = [];

        for (let i = 0; i < count; i++) {
            const node = eligibleNodes[i % eligibleNodes.length];
            additions.push({
                nodeId: node.id,
                portType: 'output',
                shape,
                color
            });
        }

        return additions;
    }

    createBridgeNodes(unreachableNodes, graph) {
        const bridgeNodes = [];
        const maxDepth = Math.max(...graph.nodes.map(n => n.depth || 0));

        unreachableNodes.forEach(nodeId => {
            const unreachableNode = graph.nodes.find(n => n.id === nodeId);
            if (!unreachableNode) return;

            // 创建桥接节点
            const bridgeNode = {
                id: `bridge_${nodeId}_${Date.now()}`,
                type: 'intermediate',
                depth: Math.max(1, (unreachableNode.depth || 0) - 1),
                inputPorts: [{
                    id: `bridge_${nodeId}_in`,
                    type: 'square',
                    color: '#ff5252',
                    side: 'input'
                }],
                outputPorts: [{
                    id: `bridge_${nodeId}_out`,
                    type: 'square',
                    color: '#ff5252',
                    side: 'output'
                }]
            };

            bridgeNodes.push(bridgeNode);
        });

        return bridgeNodes;
    }

    applyCompensation(graph, compensation) {
        const finalGraph = {
            nodes: [...graph.nodes],
            connections: [...graph.connections]
        };

        // 添加新节点
        finalGraph.nodes.push(...compensation.newNodes);

        // 添加端口
        compensation.portAdditions.forEach(addition => {
            const node = finalGraph.nodes.find(n => n.id === addition.nodeId);
            if (!node) return;

            const port = {
                id: `${node.id}_${addition.portType}_comp_${Date.now()}`,
                type: addition.shape,
                color: addition.color,
                side: addition.portType,
                nodeId: node.id
            };

            if (addition.portType === 'input') {
                if (!node.inputPorts) node.inputPorts = [];
                node.inputPorts.push(port);
            } else {
                if (!node.outputPorts) node.outputPorts = [];
                node.outputPorts.push(port);
            }
        });

        return finalGraph;
    }

    ensureReachability(graph, originalSnapshot) {
        // 确保所有新增节点都在有效路径上
        const pathSkeleton = this.pathGenerator.generatePathSkeleton(graph.nodes);

        // 验证路径覆盖
        const uncoveredNodes = graph.nodes.filter(node =>
            !pathSkeleton.coveredNodes.includes(node.id)
        );

        if (uncoveredNodes.length > 0) {
            console.log('[MODE3] Creating additional paths for', uncoveredNodes.length, 'uncovered nodes');
            // 为未覆盖的节点创建额外路径
            this.createAdditionalPathsForNodes(graph, uncoveredNodes);
        }
    }

    createAdditionalPathsForNodes(graph, uncoveredNodes) {
        const startNodes = graph.nodes.filter(n => n.type === 'start');
        const endNodes = graph.nodes.filter(n => n.type === 'end');

        uncoveredNodes.forEach(node => {
            const startNode = startNodes[Math.floor(Math.random() * startNodes.length)];
            const endNode = endNodes[Math.floor(Math.random() * endNodes.length)];

            // 创建连接确保节点在路径上
            // 这里可以添加具体的连接创建逻辑
            console.log('[MODE3] Creating path:', startNode.id, '->', node.id, '->', endNode.id);
        });
    }
}

// ========== 核心API导出 ==========

// 全局模式3引擎实例
const mode3Engine = new Mode3IncrementalSolvabilityEngine();

// 验证当前游戏状态的SYSTEM.md合规性
function validateCurrentGameState() {
    const allNodes = [...gameState.temporaryNodes, ...gameState.placedNodes];
    return systemMdValidator.validateAllConstraints(allNodes);
}

// 生成SYSTEM.md兼容的节点池
function generateSystemMdCompliantNodePool(wave, difficulty) {
    console.log('[CORE] Generating SYSTEM.md compliant node pool for wave', wave, 'difficulty', difficulty);

    // 基础节点配置
    const nodeConfig = {
        startNodes: Math.min(wave, 3),
        endNodes: Math.min(wave, 3),
        intermediateNodes: Math.max(2, wave * difficulty),
        maxDepth: Math.max(3, Math.ceil((wave * difficulty) / 3) + 1)
    };

    // 生成基础节点结构
    const nodePool = generateBaseNodeStructure(nodeConfig);

    // 生成路径骨架
    const pathSkeleton = pathSkeletonGenerator.generatePathSkeleton(nodePool);

    // 分配端口
    portAllocator.allocatePortsBasedOnSkeleton(nodePool, pathSkeleton);

    // 平衡端口
    const balanceResult = portBalancer.balancePortCounts(nodePool);

    if (!balanceResult.success) {
        throw new Error('Failed to balance ports for SYSTEM.md compliance');
    }

    // 最终验证
    const validation = systemMdValidator.validateAllConstraints(nodePool);
    if (!validation.allSatisfied) {
        throw new Error('Generated node pool does not satisfy SYSTEM.md constraints');
    }

    console.log('[CORE] Successfully generated', nodePool.length, 'SYSTEM.md compliant nodes');
    return nodePool;
}

// 生成基础节点结构
function generateBaseNodeStructure(config) {
    const nodes = [];
    let nodeIdCounter = 0;

    // 生成起点节点
    for (let i = 0; i < config.startNodes; i++) {
        nodes.push({
            id: `start_${nodeIdCounter++}`,
            type: 'start',
            depth: 0,
            inputPorts: [],
            outputPorts: [],
            label: `Start ${i + 1}`
        });
    }

    // 生成中间节点
    const depthLayers = config.maxDepth - 1;
    const nodesPerLayer = Math.ceil(config.intermediateNodes / depthLayers);

    for (let depth = 1; depth < config.maxDepth; depth++) {
        const nodesInThisLayer = Math.min(nodesPerLayer, config.intermediateNodes - (depth - 1) * nodesPerLayer);

        for (let i = 0; i < nodesInThisLayer; i++) {
            nodes.push({
                id: `intermediate_${nodeIdCounter++}`,
                type: 'intermediate',
                depth: depth,
                inputPorts: [],
                outputPorts: [],
                label: `Node ${depth}-${i + 1}`
            });
        }
    }

    // 生成终点节点
    for (let i = 0; i < config.endNodes; i++) {
        nodes.push({
            id: `end_${nodeIdCounter++}`,
            type: 'end',
            depth: config.maxDepth,
            inputPorts: [],
            outputPorts: [],
            label: `End ${i + 1}`
        });
    }

    return nodes;
}

// 模式3增量修改API
function applyMode3Modification(modification) {
    const currentGraph = {
        nodes: [...gameState.temporaryNodes, ...gameState.placedNodes],
        connections: gameState.connections
    };

    const result = mode3Engine.maintainStrongSolvabilityDuringModification(currentGraph, modification);

    // 更新游戏状态
    gameState.temporaryNodes = result.nodes.filter(n => n.area === 'temporary');
    gameState.placedNodes = result.nodes.filter(n => n.area !== 'temporary');
    gameState.connections = result.connections;

    return result;
}
